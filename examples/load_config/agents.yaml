# Example agents configuration for YAML-based loading
# Two types of variable substitution:
# 1. ${ENV_VAR} - from system environment variables
# 2. ${vars.KEY} - from the 'vars' section in this YAML file

vars:  # Internal variables (file-level)
  DEFAULT_TEMPERATURE: 0.1
  OPENAI_URL: https://api.openai.com/v1
  OPENROUTER_URL: https://openrouter.ai/api/v1

agents:
  researcher:
    system_prompt: "You specialize at researching."
    llm_config:
      llm_provider: openai
      llm_model_name: gpt-4o
      llm_api_key: ${OPENAI_API_KEY}  # from system env
      llm_base_url: ${vars.OPENAI_URL}  # from vars section
      llm_temperature: ${vars.DEFAULT_TEMPERATURE}  # from vars section

  summarizer:
    system_prompt: "You specialize at summarizing."
    llm_config:
      llm_provider: openai
      llm_model_name: gpt-5
      llm_api_key: ${OPENROUTER_API_KEY}  # from system env
      llm_base_url: ${vars.OPENROUTER_URL}  # from vars section
      llm_temperature: ${vars.DEFAULT_TEMPERATURE}  # from vars section


swarm:
  type: workflow
  order: [researcher, summarizer]

