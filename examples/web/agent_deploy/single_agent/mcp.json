{"mcpServers": {"google-pse-search": {"command": "npx", "args": ["-y", "@adenot/mcp-google-search"], "env": {"GOOGLE_API_KEY": "${GOOGLE_API_KEY}", "GOOGLE_SEARCH_ENGINE_ID": "${GOOGLE_SEARCH_ENGINE_ID}"}}, "aworld-playwright": {"command": "npx", "args": ["playwright-mcp-aworld", "--isolated"], "env": {"OSS_ENDPOINT": "${OSS_ENDPOINT}", "OSS_ACCESS_KEY_ID": "${OSS_ACCESS_KEY_ID}", "OSS_ACCESS_KEY_SECRET": "${OSS_ACCESS_KEY_SECRET}", "OSS_BUCKET": "${OSS_BUCKET}", "PLAYWRIGHT_TIMEOUT": "120000", "SESSION_REQUEST_CONNECT_TIMEOUT": "120"}}, "fetch": {"command": "uvx", "args": ["-i", "https://mirrors.aliyun.com/pypi/simple/", "mcp-server-fetch", "--ignore-robots-txt"]}, "time": {"command": "uvx", "args": ["mcp-server-time", "--local-timezone", "Asia/Shanghai"]}}}