{"task_id": "imo1", "Question": "A line in the plane is called sunny if it is not parallel to any of the x–axis, the y–axis, or the line x + y = 0. Let n ≥ 3 be a given integer. Determine all nonnegative integers k such that there exist n distinct lines in the plane satisfying both of the following: 1. for all positive integers a and b with a + b ≤ n + 1, the point (a, b) lies on at least one of the lines; and 2. exactly k of the n lines are sunny."}
{"task_id": "imo2", "Question": "Let Ω and Γ be circles with centres M and N, respectively, such that the radius of Ω is less than the radius of Γ. Suppose Ω and Γ intersect at two distinct points A and B. Line MN intersects Ω at C and Γ at D, so that C, M, N, D lie on MN in that order. Let P be the circumcenter of triangle ACD. Line AP meets Ω again at E != A and meets Γ again at F != A. Let H be the orthocenter of triangle PMN. Prove that the line through H parallel to AP is tangent to the circumcircle of triangle BEF."}
{"task_id": "imo3", "Question": "A function f: N → N is said to be bonza if f(a) divides b^a − f(b)^f(a) for all positive integers a and b. Determine the smallest real constant c such that f(n) ≤ cn for all bonza functions f and all positive integers n."}
{"task_id": "imo4", "Question": "An infinite sequence a_1, a_2, . . . consists of positive integers has each of which has at least three proper divisors. Suppose that for each n ≥ 1, a_n+1 is the sum of the three largest proper divisors of an. Determine all possible values of a_1."}
{"task_id": "imo5", "Question": "Alice and Bazza are playing the inekoalaty game, a two‑player game whose rules depend on a positive real number λ which is known to both players. On the nth turn of the game (starting with n = 1) the following happens:• If n is odd, Alice chooses a nonnegative real number xn such that x_1 + x_2 + ... + x_n ≤ λn. • If n is even, Bazza chooses a nonnegative real number xn such that (x_1)^2 + (x_2)^2 + ...+ (x_n)^2 ≤ n. If a player cannot choose a suitable x_n, the game ends and the other player wins. If the game goes on forever, neither player wins. All chosen numbers are known to both players. Determine all values of λ for which Alice has a winning strategy and all those for which Bazza has a winning strategy."}
{"task_id": "imo6", "Question": "Consider a 2025 × 2025 grid of unit squares. Matilda wishes to place on the grid some rectangular tiles, possibly of different sizes, such that each side of every tile lies on a grid line and every unit square is covered by at most one tile. Determine the minimum number of tiles Matilda needs to place so that each row and each column of the grid has exactly one unit square that is not covered by any tile."}
{"task_id": "test", "Question": "1+1=？"}