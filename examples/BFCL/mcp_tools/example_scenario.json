{"root": {"workspace": {"type": "directory", "contents": {"src": {"type": "directory", "contents": {"main.py": {"type": "file", "content": "#!/usr/bin/env python3\n\ndef main():\n    print('Hello from GorillaFileSystem!')\n    return 0\n\nif __name__ == '__main__':\n    exit(main())"}, "utils.py": {"type": "file", "content": "# Utility functions\n\ndef format_bytes(bytes_value):\n    \"\"\"Format bytes into human readable format.\"\"\"\n    for unit in ['B', 'KB', 'MB', 'GB']:\n        if bytes_value < 1024.0:\n            return f\"{bytes_value:.1f} {unit}\"\n        bytes_value /= 1024.0\n    return f\"{bytes_value:.1f} TB\"\n\ndef count_lines(text):\n    \"\"\"Count lines in text.\"\"\"\n    return len(text.splitlines())"}, "config": {"type": "directory", "contents": {"settings.json": {"type": "file", "content": "{\n  \"debug\": true,\n  \"log_level\": \"INFO\",\n  \"max_file_size\": 1048576,\n  \"allowed_extensions\": [\".py\", \".txt\", \".md\", \".json\"]\n}"}, "database.conf": {"type": "file", "content": "# Database configuration\nhost=localhost\nport=5432\ndatabase=testdb\nusername=user\npassword=secret"}}}}}, "docs": {"type": "directory", "contents": {"README.md": {"type": "file", "content": "# Project Documentation\n\nThis is a sample project demonstrating file system operations.\n\n## Features\n\n- File creation and manipulation\n- Directory navigation\n- Text processing\n- Configuration management\n\n## Usage\n\nUse the various file system tools to explore and modify the project structure."}, "API.md": {"type": "file", "content": "# API Documentation\n\n## File Operations\n\n### Creating Files\n- `touch filename.ext` - Create empty file\n- `echo \"content\" filename.ext` - Create file with content\n\n### Reading Files\n- `cat filename.ext` - Display file contents\n- `tail filename.ext` - Show last 10 lines\n\n### File Information\n- `wc filename.ext` - Count lines, words, characters\n- `du` - Show disk usage"}}}, "tests": {"type": "directory", "contents": {"test_main.py": {"type": "file", "content": "import unittest\nfrom src.main import main\nfrom src.utils import format_bytes, count_lines\n\nclass TestMain(unittest.TestCase):\n    def test_main_returns_zero(self):\n        self.assertEqual(main(), 0)\n    \n    def test_format_bytes(self):\n        self.assertEqual(format_bytes(1024), \"1.0 KB\")\n        self.assertEqual(format_bytes(1048576), \"1.0 MB\")\n    \n    def test_count_lines(self):\n        self.assertEqual(count_lines(\"line1\\nline2\\nline3\"), 3)\n\nif __name__ == '__main__':\n    unittest.main()"}}}, "data": {"type": "directory", "contents": {"sample.csv": {"type": "file", "content": "id,name,email,age\n1,<PERSON>,<EMAIL>,28\n2,<PERSON>,<EMAIL>,34\n3,<PERSON>,<EMAIL>,29\n4,<PERSON>,<EMAIL>,42\n5,<PERSON>,<EMAIL>,31"}, "log.txt": {"type": "file", "content": "2024-01-01 10:00:00 INFO Application started\n2024-01-01 10:00:01 INFO Loading configuration\n2024-01-01 10:00:02 INFO Database connection established\n2024-01-01 10:00:03 INFO Server listening on port 8080\n2024-01-01 10:00:04 ERROR Failed to connect to external API\n2024-01-01 10:00:05 WARN Retrying API connection\n2024-01-01 10:00:06 INFO API connection restored\n2024-01-01 10:00:07 INFO Application ready"}}}, "temp": {"type": "directory", "contents": {}}}}}}