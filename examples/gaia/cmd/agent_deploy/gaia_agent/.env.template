# LLM Model Config
# LLM_PROVIDER = {YOUR_CONFIG}
LLM_MODEL_NAME = {YOUR_CONFIG}
LLM_API_KEY = {YOUR_CONFIG}
LLM_BASE_URL = {YOUR_CONFIG}
LLM_TEMPERATURE = 0.0

# ===============Path Configurations=================
# GAIA_DATASET_PATH="/path/to/your/gaia-benchmark/GAIA/2023"
AWORLD_WORKSPACE="/tmp"

# ===============MCP Server Configurations=================
# [Google Search API](https://developers.google.com/custom-search/v1/introduction)
GOOGLE_API_KEY={YOUR_CONFIG}
GOOGLE_CSE_ID={YOUR_CONFIG}

# [Browser Use](https://github.com/browser-use/browser-use)
SKIP_LLM_API_KEY_VERIFICATION=true
OPENAI_API_KEY={YOUR_CONFIG}
COOKIES_FILE_PATH={YOUR_CONFIG}

# Audio Server
AUDIO_LLM_API_KEY={YOUR_CONFIG}
AUDIO_LLM_BASE_URL=https://api.zhizengzeng.com/v1
AUDIO_LLM_MODEL_NAME=gpt-4o-transcribe

# Image Server
IMAGE_LLM_API_KEY={YOUR_CONFIG}
IMAGE_LLM_BASE_URL=https://openrouter.ai/api/v1
IMAGE_LLM_MODEL_NAME=anthropic/claude-3.7-sonnet

# Video Server
VIDEO_LLM_API_KEY={YOUR_CONFIG}
VIDEO_LLM_BASE_URL=https://openrouter.ai/api/v1
VIDEO_LLM_MODEL_NAME=gpt-4o
VIDEO_LLM_TEMPERATURE=1.0

# Code Server
CODE_LLM_API_KEY={YOUR_CONFIG}
CODE_LLM_BASE_URL=https://openrouter.ai/api/v1
CODE_LLM_MODEL_NAME=anthropic/claude-sonnet-4

# Think Server
THINK_LLM_API_KEY={YOUR_CONFIG}
THINK_LLM_BASE_URL=https://openrouter.ai/api/v1
THINK_LLM_MODEL_NAME=deepseek/deepseek-r1-0528:free

# Guard Server
GUARD_LLM_API_KEY={YOUR_CONFIG}
GUARD_LLM_BASE_URL="https://openrouter.ai/api/v1"
GUARD_LLM_MODEL_NAME="google/gemini-2.5-pro-preview"

# [E2B Server](https://e2b.dev/docs/quickstart)
E2B_API_KEY={YOUR_CONFIG}
