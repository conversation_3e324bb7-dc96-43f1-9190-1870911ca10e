{"mcpServers": {"audio": {"command": "python", "args": ["-m", "examples.gaia.mcp_collections.media.audio"], "env": {}, "client_session_timeout_seconds": 9999.0}, "browser": {"command": "python", "args": ["-m", "examples.gaia.mcp_collections.tools.browser"], "env": {"LLM_MODEL_NAME": "${LLM_MODEL_NAME}", "LLM_API_KEY": "${LLM_API_KEY}", "LLM_BASE_URL": "${LLM_BASE_URL}"}, "client_session_timeout_seconds": 9999.0}, "chess": {"command": "python", "args": ["-m", "examples.gaia.mcp_collections.tools.playchess"], "env": {}, "client_session_timeout_seconds": 9999.0}, "code": {"command": "npx", "args": ["-y", "@e2b/mcp-server"], "env": {"E2B_API_KEY": "${E2B_API_KEY}"}}, "csv": {"command": "python", "args": ["-m", "examples.gaia.mcp_collections.documents.mscsv"], "env": {}, "client_session_timeout_seconds": 9999.0}, "docx": {"command": "python", "args": ["-m", "examples.gaia.mcp_collections.documents.msdocx"], "env": {}, "client_session_timeout_seconds": 9999.0}, "download": {"command": "python", "args": ["-m", "examples.gaia.mcp_collections.tools.download"], "env": {}, "client_session_timeout_seconds": 9999.0}, "xlsx": {"command": "python", "args": ["-m", "examples.gaia.mcp_collections.documents.msxlsx"], "env": {}, "client_session_timeout_seconds": 9999.0}, "image": {"command": "python", "args": ["-m", "examples.gaia.mcp_collections.media.image"], "env": {}, "client_session_timeout_seconds": 9999.0}, "pdf": {"command": "python", "args": ["-m", "examples.gaia.mcp_collections.documents.pdf"], "env": {}, "client_session_timeout_seconds": 9999.0}, "pptx": {"command": "python", "args": ["-m", "examples.gaia.mcp_collections.documents.mspptx"], "env": {}, "client_session_timeout_seconds": 9999.0}, "pubchem": {"command": "python", "args": ["-m", "examples.gaia.mcp_collections.tools.pubchem"], "env": {}, "client_session_timeout_seconds": 9999.0}, "reasoning": {"command": "python", "args": ["-m", "examples.gaia.mcp_collections.intelligence.think"], "env": {}, "client_session_timeout_seconds": 9999.0}, "search": {"command": "python", "args": ["-m", "examples.gaia.mcp_collections.tools.search"], "env": {"GOOGLE_API_KEY": "${GOOGLE_API_KEY}", "GOOGLE_CSE_ID": "${GOOGLE_CSE_ID}"}, "client_session_timeout_seconds": 9999.0}, "terminal": {"command": "python", "args": ["-m", "examples.gaia.mcp_collections.tools.terminal"]}, "video": {"command": "python", "args": ["-m", "examples.gaia.mcp_collections.media.video"], "env": {}, "client_session_timeout_seconds": 9999.0}, "wayback": {"command": "python", "args": ["-m", "examples.gaia.mcp_collections.tools.wayback"], "env": {}, "client_session_timeout_seconds": 9999.0}, "wikipedia": {"command": "python", "args": ["-m", "examples.gaia.mcp_collections.tools.wiki"], "env": {}, "client_session_timeout_seconds": 9999.0}, "youtube": {"command": "python", "args": ["-m", "examples.gaia.mcp_collections.tools.youtube"], "env": {}, "client_session_timeout_seconds": 9999.0}, "txt": {"command": "python", "args": ["-m", "examples.gaia.mcp_collections.documents.txt"], "env": {}, "client_session_timeout_seconds": 9999.0}}}