name: aworld-gaia
channels:
  - defaults
dependencies:
  - bzip2=1.0.8
  - ca-certificates=2025.2.25
  - expat=2.7.1
  - libcxx=17.0.6
  - libffi=3.4.4
  - ncurses=6.4
  - openssl=3.0.16
  - pip=25.1
  - python=3.12.11
  - readline=8.2
  - setuptools=78.1.1
  - sqlite=3.45.3
  - tk=8.6.14
  - wheel=0.45.1
  - xz=5.6.4
  - zlib=1.2.13
  - pip:
      - aiofiles==24.1.0
      - altair==5.5.0
      - annotated-types==0.7.0
      - anthropic
      - anyio==4.9.0
      - arxiv==2.2.0
      - asttokens==3.0.0
      - attrs==25.3.0
      - Authlib==1.6.0
      - backoff==2.2.1
      - beautifulsoup4==4.13.4
      - blinker==1.9.0
      - Brotli==1.1.0
      - browser-use==0.3.2
      - bubus==1.2.1
      - cachetools==5.5.2
      - certifi==2025.6.15
      - cffi==1.17.1
      - cfgv==3.4.0
      - chardet==5.2.0
      - charset-normalizer==3.4.2
      - chess==1.11.2
      - click==8.2.1
      - cobble==0.1.4
      - cryptography==45.0.4
      - cssselect2==0.8.0
      - Cython==3.1.2
      - decorator==5.2.1
      - defusedxml==0.7.1
      - distlib==0.3.9
      - distro==1.9.0
      - dotenv==0.9.9
      - durationpy==0.10
      - EbookLib==0.18
      - einops==0.8.1
      - et_xmlfile==2.0.0
      - executing==2.2.0
      - faiss-cpu==1.11.0
      - feedparser==6.0.11
      - filelock==3.18.0
      - filetype==1.2.0
      - Flask==3.1.1
      - fonttools==4.58.4
      - fsspec==2025.5.1
      - ftfy==6.3.1
      - gitdb==4.0.12
      - GitPython==3.1.44
      - google-ai-generativelanguage==0.6.18
      - google-api-core==2.25.1
      - google-auth==2.40.3
      - google-genai==1.21.1
      - googleapis-common-protos==1.70.0
      - greenlet==3.2.3
      - grpcio==1.73.0
      - grpcio-status==1.73.0
      - h11==0.16.0
      - h2==4.2.0
      - hf-xet==1.1.5
      - hpack==4.1.0
      - httpcore==1.0.9
      - httpx==0.28.1
      - httpx-sse==0.4.1
      - huggingface-hub==0.33.0
      - hyperframe==6.1.0
      - identify==2.6.12
      - idna==3.10
      - ipython==9.3.0
      - ipython_pygments_lexers==1.1.1
      - itsdangerous==2.2.0
      - jedi==0.19.2
      - Jinja2==3.1.6
      - jiter==0.10.0
      - joblib==1.5.1
      - jsonpatch==1.33
      - jsonpickle==4.1.1
      - jsonpointer==3.0.0
      - jsonschema==4.24.0
      - jsonschema-specifications==2025.4.1
      - kubernetes==32.0.1
      - langchain==0.3.25
      - langchain-anthropic==0.3.15
      - langchain-core==0.3.64
      - langchain-deepseek==0.1.3
      - langchain-google-genai==2.1.5
      - langchain-ollama==0.3.3
      - langchain-openai==0.3.21
      - langchain-text-splitters==0.3.8
      - langsmith==0.3.45
      - lxml==5.4.0
      - mammoth==1.9.1
      - markdown
      # - marker-pdf
      - markdown-it-py==3.0.0
      - markdown2==2.5.3
      - markdownify==1.1.0
      - MarkupSafe==3.0.2
      - matplotlib-inline==0.1.7
      - mcp==1.6.0
      - mdurl==0.1.2
      - mem0ai==0.1.111
      - mpmath==1.3.0
      - narwhals==1.44.0
      - networkx==3.5
      - nodeenv==1.9.1
      - numpy==2.3.1
      - oauthlib==3.3.1
      - ollama==0.5.1
      - openai
      - opencv-python==4.11.0.86
      - opencv-python-headless==4.11.0.86
      - openpyxl==3.1.5
      - orjson==3.10.18
      - outcome==1.3.0.post0
      - packaging==24.2
      - pandas==2.3.0
      - parso==0.8.4
      - patchright==1.52.5
      - pdftext==0.6.3
      - pexpect==4.9.0
      - pillow==10.4.0
      - platformdirs==4.3.8
      - playwright==1.52.0
      - portalocker==2.10.1
      - posthog==5.4.0
      - prompt_toolkit==3.0.51
      - proto-plus==1.26.1
      - protobuf==6.31.1
      - psutil==7.0.0
      - ptyprocess==0.7.0
      - pure_eval==0.2.3
      - pyarrow==20.0.0
      - pyasn1==0.6.1
      - pyasn1_modules==0.4.2
      - pycparser==2.22
      - pydantic==2.11.5
      - pydantic-settings
      - pydantic_core
      - pyautogui
      - rubicon-objc
      - pydeck==0.9.1
      - pydyf==0.11.0
      - pyee==13.0.0
      - Pygments==2.19.2
      - pypdfium2==4.30.0
      - pyperclip==1.9.0
      - pyphen==0.17.2
      - PySocks==1.7.1
      - pytesseract==0.3.13
      - python-dateutil==2.9.0.post0
      - python-docx==1.2.0
      - python-dotenv==1.0.1
      - python-pptx==1.0.2
      - python-magic==0.4.27
      - pytz==2025.2
      - pyvis==0.3.2
      - PyYAML==6.0.2
      - qdrant-client==1.14.3
      - RapidFuzz==3.13.0
      - referencing==0.36.2
      - regex==2024.11.6
      - requests==2.32.4
      - requests-oauthlib==2.0.0
      - requests-toolbelt==1.0.0
      - rich==14.0.0
      - rpds-py==0.25.1
      - rsa==4.9.1
      - safetensors==0.5.3
      - scikit-learn==1.7.0
      - scipy==1.16.0
      - screeninfo==0.8.1
      - selenium==4.33.0
      - sentence-transformers==4.1.0
      - setuptools==78.1.1
      - sgmllib3k==1.0.0
      - shellingham==1.5.4
      - six==1.17.0
      - smmap==5.0.2
      - sniffio==1.3.1
      - sortedcontainers==2.4.0
      - soupsieve==2.7
      - SQLAlchemy==2.0.41
      - sse-starlette==2.3.6
      - stack-data==0.6.3
      - starlette==0.47.1
      - streamlit==1.46.0
      - surya-ocr==0.14.6
      - sympy==1.14.0
      - tabulate==0.9.0
      - tenacity==8.5.0
      - threadpoolctl==3.6.0
      - tiktoken==0.9.0
      - tinycss2==1.4.0
      - tinyhtml5==2.0.0
      - tokenizers==0.21.2
      - toml==0.10.2
      - torch==2.7.1
      - tornado==6.5.1
      - tqdm==4.67.1
      - traitlets==5.14.3
      - transformers==4.52.4
      - trio==0.30.0
      - trio-websocket==0.12.2
      - typer==0.16.0
      - typing-inspection==0.4.1
      - typing_extensions==4.13.2
      - tzdata==2025.2
      - urllib3==2.4.0
      - uuid7==0.1.0
      - uvicorn==0.34.3
      - virtualenv==20.31.2
      - waybackpy==3.0.6
      - wcwidth==0.2.13
      - weasyprint==63.1
      - webencodings==0.5.1
      - websocket-client==1.8.0
      - websockets==15.0.1
      - Werkzeug==3.1.3
      - wheel==0.45.1
      - wikipedia==1.4.0
      - wsproto==1.2.0
      - xlsxwriter==3.2.5
      - youtube-transcript-api==1.1.0
      - zopfli==0.2.3.post1
      - zstandard==0.23.0
      - httpx[socks]
      - ffmpeg-python==0.2.0
prefix: /opt/homebrew/Caskroom/miniconda/base/envs/aworld-gaia
