# Python specific
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
.pytest_cache/
htmlcov/
.coverage
.coverage.*
.cache
coverage.xml
*.cover

# Log files
*.log
git-pre-push-hook-warn.log
logs/
logging/

# Environment and virtual environments
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
.env
.env.docker

# IDE specific files
.idea/
.vscode/
.cursor/
*.swp
*.swo
.DS_Store
.ipynb_checkpoints

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Build artifacts
*.pyc
*.pyo
*.pyd

# Documentation
docs/_build/
docs/api/

# Data files (customize as needed)
*.csv
*.dat
*.out
data/raw/
data/processed/
data/workspaces/
gaia-benchmark/
examples/gaia/gaia_dataset/

# Compiled files
*.com
*.class
*.dll
*.exe
*.o
*.a

# Package files
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# Temporary files
*.tmp
*.temp
.*.swp
.*.swo
*.webm
*.mp4
*.mp3
*.avi
*.mov
*.flv
*.wmv
*.mkv
*.npy
*.xlsx
*.xls
*.docx
*.doc
*.pptx
*.ppt
*.pdf
*.wav
*.bak

# Node Modules
node_modules/

aworld.db
.data/
examples/gaia/GAIA
traces*.json
trace_data

examples/gaia/output
examples/**/workspaces
examples/**/data
examples/**/*.bak
=**