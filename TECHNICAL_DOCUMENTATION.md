# AWorld Framework Technical Documentation

## Table of Contents

1. [Overview](#overview)
2. [Architecture Design](#architecture-design)
3. [Core Components](#core-components)
4. [Multi-Agent Systems](#multi-agent-systems)
5. [Memory System](#memory-system)
6. [Tool System](#tool-system)
7. [Observability & Tracing](#observability--tracing)
8. [Configuration Management](#configuration-management)
9. [API Reference](#api-reference)
10. [Development Guidelines](#development-guidelines)

## Overview

**AWorld (Agent World)** is a next-generation framework engineered for agent self-improvement at scale. The framework enables AI agents to continuously evolve by synthesizing their own knowledge and experiences through sophisticated multi-agent systems, cloud-native scalability, and comprehensive observability.

### Key Characteristics

- **Multi-Agent Systems (MAS)**: Build complex, interacting agent societies using plug-and-play protocols
- **Intelligence Beyond a Single Model**: Generate high-quality feedback and diverse synthetic training data
- **Cloud-Native for Diversity & Scale**: High concurrency and scalability for training smarter agents
- **Self-Improvement**: Autonomous evolution through knowledge synthesis and experience accumulation

### Design Principles

1. **Modularity**: Each component is designed as an independent, reusable module
2. **Extensibility**: Plugin architecture allows easy integration of new tools, models, and capabilities
3. **Scalability**: Cloud-native design supports distributed execution and high concurrency
4. **Observability**: Comprehensive tracing and monitoring for debugging and optimization
5. **Flexibility**: Support for various agent topologies and execution patterns

## Architecture Design

The AWorld framework follows a layered architecture with clear separation of concerns:

### Architectural Layers

1. **Core Layer**: Fundamental abstractions (Agent, Task, Context, Swarm)
2. **Execution Layer**: Runtime management (Runners, Event handling)
3. **Tool Layer**: External integrations (Tools, MCP, Sandbox)
4. **Memory Layer**: Knowledge management (Memory, Vector DB, Long-term storage)
5. **Observability Layer**: Monitoring and tracing (Trace, Events, Metrics)
6. **Infrastructure Layer**: Configuration and utilities (Config, Factory, Utils)

### Component Relationships

The framework uses dependency injection and factory patterns to manage component relationships:

- **AgentFactory**: Manages agent registration and instantiation
- **ToolFactory**: Handles tool registration and execution
- **MemoryFactory**: Provides singleton access to memory systems
- **ActionFactory**: Manages action registration and execution

## Core Components

### Agent System

#### BaseAgent Class

The `BaseAgent` class serves as the foundation for all agent implementations in AWorld.

**Key Responsibilities:**
- Define agent lifecycle (initialization, execution, cleanup)
- Manage agent state and configuration
- Handle tool and agent interactions
- Support both synchronous and asynchronous execution

**Core Methods:**
```python
class BaseAgent(Generic[INPUT, OUTPUT]):
    def __init__(self, name: str, conf: Config, **kwargs)
    def policy(self, observation: INPUT, **kwargs) -> OUTPUT
    async def async_policy(self, observation: INPUT, **kwargs) -> OUTPUT
    def run(self, message: Message, **kwargs) -> Message
    async def async_run(self, message: Message, **kwargs) -> Message
    def reset(self, options: Dict[str, Any])
```

**Configuration Parameters:**
- `name`: Unique agent identifier
- `conf`: Agent configuration (LLM settings, tools, etc.)
- `tool_names`: Available tools for the agent
- `agent_names`: Other agents this agent can interact with
- `mcp_servers`: MCP server configurations
- `feedback_tool_result`: Whether to process tool results before continuing
- `wait_tool_result`: Whether to wait for tool completion

#### Agent Class (LLM Agent)

The `Agent` class extends `BaseAgent` with LLM-specific functionality.

**Key Features:**
- LLM model integration (OpenAI, Anthropic, local models)
- Tool calling and result processing
- Memory integration for context retention
- Prompt template management
- Event-driven execution

**Initialization Example:**
```python
from aworld.agents.llm_agent import Agent
from aworld.config.conf import AgentConfig

agent = Agent(
    name="Research Agent",
    conf=AgentConfig(
        llm_provider="openai",
        llm_model_name="gpt-4",
        llm_api_key="your-api-key",
        llm_temperature=0.7
    ),
    system_prompt="You are a research assistant specialized in data analysis.",
    tool_names=["web_search", "file_reader"],
    mcp_servers=["search_server"]
)
```

### Task System

#### Task Class

The `Task` class encapsulates a complete execution unit with all necessary context.

**Core Attributes:**
```python
@dataclass
class Task:
    id: str                    # Unique task identifier
    name: str                  # Human-readable task name
    input: Any                 # Task input data
    conf: Config               # Task configuration
    tools: List[Tool]          # Available tools
    agent: Optional[Agent]     # Single agent execution
    swarm: Optional[Swarm]     # Multi-agent execution
    context: Context           # Execution context
    outputs: Outputs           # Output management
```

**Lifecycle:**
1. **Creation**: Task is created with input and configuration
2. **Initialization**: Agent/Swarm and tools are prepared
3. **Execution**: Runner executes the task
4. **Completion**: Results are collected and returned

#### TaskResponse Class

Encapsulates the results of task execution:

```python
@dataclass
class TaskResponse:
    id: str                           # Task identifier
    answer: str                       # Final response
    context: Context                  # Updated context
    usage: Dict[str, Any]            # Resource usage statistics
    time_cost: float                 # Execution time
    success: bool                    # Success status
    trajectory: List[Dict[str, Any]] # Execution trace
```

### Context System

#### Context Class

The `Context` class is the central state management system in AWorld.

**Core Responsibilities:**
- **State Restoration**: Save and restore complete agent state
- **Configuration Management**: Store immutable agent configuration
- **Runtime State Tracking**: Manage mutable execution state
- **LLM Prompt Management**: Handle prompt context and optimization
- **Multi-task State Management**: Support task forking and merging

**Key Features:**
```python
class Context:
    # Immutable configuration fields
    agent_id: str
    agent_name: str
    system_prompt: str
    tool_names: List[str]
    
    # Mutable runtime fields
    messages: List[Dict]
    step: int
    context_usage: ContextUsage
    trajectories: OrderedDict
```

**Context Lifecycle:**
1. **Creation**: Initialize with agent configuration
2. **Runtime**: Continuously update during execution
3. **Fork/Merge**: Support parallel task execution
4. **Destruction**: Cleanup when agent completes

### Runner System

#### TaskRunner Class

The `TaskRunner` class manages task execution lifecycle.

**Execution Flow:**
1. **Pre-run**: Initialize context and prepare environment
2. **Main execution**: Run agent policy and handle results
3. **Post-run**: Cleanup and finalize results

**Runner Types:**
- **EventRunner**: Event-driven execution with message passing
- **CallDrivenRunner**: Direct function call execution
- **StreamingRunner**: Real-time streaming output

## Multi-Agent Systems

### Swarm Architecture

The `Swarm` class enables sophisticated multi-agent coordination through various topology patterns.

#### Topology Types

1. **Workflow Topology**: Sequential agent execution
2. **Team Topology**: Leader-executor pattern with centralized coordination
3. **Handoff Topology**: Peer-to-peer agent collaboration
4. **Hierarchical Topology**: Tree-structured agent organization

#### Swarm Configuration

```python
from aworld.core.agent.swarm import Swarm, TeamSwarm, HandoffSwarm

# Team topology (Leader-Executor)
leader = Agent(name="coordinator", conf=config)
worker1 = Agent(name="worker1", conf=config)
worker2 = Agent(name="worker2", conf=config)
team = TeamSwarm(leader, worker1, worker2)

# Handoff topology (Peer-to-Peer)
agent_a = Agent(name="agent_a", conf=config)
agent_b = Agent(name="agent_b", conf=config)
handoff = HandoffSwarm((agent_a, agent_b), (agent_b, agent_a))
```

#### Agent Graph

The `AgentGraph` class represents the topology structure:

```python
class AgentGraph:
    agents: Dict[str, BaseAgent]      # Agent registry
    edges: Dict[str, List[str]]       # Connection graph
    ordered_agents: List[BaseAgent]   # Execution order
```

### Communication Patterns

#### Message-Based Communication

Agents communicate through structured messages:

```python
class Message:
    category: str        # Message type (TASK, TOOL, AGENT, OUTPUT)
    payload: Any         # Message content
    sender: str          # Sender agent ID
    receiver: str        # Receiver agent ID
    session_id: str      # Session identifier
    context: Context     # Execution context
```

#### Event-Driven Architecture

The framework supports event-driven communication through the EventManager:

```python
class EventManager:
    async def emit(self, data: Any, sender: str, receiver: str = None)
    async def consume(self, nowait: bool = False)
    async def register(self, event_type: str, topic: str, handler: Callable)
```

## Memory System

### Memory Architecture

The memory system provides both short-term and long-term memory capabilities with vector database integration.

#### MemoryFactory

Singleton factory for memory system initialization:

```python
class MemoryFactory:
    @classmethod
    def init(cls, config: MemoryConfig)
    
    @classmethod
    def instance(cls) -> MemoryBase
```

#### Memory Configuration

```python
class MemoryConfig:
    provider: str                              # Memory provider (aworld, mem0)
    llm_config: MemoryLLMConfig               # LLM configuration
    embedding_config: EmbeddingsConfig        # Embedding model config
    vector_store_config: VectorDBConfig       # Vector database config
```

#### Memory Operations

```python
class MemoryBase:
    async def add(self, memory_item: MemoryItem)
    async def search(self, query: str, filters: dict = None)
    async def get_all(self, filters: dict = None)
    async def update(self, memory_item: MemoryItem)
    async def delete(self, memory_id: str)
```

### Vector Database Integration

Support for multiple vector database providers:

- **ChromaDB**: Local and cloud vector storage
- **Pinecone**: Cloud-native vector database
- **Weaviate**: Open-source vector search engine

### Embedding Providers

- **OpenAI**: text-embedding-3-small/large models
- **Ollama**: Local embedding models
- **HuggingFace**: Transformer-based embeddings

## Tool System

### Tool Architecture

The tool system provides a flexible framework for agent-environment interaction.

#### BaseTool Class

```python
class BaseTool(Generic[AgentInput, ToolInput]):
    def __init__(self, conf: ToolConfig, **kwargs)
    def step(self, message: Message, **kwargs) -> Message
    async def async_step(self, message: Message, **kwargs) -> Message
    def reset(self, seed: int = None, options: Dict = None)
```

#### Tool Registration

Tools are registered using the ToolFactory:

```python
@ToolFactory.register(name="example_tool", desc="Example tool description")
class ExampleTool(BaseTool):
    def do_step(self, action: ActionModel, **kwargs):
        # Tool implementation
        return observation, reward, terminated, truncated, info
```

### MCP Integration

Model Context Protocol (MCP) integration enables seamless tool connectivity:

#### MCP Server Configuration

```python
mcp_config = {
    "mcpServers": {
        "file_system": {
            "type": "stdio",
            "command": "python",
            "args": ["path/to/mcp_server.py"]
        },
        "web_search": {
            "type": "sse",
            "url": "https://api.example.com/mcp",
            "timeout": 30
        }
    }
}
```

#### MCP Tool Execution

```python
class MCPToolExecutor:
    async def execute_action(self, actions: List[ActionModel])
    async def get_available_tools(self, server_name: str)
    async def close(self, keys: List[str] = [])
```

### Action System

#### ActionModel

Represents a single action to be executed:

```python
class ActionModel:
    tool_name: str           # Target tool name
    action_name: str         # Specific action
    params: Dict[str, Any]   # Action parameters
    agent_name: str          # Executing agent
    tool_call_id: str        # Unique call identifier
```

#### ActionFactory

Manages action registration and instantiation:

```python
@ActionFactory.register(name="search", desc="Web search action", tool_name="web_tool")
class SearchAction(ExecutableAction):
    async def async_act(self, **kwargs) -> ActionResult:
        # Action implementation
        return ActionResult(content=result, success=True)
```

## Observability & Tracing

### Trace System

AWorld implements comprehensive distributed tracing using OpenTelemetry standards.

#### TraceProvider

```python
class TraceProvider:
    def get_tracer(self, name: str, version: str = None) -> Tracer
    def start_span(self, name: str, **kwargs) -> Span
    def start_as_current_span(self, name: str, **kwargs) -> Iterator[Span]
```

#### Span Management

```python
import aworld.trace as trace

# Create a traced function
@trace.trace_function
async def my_function():
    with trace.start_as_current_span("operation") as span:
        span.set_attribute("key", "value")
        # Function implementation
```

#### Trace Configuration

```python
from aworld.trace import trace_configure

trace_configure(
    provider="otlp",
    backends=["logfire"],
    base_url="https://api.logfire.dev",
    write_token="your-token"
)
```

### Event System

#### EventManager

Manages event-driven communication between components:

```python
class EventManager:
    async def emit(self, data: Any, sender: str, receiver: str = None)
    async def consume(self, nowait: bool = False)
    async def register(self, event_type: str, topic: str, handler: Callable)
    async def unregister(self, event_type: str, topic: str, handler: Callable)
```

#### Event Types

- **TASK**: Task-related events
- **TOOL**: Tool execution events
- **AGENT**: Agent communication events
- **OUTPUT**: Output and result events

## Configuration Management

### Configuration Hierarchy

AWorld uses a hierarchical configuration system:

1. **Environment Variables**: Runtime configuration
2. **YAML Files**: Static configuration
3. **Code Configuration**: Programmatic setup
4. **Default Values**: Fallback configuration

### Core Configuration Classes

#### AgentConfig

```python
class AgentConfig(BaseConfig):
    llm_provider: str = "openai"
    llm_model_name: str = None
    llm_api_key: str = None
    llm_base_url: str = None
    llm_temperature: float = 0.7
    max_retries: int = 3
    max_model_len: int = 128000
```

#### TaskConfig

```python
class TaskConfig(BaseConfig):
    max_steps: int = 10
    timeout: float = 300.0
    enable_trace: bool = True
    enable_memory: bool = True
```

#### MemoryConfig

```python
class MemoryConfig(BaseConfig):
    provider: str = "aworld"
    llm_config: MemoryLLMConfig
    embedding_config: EmbeddingsConfig
    vector_store_config: VectorDBConfig
```

### Environment Variables

Key environment variables for configuration:

```bash
# LLM Configuration
export LLM_MODEL_NAME="gpt-4"
export LLM_API_KEY="your-api-key"
export LLM_BASE_URL="https://api.openai.com/v1"
export LLM_TEMPERATURE="0.7"

# Tracing Configuration
export LOGFIRE_WRITE_TOKEN="your-token"
export START_TRACE_SERVER="true"
export TRACE_SERVER_PORT="7079"

# Memory Configuration
export MEMORY_STORE_POSTGRES_DSN="postgresql://user:pass@host:port/db"
```

## API Reference

### Runners API

The `Runners` class provides the main entry points for task execution:

#### Synchronous Execution

```python
from aworld.runner import Runners

# Simple agent execution
result = Runners.sync_run(
    input="Your query here",
    agent=agent,
    tool_names=["web_search"],
    session_id="session_123"
)

# Task-based execution
task = Task(input="Query", agent=agent, conf=TaskConfig())
result = Runners.sync_run_task(task)
```

#### Asynchronous Execution

```python
# Async agent execution
result = await Runners.run(
    input="Your query here",
    agent=agent,
    swarm=swarm
)

# Streaming execution
streaming_output = Runners.streamed_run_task(task)
async for event in streaming_output.stream_events():
    print(event)
```

### Agent API

#### Agent Creation

```python
from aworld.agents.llm_agent import Agent

agent = Agent(
    name="Assistant",
    conf=agent_config,
    system_prompt="You are a helpful assistant",
    tool_names=["calculator", "web_search"],
    mcp_servers=["file_server"],
    feedback_tool_result=True
)
```

#### Agent Execution

```python
# Direct policy execution
observation = Observation(content="User input")
actions = await agent.async_policy(observation)

# Message-based execution
message = Message(payload=observation, context=context)
result = await agent.async_run(message)
```

### Swarm API

#### Swarm Creation

```python
from aworld.core.agent.swarm import Swarm, TeamSwarm, HandoffSwarm

# Workflow swarm
workflow = Swarm(agent1, agent2, agent3)

# Team swarm (leader-executor)
team = TeamSwarm(leader, executor1, executor2)

# Handoff swarm (peer-to-peer)
handoff = HandoffSwarm((agent1, agent2), (agent2, agent3))
```

#### Swarm Execution

```python
# Initialize swarm
swarm.init(content="Task description", context=context)

# Execute swarm
result = await swarm.async_run(message)
```

### Memory API

#### Memory Initialization

```python
from aworld.memory.main import MemoryFactory
from aworld.core.memory import MemoryConfig

MemoryFactory.init(config=MemoryConfig(
    provider="aworld",
    llm_config=llm_config,
    embedding_config=embedding_config,
    vector_store_config=vector_config
))

memory = MemoryFactory.instance()
```

#### Memory Operations

```python
from aworld.core.memory import MemoryItem

# Add memory
memory_item = MemoryItem(
    content="Important information",
    metadata={"user_id": "user123", "session_id": "session456"}
)
await memory.add(memory_item)

# Search memory
results = await memory.search(
    query="search query",
    filters={"user_id": "user123"},
    limit=10
)

# Get conversation history
history = await memory.get_all(filters={"session_id": "session456"})
```

### Tool API

#### Tool Registration

```python
from aworld.core.tool.base import ToolFactory, AsyncTool

@ToolFactory.register(name="custom_tool", desc="Custom tool description")
class CustomTool(AsyncTool):
    async def do_step(self, actions: List[ActionModel], **kwargs):
        # Tool implementation
        results = []
        for action in actions:
            result = await self.process_action(action)
            results.append(result)
        
        observation = Observation(content="Tool result")
        return observation, 0.0, False, False, {"results": results}
```

#### Action Registration

```python
from aworld.core.tool.action import ExecutableAction
from aworld.core.tool.action_factory import ActionFactory

@ActionFactory.register(name="search", desc="Search action", tool_name="web_tool")
class SearchAction(ExecutableAction):
    async def async_act(self, query: str, **kwargs) -> ActionResult:
        # Perform search
        results = await self.search_engine.search(query)
        return ActionResult(
            content=results,
            success=True,
            metadata={"query": query}
        )
```

## Development Guidelines

### Code Organization

1. **Module Structure**: Follow the established module hierarchy
2. **Naming Conventions**: Use descriptive names following Python conventions
3. **Documentation**: Include comprehensive docstrings and type hints
4. **Error Handling**: Implement robust error handling and logging

### Testing Strategy

1. **Unit Tests**: Test individual components in isolation
2. **Integration Tests**: Test component interactions
3. **End-to-End Tests**: Test complete workflows
4. **Performance Tests**: Validate scalability and performance

### Extension Points

#### Custom Agents

```python
class CustomAgent(BaseAgent):
    async def async_policy(self, observation, **kwargs):
        # Custom agent logic
        return actions
```

#### Custom Tools

```python
@ToolFactory.register(name="my_tool")
class MyTool(AsyncTool):
    async def do_step(self, actions, **kwargs):
        # Custom tool logic
        return observation, reward, done, truncated, info
```

#### Custom Memory Providers

```python
class CustomMemoryStore(MemoryStore):
    def add(self, memory_item: MemoryItem):
        # Custom storage logic
        pass
```

### Performance Optimization

1. **Async/Await**: Use asynchronous programming for I/O operations
2. **Connection Pooling**: Reuse database and API connections
3. **Caching**: Cache frequently accessed data
4. **Batch Processing**: Process multiple items together when possible

### Security Considerations

1. **API Key Management**: Use environment variables for sensitive data
2. **Input Validation**: Validate all user inputs
3. **Sandbox Isolation**: Use sandboxes for untrusted code execution
4. **Access Control**: Implement proper authentication and authorization

## Detailed Component Analysis

### Agent Implementation Details

#### LLM Integration

The Agent class integrates with various LLM providers through a unified interface:

<augment_code_snippet path="aworld/agents/llm_agent.py" mode="EXCERPT">
````python
class Agent(BaseAgent[Observation, List[ActionModel]]):
    """Basic agent for unified protocol within the framework."""

    async def _call_llm_model(self, observation: Observation,
                              messages: List[Dict[str, str]] = [],
                              info: Dict[str, Any] = {}, **kwargs) -> ModelResponse:
        """Perform LLM call with comprehensive error handling and tracing."""
````
</augment_code_snippet>

**LLM Call Process:**
1. **Prompt Processing**: Context optimization and compression
2. **Model Invocation**: Async call to LLM provider
3. **Response Parsing**: Extract content and tool calls
4. **Error Handling**: Retry logic and fallback mechanisms

#### Tool Calling Mechanism

The agent supports both native tool calling and prompt-based tool usage:

<augment_code_snippet path="aworld/agents/llm_agent.py" mode="EXCERPT">
````python
class LlmOutputParser(ModelOutputParser[ModelResponse, AgentResult]):
    async def parse(self, resp: ModelResponse, **kwargs) -> AgentResult:
        """Standard parse based OpenAI API."""
        results = []
        is_call_tool = False

        if resp.tool_calls:
            is_call_tool = True
            for tool_call in resp.tool_calls:
                # Parse tool calls and create ActionModel instances
````
</augment_code_snippet>

### Context Management Deep Dive

#### Context State Tracking

The Context class maintains both immutable configuration and mutable runtime state:

<augment_code_snippet path="aworld/core/context/base.py" mode="EXCERPT">
````python
class Context:
    """Context is the core context management class in the AWorld architecture.

    Provides:
    1. State Restoration: Save all state information during Agent execution
    2. Configuration Management: Store Agent's immutable configuration
    3. Runtime State Tracking: Manage Agent's mutable state during execution
    4. LLM Prompt Management: Manage complete prompt context for LLM calls
    5. Multi-task State Management: Support fork_new_task and context merging
    """
````
</augment_code_snippet>

#### Context Lifecycle Management

**Creation Phase:**
- Initialize with agent configuration
- Setup session and trace IDs
- Prepare context state

**Runtime Phase:**
- Update messages and step counters
- Track token usage and performance metrics
- Manage tool execution results

**Cleanup Phase:**
- Finalize execution results
- Merge child contexts if applicable
- Persist important state information

### Memory System Implementation

#### Memory Store Abstraction

<augment_code_snippet path="aworld/core/memory.py" mode="EXCERPT">
````python
class MemoryStore(ABC):
    """Memory store interface for messages history"""

    @abstractmethod
    def add(self, memory_item: MemoryItem):
        pass

    @abstractmethod
    def get_all(self, filters: dict = None) -> list[MemoryItem]:
        pass
````
</augment_code_snippet>

#### Vector Database Integration

The memory system integrates with vector databases for semantic search:

<augment_code_snippet path="aworld/memory/vector/dbs/chroma.py" mode="EXCERPT">
````python
class ChromaVectorDB(VectorDB):
    def search(self, collection_name: str, vectors: list[list[float]],
               limit: int = 10, filter: dict = None):
        """Perform semantic search using vector embeddings."""
        collection = self.client.get_collection(name=collection_name)
        result = collection.query(
            query_embeddings=vectors,
            where=where_filter,
            n_results=limit
        )
````
</augment_code_snippet>

### Tool System Architecture

#### Tool Factory Pattern

<augment_code_snippet path="aworld/core/tool/base.py" mode="EXCERPT">
````python
class ToolFactory(Factory):
    """Factory for tool registration and instantiation."""

    def register(self, name: str, desc: str, **kwargs):
        """Register a tool class with the factory."""
        def decorator(cls):
            self._cls[name] = cls
            self._desc[name] = desc
            return cls
        return decorator
````
</augment_code_snippet>

#### MCP Protocol Implementation

Model Context Protocol integration enables seamless tool connectivity:

<augment_code_snippet path="aworld/tools/mcp_tool/executor.py" mode="EXCERPT">
````python
class MCPToolExecutor:
    """Executor for MCP (Model Context Protocol) tools."""

    async def execute_action(self, actions: List[ActionModel], **kwargs):
        """Execute MCP tool actions with proper error handling."""
        action_results = []
        for action in actions:
            result = await server.call_tool(action.action_name, action.params)
            action_results.append(self._process_result(result))
````
</augment_code_snippet>

### Swarm Coordination Patterns

#### Team Builder Implementation

<augment_code_snippet path="aworld/core/agent/swarm.py" mode="EXCERPT">
````python
class TeamBuilder(TopologyBuilder):
    """Team mechanism requires a leadership agent, and other agents follow its command."""

    def build(self):
        """Build team topology with leader-executor pattern."""
        agent_graph = AgentGraph(GraphBuildType.TEAM.value)
        root_agent = self.agent_list[0]  # First agent is leader

        # Setup leader-executor relationships
        for executor in self.agent_list[1:]:
            agent_graph.add_edge(root_agent, executor)
            root_agent.handoffs.append(executor.id())
````
</augment_code_snippet>

#### Handoff Builder Implementation

<augment_code_snippet path="aworld/core/agent/swarm.py" mode="EXCERPT">
````python
class HandoffBuilder(TopologyBuilder):
    """Handoff mechanism using agents as tools while maintaining independent state."""

    def build(self):
        """Build handoff topology for peer-to-peer agent collaboration."""
        agent_graph = AgentGraph(GraphBuildType.HANDOFF.value)

        for pair in self.agent_list:
            # Create bidirectional relationships
            agent_graph.add_edge(pair[0], pair[1])
            pair[0].handoffs.append(pair[1].id())
````
</augment_code_snippet>

### Event-Driven Architecture

#### Event Bus Implementation

<augment_code_snippet path="aworld/events/manager.py" mode="EXCERPT">
````python
class EventManager:
    """Event manager builds event bus instance and stores recent messages."""

    async def emit_message(self, event: Message):
        """Send message to event bus with proper routing."""
        key = event.key()
        self.messages[key].append(event)
        await self.event_bus.publish(event)
````
</augment_code_snippet>

#### Message Types and Routing

The framework supports various message types for different communication patterns:

- **ToolMessage**: Tool execution requests and results
- **AgentMessage**: Inter-agent communication
- **GroupMessage**: Multi-agent coordination
- **OutputMessage**: Result streaming and logging

### Distributed System Support

#### Cloud-Native Features

AWorld supports distributed execution through:

1. **Horizontal Scaling**: Multiple agent instances across nodes
2. **Load Balancing**: Distribute tasks across available resources
3. **State Synchronization**: Consistent state across distributed components
4. **Fault Tolerance**: Graceful handling of node failures

#### Container Support

<augment_code_snippet path="aworlddistributed/docker-compose.yaml" mode="EXCERPT">
````yaml
version: '3.8'
services:
  aworld-api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - LLM_MODEL_NAME=${LLM_MODEL_NAME}
      - LLM_API_KEY=${LLM_API_KEY}
````
</augment_code_snippet>

## Advanced Usage Patterns

### Custom Agent Development

#### Specialized Agent Classes

```python
class ResearchAgent(Agent):
    """Specialized agent for research tasks."""

    def __init__(self, **kwargs):
        super().__init__(
            name="Research Agent",
            system_prompt=self._build_research_prompt(),
            tool_names=["web_search", "document_reader", "data_analyzer"],
            **kwargs
        )

    def _build_research_prompt(self) -> str:
        """Build specialized prompt for research tasks."""
        return """You are a research specialist with expertise in:
        - Information gathering and synthesis
        - Data analysis and interpretation
        - Report generation and summarization
        """

    async def async_policy(self, observation: Observation, **kwargs):
        """Custom policy for research-specific workflows."""
        # Add research-specific logic
        actions = await super().async_policy(observation, **kwargs)
        return self._enhance_research_actions(actions)
```

### Memory Integration Patterns

#### Agent Memory Configuration

```python
from aworld.core.memory import AgentMemoryConfig, LongTermConfig

agent_memory_config = AgentMemoryConfig(
    long_term_config=LongTermConfig(
        enabled=True,
        trigger_params=LongTermMemoryTriggerParams(
            message_count_threshold=10,
            token_count_threshold=5000
        )
    )
)

agent = Agent(
    name="Memory-Enhanced Agent",
    conf=agent_config,
    agent_memory_config=agent_memory_config
)
```

### Tool Development Patterns

#### Async Tool Implementation

```python
@ToolFactory.register(name="async_web_tool", desc="Asynchronous web operations")
class AsyncWebTool(AsyncTool):
    """Asynchronous web tool for concurrent operations."""

    async def do_step(self, actions: List[ActionModel], **kwargs):
        """Execute multiple web operations concurrently."""
        tasks = []
        for action in actions:
            if action.action_name == "search":
                tasks.append(self._async_search(action.params))
            elif action.action_name == "fetch":
                tasks.append(self._async_fetch(action.params))

        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Process results and build observation
        observation = self._build_observation(results)
        return observation, 0.0, True, False, {"results": results}

    async def _async_search(self, params: Dict[str, Any]):
        """Perform asynchronous web search."""
        # Implementation details
        pass

    async def _async_fetch(self, params: Dict[str, Any]):
        """Perform asynchronous web fetch."""
        # Implementation details
        pass
```

### Error Handling and Resilience

#### Retry Mechanisms

```python
class ResilientAgent(Agent):
    """Agent with built-in retry and error handling."""

    async def async_policy(self, observation: Observation, **kwargs):
        """Policy with retry logic for LLM calls."""
        max_retries = self.conf.get("max_retries", 3)

        for attempt in range(max_retries):
            try:
                return await super().async_policy(observation, **kwargs)
            except Exception as e:
                if attempt == max_retries - 1:
                    raise e

                logger.warning(f"Attempt {attempt + 1} failed: {e}")
                await asyncio.sleep(2 ** attempt)  # Exponential backoff
```

#### Circuit Breaker Pattern

```python
class CircuitBreakerTool(AsyncTool):
    """Tool with circuit breaker for external service calls."""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.failure_count = 0
        self.last_failure_time = None
        self.circuit_open = False

    async def do_step(self, actions: List[ActionModel], **kwargs):
        """Execute with circuit breaker protection."""
        if self._is_circuit_open():
            return self._circuit_open_response()

        try:
            result = await self._execute_actions(actions)
            self._reset_circuit()
            return result
        except Exception as e:
            self._record_failure()
            raise e
```

## Deployment and Operations

### Production Deployment

#### Docker Deployment

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
RUN python setup.py install

EXPOSE 8000
CMD ["aworld", "api", "--port", "8000"]
```

#### Kubernetes Deployment

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: aworld-deployment
spec:
  replicas: 3
  selector:
    matchLabels:
      app: aworld
  template:
    metadata:
      labels:
        app: aworld
    spec:
      containers:
      - name: aworld
        image: aworld:latest
        ports:
        - containerPort: 8000
        env:
        - name: LLM_MODEL_NAME
          valueFrom:
            secretKeyRef:
              name: aworld-secrets
              key: llm-model-name
```

### Monitoring and Observability

#### Trace Configuration

```python
from aworld.trace import trace_configure

# Production trace configuration
trace_configure(
    provider="otlp",
    backends=["logfire", "jaeger"],
    base_url=os.getenv("TRACE_ENDPOINT"),
    write_token=os.getenv("TRACE_TOKEN"),
    server_enabled=True,
    server_port=7079
)
```

#### Custom Span Consumers

```python
from aworld.trace.span_cosumer import register_span_consumer, SpanConsumer

@register_span_consumer()
class MetricsSpanConsumer(SpanConsumer):
    """Custom span consumer for metrics collection."""

    def consume(self, spans: Sequence[Span]) -> None:
        """Process spans for metrics extraction."""
        for span in spans:
            # Extract metrics from span attributes
            duration = span.end_time - span.start_time
            operation = span.name

            # Send to metrics backend
            self.metrics_client.record_duration(operation, duration)
```

### Performance Tuning

#### Memory Optimization

```python
# Configure memory system for high-throughput scenarios
memory_config = MemoryConfig(
    provider="aworld",
    embedding_config=EmbeddingsConfig(
        provider="ollama",  # Local embeddings for reduced latency
        model_name="nomic-embed-text",
        context_length=8191
    ),
    vector_store_config=VectorDBConfig(
        provider="chroma",
        config={
            "chroma_data_path": "/data/chroma",
            "collection_name": "production",
            "batch_size": 1000  # Optimize for batch operations
        }
    )
)
```

#### Context Compression

```python
# Enable context compression for long conversations
context_rule_config = ContextRuleConfig(
    optimization_config=OptimizationConfig(
        enabled=True,
        max_token_budget_ratio=0.7
    ),
    compression_config=LlmCompressionConfig(
        enabled=True,
        compress_type="llm",
        trigger_compress_token_length=10000
    )
)
```

## Troubleshooting Guide

### Common Issues and Solutions

#### Agent Not Responding

**Symptoms:** Agent execution hangs or times out
**Causes:**
- LLM API connectivity issues
- Tool execution blocking
- Memory system bottlenecks

**Solutions:**
```python
# Add timeout configuration
agent_config = AgentConfig(
    llm_timeout=30.0,
    max_retries=3,
    retry_delay=1.0
)

# Enable debug logging
import logging
logging.getLogger("aworld").setLevel(logging.DEBUG)
```

#### Memory System Performance

**Symptoms:** Slow memory operations
**Causes:**
- Large vector database
- Inefficient embedding model
- Network latency

**Solutions:**
```python
# Optimize vector database configuration
vector_config = VectorDBConfig(
    provider="chroma",
    config={
        "chroma_data_path": "/fast-ssd/chroma",
        "batch_size": 500,
        "index_type": "hnsw",
        "hnsw_space": "cosine"
    }
)
```

#### Tool Execution Failures

**Symptoms:** Tools fail to execute or return errors
**Causes:**
- MCP server connectivity
- Tool configuration issues
- Sandbox restrictions

**Solutions:**
```python
# Add tool error handling
@ToolFactory.register(name="resilient_tool")
class ResilientTool(AsyncTool):
    async def do_step(self, actions, **kwargs):
        try:
            return await self._execute_actions(actions)
        except Exception as e:
            logger.error(f"Tool execution failed: {e}")
            # Return graceful failure response
            return self._build_error_observation(str(e))
```

### Debugging Techniques

#### Enable Comprehensive Logging

```python
import logging
from aworld.logs.util import logger

# Configure detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Enable trace logging
logger.setLevel(logging.DEBUG)
```

#### Use Trace Server

```bash
# Start trace server for debugging
export START_TRACE_SERVER=true
export TRACE_SERVER_PORT=7079

# Access trace UI at http://localhost:7079
aworld web --port 8000
```

## Migration and Upgrade Guide

### Version Compatibility

- **Python**: Requires Python 3.11 or higher
- **Dependencies**: See `aworld/requirements.txt` for detailed requirements
- **Breaking Changes**: Check release notes for API changes

### Migration Checklist

1. **Backup Configuration**: Save existing agent and tool configurations
2. **Update Dependencies**: Install new version requirements
3. **Test Compatibility**: Run existing agents with new version
4. **Update Code**: Adapt to any API changes
5. **Performance Testing**: Validate performance in new version

## Class Reference

### Core Classes

#### BaseAgent

**Location:** `aworld/core/agent/base.py`

**Purpose:** Abstract base class for all agent implementations in AWorld.

**Key Methods:**

| Method | Description | Parameters | Returns |
|--------|-------------|------------|---------|
| `__init__()` | Initialize agent with configuration | `name`, `conf`, `tool_names`, `agent_names` | None |
| `policy()` | Synchronous policy execution | `observation`, `info` | `OUTPUT` |
| `async_policy()` | Asynchronous policy execution | `observation`, `info` | `OUTPUT` |
| `run()` | Synchronous agent execution | `message`, `**kwargs` | `Message` |
| `async_run()` | Asynchronous agent execution | `message`, `**kwargs` | `Message` |
| `reset()` | Reset agent state | `options` | None |

**Configuration Parameters:**

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `name` | `str` | Required | Unique agent identifier |
| `conf` | `Config` | Required | Agent configuration object |
| `desc` | `str` | `None` | Agent description |
| `tool_names` | `List[str]` | `[]` | Available tool names |
| `agent_names` | `List[str]` | `[]` | Collaborating agent names |
| `mcp_servers` | `List[str]` | `[]` | MCP server configurations |
| `feedback_tool_result` | `bool` | `True` | Process tool results before continuing |
| `wait_tool_result` | `bool` | `False` | Wait for tool completion |

#### Agent (LLM Agent)

**Location:** `aworld/agents/llm_agent.py`

**Purpose:** Concrete implementation of BaseAgent with LLM integration.

**Key Features:**
- LLM model integration with multiple providers
- Tool calling and result processing
- Memory integration for context retention
- Prompt template management
- Event-driven execution support

**Additional Configuration:**

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `system_prompt` | `str` | `None` | System instruction for LLM |
| `system_prompt_template` | `BasePromptTemplate` | `None` | Dynamic prompt template |
| `use_tools_in_prompt` | `bool` | `False` | Include tool descriptions in prompt |
| `model_output_parser` | `ModelOutputParser` | `LlmOutputParser()` | Response parser |
| `tool_aggregate_func` | `Callable` | `None` | Tool result aggregation function |

**Usage Example:**
```python
agent = Agent(
    name="Assistant",
    conf=AgentConfig(
        llm_provider="openai",
        llm_model_name="gpt-4",
        llm_api_key=os.getenv("OPENAI_API_KEY"),
        llm_temperature=0.7
    ),
    system_prompt="You are a helpful AI assistant.",
    tool_names=["web_search", "calculator"],
    feedback_tool_result=True
)
```

#### Task

**Location:** `aworld/core/task.py`

**Purpose:** Encapsulates a complete execution unit with context and configuration.

**Key Attributes:**

| Attribute | Type | Description |
|-----------|------|-------------|
| `id` | `str` | Unique task identifier |
| `name` | `str` | Human-readable task name |
| `input` | `Any` | Task input data |
| `conf` | `Config` | Task configuration |
| `tools` | `List[Tool]` | Available tools |
| `agent` | `Optional[Agent]` | Single agent for execution |
| `swarm` | `Optional[Swarm]` | Multi-agent system |
| `context` | `Context` | Execution context |
| `outputs` | `Outputs` | Output management |
| `event_driven` | `bool` | Enable event-driven execution |

**Usage Example:**
```python
task = Task(
    name="Research Task",
    input="Analyze market trends in AI",
    agent=research_agent,
    conf=TaskConfig(max_steps=10, timeout=300),
    tools=[web_tool, analysis_tool]
)
```

#### Context

**Location:** `aworld/core/context/base.py`

**Purpose:** Central state management system for agent execution.

**Key Responsibilities:**
- State restoration and persistence
- Configuration management
- Runtime state tracking
- LLM prompt management
- Multi-task state coordination

**Core Methods:**

| Method | Description | Parameters | Returns |
|--------|-------------|------------|---------|
| `set_state()` | Set context state | `key`, `value` | None |
| `get_state()` | Get context state | `key` | `Any` |
| `deep_copy()` | Create context copy | None | `Context` |
| `merge_context()` | Merge child context | `child_context` | None |
| `fork_new_task()` | Create child context | `task_input` | `Context` |

**State Categories:**
- **Immutable**: `agent_id`, `agent_name`, `system_prompt`, `tool_names`
- **Mutable**: `messages`, `step`, `context_usage`, `trajectories`

#### Swarm

**Location:** `aworld/core/agent/swarm.py`

**Purpose:** Multi-agent topology management and coordination.

**Topology Types:**

| Type | Class | Description | Use Case |
|------|-------|-------------|----------|
| Workflow | `Swarm` | Sequential execution | Pipeline processing |
| Team | `TeamSwarm` | Leader-executor pattern | Hierarchical coordination |
| Handoff | `HandoffSwarm` | Peer-to-peer collaboration | Dynamic task routing |

**Configuration:**

| Parameter | Type | Description |
|-----------|------|-------------|
| `root_agent` | `BaseAgent` | Communication agent |
| `max_steps` | `int` | Maximum execution steps |
| `build_type` | `GraphBuildType` | Topology construction type |
| `event_driven` | `bool` | Enable event-driven execution |

**Usage Examples:**
```python
# Team topology
team = TeamSwarm(leader_agent, worker1, worker2)

# Handoff topology
handoff = HandoffSwarm((agent_a, agent_b), (agent_b, agent_c))

# Custom topology
custom = Swarm(
    agent1, agent2, agent3,
    build_type=GraphBuildType.WORKFLOW,
    max_steps=20
)
```

### Tool Classes

#### BaseTool

**Location:** `aworld/core/tool/base.py`

**Purpose:** Abstract base class for all tool implementations.

**Key Methods:**

| Method | Description | Parameters | Returns |
|--------|-------------|------------|---------|
| `step()` | Execute tool action | `message` | `Message` |
| `do_step()` | Core tool logic | `action` | `Tuple[observation, reward, done, truncated, info]` |
| `reset()` | Reset tool state | `seed`, `options` | `Tuple[observation, info]` |
| `pre_step()` | Pre-execution hook | `action` | None |
| `post_step()` | Post-execution hook | `step_res`, `action` | `Message` |

#### Tool and AsyncTool

**Concrete Implementations:**
- `Tool`: Synchronous tool execution
- `AsyncTool`: Asynchronous tool execution with concurrent support

**Registration Pattern:**
```python
@ToolFactory.register(name="tool_name", desc="Tool description")
class MyTool(AsyncTool):
    async def do_step(self, actions: List[ActionModel], **kwargs):
        # Tool implementation
        return observation, reward, done, truncated, info
```

### Memory Classes

#### MemoryBase

**Location:** `aworld/memory/main.py`

**Purpose:** Abstract interface for memory system implementations.

**Core Operations:**

| Method | Description | Parameters | Returns |
|--------|-------------|------------|---------|
| `add()` | Store memory item | `memory_item`, `filters`, `config` | None |
| `search()` | Search memories | `query`, `filters`, `limit` | `List[MemoryItem]` |
| `get_all()` | Retrieve all memories | `filters` | `List[MemoryItem]` |
| `update()` | Update memory item | `memory_item` | None |
| `delete()` | Delete memory | `memory_id` | None |

#### AworldMemory

**Location:** `aworld/memory/main.py`

**Purpose:** Default memory implementation with vector database integration.

**Features:**
- Short-term and long-term memory management
- Vector database integration for semantic search
- Automatic memory summarization and compression
- User profile and agent experience tracking

#### MemoryItem

**Purpose:** Represents a single memory entry.

**Structure:**
```python
class MemoryItem:
    id: str                    # Unique identifier
    content: str               # Memory content
    metadata: Dict[str, Any]   # Associated metadata
    timestamp: datetime        # Creation time
    memory_type: str           # Type classification
    embedding: List[float]     # Vector representation
```

### Configuration Classes

#### AgentConfig

**Location:** `aworld/config/conf.py`

**Purpose:** Configuration for agent behavior and LLM integration.

**Key Parameters:**

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `llm_provider` | `str` | `"openai"` | LLM provider name |
| `llm_model_name` | `str` | Required | Model identifier |
| `llm_api_key` | `str` | Required | API authentication key |
| `llm_base_url` | `str` | Provider default | API endpoint URL |
| `llm_temperature` | `float` | `1.0` | Response randomness |
| `max_retries` | `int` | `3` | Retry attempts for failed calls |
| `max_model_len` | `int` | `128000` | Maximum context length |

#### TaskConfig

**Purpose:** Configuration for task execution parameters.

**Parameters:**

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `max_steps` | `int` | `10` | Maximum execution steps |
| `timeout` | `float` | `300.0` | Execution timeout (seconds) |
| `enable_trace` | `bool` | `True` | Enable distributed tracing |
| `enable_memory` | `bool` | `True` | Enable memory integration |
| `endless_threshold` | `int` | `3` | Loop detection threshold |

#### MemoryConfig

**Purpose:** Configuration for memory system components.

**Structure:**
```python
class MemoryConfig:
    provider: str                    # Memory provider (aworld, mem0)
    llm_config: MemoryLLMConfig     # LLM configuration for memory operations
    embedding_config: EmbeddingsConfig  # Embedding model configuration
    vector_store_config: VectorDBConfig  # Vector database configuration
```

### Factory Classes

#### ToolFactory

**Location:** `aworld/core/tool/base.py`

**Purpose:** Manages tool registration and instantiation.

**Key Methods:**
```python
class ToolFactory(Factory):
    @classmethod
    def register(cls, name: str, desc: str, **kwargs)

    @classmethod
    def create_tool(cls, name: str, conf: ToolConfig, **kwargs) -> BaseTool

    @classmethod
    def get_tool_desc(cls, name: str) -> str
```

#### ActionFactory

**Location:** `aworld/core/tool/action_factory.py`

**Purpose:** Manages action registration and execution.

**Registration Pattern:**
```python
@ActionFactory.register(name="action_name", desc="Action description", tool_name="tool_name")
class MyAction(ExecutableAction):
    async def async_act(self, **kwargs) -> ActionResult:
        # Action implementation
        return ActionResult(content=result, success=True)
```

### Event Classes

#### Message

**Location:** `aworld/core/event/base.py`

**Purpose:** Represents communication between components.

**Structure:**
```python
class Message:
    category: str        # Message category (TASK, TOOL, AGENT, OUTPUT)
    payload: Any         # Message content
    sender: str          # Sender identifier
    receiver: str        # Receiver identifier
    session_id: str      # Session identifier
    topic: str           # Message topic
    context: Context     # Execution context
    headers: Dict        # Additional metadata
```

**Message Categories:**
- `Constants.TASK`: Task-related messages
- `Constants.TOOL`: Tool execution messages
- `Constants.AGENT`: Agent communication messages
- `Constants.OUTPUT`: Output and result messages

#### EventManager

**Location:** `aworld/events/manager.py`

**Purpose:** Manages event-driven communication and message routing.

**Core Functionality:**
```python
class EventManager:
    async def emit(self, data: Any, sender: str, receiver: str = None)
    async def consume(self, nowait: bool = False)
    async def register(self, event_type: str, topic: str, handler: Callable)
    async def unregister(self, event_type: str, topic: str, handler: Callable)
```

## Practical Usage Examples

### Basic Agent Usage

#### Simple Question-Answering Agent

```python
import os
from aworld.agents.llm_agent import Agent
from aworld.config.conf import AgentConfig
from aworld.runner import Runners

# Configure agent
config = AgentConfig(
    llm_provider="openai",
    llm_model_name="gpt-4",
    llm_api_key=os.getenv("OPENAI_API_KEY"),
    llm_temperature=0.7
)

# Create agent
qa_agent = Agent(
    name="QA Agent",
    conf=config,
    system_prompt="""You are a knowledgeable assistant that provides
    accurate and helpful answers to user questions. Always cite sources
    when possible and admit when you don't know something."""
)

# Execute query
result = Runners.sync_run(
    input="What are the key principles of machine learning?",
    agent=qa_agent
)

print(f"Answer: {result.answer}")
```

#### Agent with Tool Integration

```python
from aworld.agents.llm_agent import Agent
from aworld.runner import Runners

# MCP configuration for tools
mcp_config = {
    "mcpServers": {
        "web_search": {
            "type": "stdio",
            "command": "python",
            "args": ["tools/web_search_server.py"]
        },
        "calculator": {
            "type": "sse",
            "url": "https://api.calculator.com/mcp",
            "timeout": 30
        }
    }
}

# Create tool-enabled agent
research_agent = Agent(
    name="Research Agent",
    conf=config,
    system_prompt="""You are a research assistant with access to web search
    and calculation tools. Use these tools to provide comprehensive and
    accurate information.""",
    mcp_servers=["web_search", "calculator"],
    mcp_config=mcp_config,
    feedback_tool_result=True
)

# Execute research task
result = Runners.sync_run(
    input="Research the latest developments in quantum computing and calculate the market size",
    agent=research_agent
)
```

### Multi-Agent System Examples

#### Team Topology (Leader-Executor)

```python
from aworld.agents.llm_agent import Agent
from aworld.core.agent.swarm import TeamSwarm
from aworld.runner import Runners

# Create specialized agents
coordinator = Agent(
    name="Project Coordinator",
    conf=config,
    system_prompt="""You are a project coordinator responsible for breaking down
    complex tasks and delegating them to specialized team members. Analyze the
    requirements and assign appropriate subtasks."""
)

researcher = Agent(
    name="Research Specialist",
    conf=config,
    system_prompt="""You are a research specialist. Focus on gathering information,
    analyzing data, and providing detailed research findings.""",
    tool_names=["web_search", "document_reader"]
)

analyst = Agent(
    name="Data Analyst",
    conf=config,
    system_prompt="""You are a data analyst. Focus on processing data,
    creating visualizations, and generating insights from research findings.""",
    tool_names=["data_processor", "chart_generator"]
)

writer = Agent(
    name="Technical Writer",
    conf=config,
    system_prompt="""You are a technical writer. Focus on creating clear,
    comprehensive documentation and reports from research and analysis.""",
    tool_names=["document_generator", "formatter"]
)

# Create team swarm
research_team = TeamSwarm(coordinator, researcher, analyst, writer)

# Execute complex research project
result = Runners.sync_run(
    input="Conduct a comprehensive analysis of renewable energy trends and create a detailed report",
    swarm=research_team
)
```

#### Handoff Topology (Peer-to-Peer)

```python
from aworld.core.agent.swarm import HandoffSwarm

# Create collaborative agents
code_reviewer = Agent(
    name="Code Reviewer",
    conf=config,
    system_prompt="""You are a senior code reviewer. Analyze code for quality,
    security, and best practices. Provide detailed feedback and suggestions.""",
    tool_names=["static_analyzer", "security_scanner"]
)

performance_optimizer = Agent(
    name="Performance Optimizer",
    conf=config,
    system_prompt="""You are a performance optimization specialist. Analyze code
    for performance bottlenecks and suggest optimizations.""",
    tool_names=["profiler", "benchmark_tool"]
)

documentation_expert = Agent(
    name="Documentation Expert",
    conf=config,
    system_prompt="""You are a documentation specialist. Ensure code is properly
    documented and create comprehensive technical documentation.""",
    tool_names=["doc_generator", "api_documenter"]
)

# Create handoff swarm for code review workflow
code_review_team = HandoffSwarm(
    (code_reviewer, performance_optimizer),
    (performance_optimizer, documentation_expert),
    (documentation_expert, code_reviewer)
)

# Execute code review workflow
result = Runners.sync_run(
    input="Review and optimize the attached Python codebase",
    swarm=code_review_team
)
```

### Memory Integration Examples

#### Agent with Long-term Memory

```python
from aworld.memory.main import MemoryFactory
from aworld.core.memory import MemoryConfig, AgentMemoryConfig, LongTermConfig

# Initialize memory system
MemoryFactory.init(config=MemoryConfig(
    provider="aworld",
    llm_config=MemoryLLMConfig(
        provider="openai",
        model_name="gpt-4",
        api_key=os.getenv("OPENAI_API_KEY")
    ),
    embedding_config=EmbeddingsConfig(
        provider="openai",
        model_name="text-embedding-3-small",
        api_key=os.getenv("OPENAI_API_KEY")
    ),
    vector_store_config=VectorDBConfig(
        provider="chroma",
        config={"chroma_data_path": "./memory_db"}
    )
))

# Configure agent memory
agent_memory_config = AgentMemoryConfig(
    long_term_config=LongTermConfig(
        enabled=True,
        trigger_params=LongTermMemoryTriggerParams(
            message_count_threshold=10,
            token_count_threshold=5000
        )
    )
)

# Create memory-enabled agent
memory_agent = Agent(
    name="Memory Assistant",
    conf=config,
    system_prompt="""You are an AI assistant with long-term memory.
    Remember important information about users and previous conversations
    to provide personalized assistance.""",
    agent_memory_config=agent_memory_config
)

# Use agent with memory
result = Runners.sync_run(
    input="Remember that I prefer Python over JavaScript for backend development",
    agent=memory_agent,
    session_id="user_123"
)
```

#### Custom Memory Store

```python
from aworld.core.memory import MemoryStore, MemoryItem
from aworld.memory.db.postgres import PostgresMemoryStore

# Use PostgreSQL as memory backend
postgres_store = PostgresMemoryStore(
    db_url="postgresql://user:password@localhost:5432/aworld_memory"
)

MemoryFactory.init(
    custom_memory_store=postgres_store,
    config=MemoryConfig(provider="aworld")
)
```

### Advanced Tool Development

#### Custom Async Tool with Error Handling

```python
import asyncio
import aiohttp
from typing import List, Tuple, Dict, Any

@ToolFactory.register(name="advanced_web_tool", desc="Advanced web operations with retry logic")
class AdvancedWebTool(AsyncTool):
    """Advanced web tool with comprehensive error handling and retry logic."""

    def __init__(self, conf: ToolConfig, **kwargs):
        super().__init__(conf, **kwargs)
        self.session = None
        self.max_retries = conf.get("max_retries", 3)
        self.timeout = conf.get("timeout", 30)

    async def reset(self, *, seed: int = None, options: Dict[str, Any] = None):
        """Initialize HTTP session."""
        if self.session:
            await self.session.close()

        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.timeout)
        )

        return Observation(content="Web tool initialized"), {}

    async def do_step(self, actions: List[ActionModel], **kwargs) -> Tuple[
        Observation, float, bool, bool, Dict[str, Any]
    ]:
        """Execute web operations with retry logic."""
        results = []

        for action in actions:
            if action.action_name == "search":
                result = await self._search_with_retry(action.params)
            elif action.action_name == "fetch":
                result = await self._fetch_with_retry(action.params)
            else:
                result = {"error": f"Unknown action: {action.action_name}"}

            results.append(result)

        observation = Observation(
            content=f"Completed {len(results)} web operations",
            metadata={"results": results}
        )

        return observation, 0.0, True, False, {"results": results}

    async def _search_with_retry(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Perform web search with exponential backoff retry."""
        query = params.get("query", "")

        for attempt in range(self.max_retries):
            try:
                async with self.session.get(
                    "https://api.search.com/search",
                    params={"q": query}
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {"success": True, "data": data}
                    else:
                        raise aiohttp.ClientResponseError(
                            request_info=response.request_info,
                            history=response.history,
                            status=response.status
                        )

            except Exception as e:
                if attempt == self.max_retries - 1:
                    return {"success": False, "error": str(e)}

                wait_time = 2 ** attempt
                await asyncio.sleep(wait_time)

        return {"success": False, "error": "Max retries exceeded"}

    async def _fetch_with_retry(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Fetch web content with retry logic."""
        url = params.get("url", "")

        for attempt in range(self.max_retries):
            try:
                async with self.session.get(url) as response:
                    if response.status == 200:
                        content = await response.text()
                        return {"success": True, "content": content}
                    else:
                        raise aiohttp.ClientResponseError(
                            request_info=response.request_info,
                            history=response.history,
                            status=response.status
                        )

            except Exception as e:
                if attempt == self.max_retries - 1:
                    return {"success": False, "error": str(e)}

                wait_time = 2 ** attempt
                await asyncio.sleep(wait_time)

        return {"success": False, "error": "Max retries exceeded"}

    async def cleanup(self):
        """Cleanup HTTP session."""
        if self.session:
            await self.session.close()
```

#### Action Registration for Custom Tool

```python
from aworld.core.tool.action import ExecutableAction
from aworld.core.tool.action_factory import ActionFactory

@ActionFactory.register(
    name="search",
    desc="Search the web for information",
    tool_name="advanced_web_tool"
)
class WebSearchAction(ExecutableAction):
    """Web search action with parameter validation."""

    async def async_act(self, query: str, max_results: int = 10, **kwargs) -> ActionResult:
        """Execute web search with parameter validation."""
        if not query or not query.strip():
            return ActionResult(
                success=False,
                error="Query parameter is required and cannot be empty"
            )

        if max_results <= 0 or max_results > 100:
            return ActionResult(
                success=False,
                error="max_results must be between 1 and 100"
            )

        # Action would be executed by the tool
        return ActionResult(
            success=True,
            content=f"Search initiated for: {query}",
            metadata={"query": query, "max_results": max_results}
        )

@ActionFactory.register(
    name="fetch",
    desc="Fetch content from a URL",
    tool_name="advanced_web_tool"
)
class WebFetchAction(ExecutableAction):
    """Web fetch action with URL validation."""

    async def async_act(self, url: str, **kwargs) -> ActionResult:
        """Execute web fetch with URL validation."""
        import re

        # Basic URL validation
        url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
            r'localhost|'  # localhost...
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)

        if not url_pattern.match(url):
            return ActionResult(
                success=False,
                error="Invalid URL format"
            )

        return ActionResult(
            success=True,
            content=f"Fetch initiated for: {url}",
            metadata={"url": url}
        )
```

### Streaming and Real-time Processing

#### Streaming Agent Execution

```python
import asyncio
from aworld.runner import Runners
from aworld.core.task import Task
from aworld.config.conf import TaskConfig

async def streaming_example():
    """Example of streaming agent execution with real-time output."""

    # Create task for streaming
    task = Task(
        name="Streaming Analysis",
        input="Analyze the current state of artificial intelligence research",
        agent=research_agent,
        conf=TaskConfig(max_steps=15)
    )

    # Start streaming execution
    streaming_output = Runners.streamed_run_task(task)

    print("Starting streaming analysis...")

    # Process streaming events
    async for event in streaming_output.stream_events():
        if hasattr(event, 'category'):
            if event.category == "OUTPUT":
                if hasattr(event.payload, 'content'):
                    print(f"Agent: {event.payload.content}")
                elif hasattr(event.payload, 'data'):
                    print(f"Data: {event.payload.data}")
            elif event.category == "TOOL":
                print(f"Tool execution: {event.payload}")

    # Get final result
    final_result = await streaming_output.get_final_result()
    print(f"Final analysis: {final_result.answer}")

# Run streaming example
asyncio.run(streaming_example())
```

### Error Handling and Resilience Patterns

#### Resilient Agent with Fallback

```python
class ResilientAgent(Agent):
    """Agent with comprehensive error handling and fallback mechanisms."""

    def __init__(self, primary_config: AgentConfig, fallback_config: AgentConfig, **kwargs):
        super().__init__(conf=primary_config, **kwargs)
        self.fallback_config = fallback_config
        self.failure_count = 0
        self.max_failures = 3

    async def async_policy(self, observation: Observation, **kwargs):
        """Policy with fallback to secondary LLM on failures."""
        try:
            # Try primary LLM
            return await super().async_policy(observation, **kwargs)

        except Exception as e:
            self.failure_count += 1
            logger.warning(f"Primary LLM failed (attempt {self.failure_count}): {e}")

            if self.failure_count >= self.max_failures:
                # Switch to fallback configuration
                logger.info("Switching to fallback LLM configuration")
                self.conf = self.fallback_config
                self.failure_count = 0

            # Retry with current configuration
            return await super().async_policy(observation, **kwargs)

# Usage
primary_config = AgentConfig(
    llm_provider="openai",
    llm_model_name="gpt-4",
    llm_api_key=os.getenv("OPENAI_API_KEY")
)

fallback_config = AgentConfig(
    llm_provider="anthropic",
    llm_model_name="claude-3-sonnet",
    llm_api_key=os.getenv("ANTHROPIC_API_KEY")
)

resilient_agent = ResilientAgent(
    name="Resilient Assistant",
    primary_config=primary_config,
    fallback_config=fallback_config,
    system_prompt="You are a reliable AI assistant with fallback capabilities."
)
```

### Custom Memory Implementation

#### Specialized Memory Store

```python
import json
import redis
from aworld.core.memory import MemoryStore, MemoryItem

class RedisMemoryStore(MemoryStore):
    """Redis-based memory store for high-performance caching."""

    def __init__(self, redis_url: str = "redis://localhost:6379"):
        self.redis_client = redis.from_url(redis_url, decode_responses=True)
        self.key_prefix = "aworld:memory:"

    def add(self, memory_item: MemoryItem):
        """Add memory item to Redis."""
        key = f"{self.key_prefix}{memory_item.id}"
        value = json.dumps(memory_item.model_dump())

        # Store with expiration (24 hours)
        self.redis_client.setex(key, 86400, value)

        # Add to session index
        session_key = f"{self.key_prefix}session:{memory_item.metadata.get('session_id')}"
        self.redis_client.sadd(session_key, memory_item.id)

    def get(self, memory_id: str) -> Optional[MemoryItem]:
        """Retrieve memory item by ID."""
        key = f"{self.key_prefix}{memory_id}"
        value = self.redis_client.get(key)

        if value:
            data = json.loads(value)
            return MemoryItem(**data)
        return None

    def get_all(self, filters: dict = None) -> List[MemoryItem]:
        """Retrieve all memory items matching filters."""
        if filters and "session_id" in filters:
            session_key = f"{self.key_prefix}session:{filters['session_id']}"
            memory_ids = self.redis_client.smembers(session_key)

            memories = []
            for memory_id in memory_ids:
                memory = self.get(memory_id)
                if memory:
                    memories.append(memory)
            return memories

        # Fallback: scan all keys (expensive operation)
        pattern = f"{self.key_prefix}*"
        keys = self.redis_client.keys(pattern)

        memories = []
        for key in keys:
            if ":session:" not in key:  # Skip session index keys
                value = self.redis_client.get(key)
                if value:
                    data = json.loads(value)
                    memories.append(MemoryItem(**data))

        return memories

    def update(self, memory_item: MemoryItem):
        """Update existing memory item."""
        self.add(memory_item)  # Redis SET overwrites existing keys

    def delete(self, memory_id: str):
        """Delete memory item."""
        key = f"{self.key_prefix}{memory_id}"
        self.redis_client.delete(key)

# Usage
redis_store = RedisMemoryStore("redis://localhost:6379")
MemoryFactory.init(
    custom_memory_store=redis_store,
    config=MemoryConfig(provider="aworld")
)
```

### Production Deployment Examples

#### Web Application Integration

```python
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from aworld.runner import Runners
from aworld.agents.llm_agent import Agent
from aworld.config.conf import AgentConfig

app = FastAPI(title="AWorld API Service")

# Initialize agent
agent_config = AgentConfig(
    llm_provider="openai",
    llm_model_name="gpt-4",
    llm_api_key=os.getenv("OPENAI_API_KEY")
)

assistant_agent = Agent(
    name="API Assistant",
    conf=agent_config,
    system_prompt="You are an API assistant that provides helpful responses to user queries."
)

class QueryRequest(BaseModel):
    message: str
    session_id: str = None

class QueryResponse(BaseModel):
    response: str
    session_id: str
    success: bool

@app.post("/chat", response_model=QueryResponse)
async def chat_endpoint(request: QueryRequest):
    """Chat endpoint using AWorld agent."""
    try:
        result = await Runners.run(
            input=request.message,
            agent=assistant_agent,
            session_id=request.session_id
        )

        return QueryResponse(
            response=result.answer,
            session_id=result.context.session_id,
            success=result.success
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "framework": "AWorld"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
```

#### Batch Processing System

```python
import asyncio
from concurrent.futures import ThreadPoolExecutor
from typing import List
from aworld.runner import Runners
from aworld.core.task import Task

class BatchProcessor:
    """Batch processing system for high-throughput agent execution."""

    def __init__(self, agent: Agent, max_workers: int = 10):
        self.agent = agent
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(max_workers=max_workers)

    async def process_batch(self, inputs: List[str]) -> List[Dict[str, Any]]:
        """Process multiple inputs concurrently."""
        tasks = []

        for i, input_text in enumerate(inputs):
            task = Task(
                name=f"Batch Task {i}",
                input=input_text,
                agent=self.agent
            )
            tasks.append(task)

        # Execute tasks concurrently
        results = await asyncio.gather(
            *[Runners.run_task(task) for task in tasks],
            return_exceptions=True
        )

        # Process results
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    "index": i,
                    "success": False,
                    "error": str(result)
                })
            else:
                task_response = list(result.values())[0]
                processed_results.append({
                    "index": i,
                    "success": task_response.success,
                    "answer": task_response.answer,
                    "time_cost": task_response.time_cost
                })

        return processed_results

# Usage
batch_processor = BatchProcessor(assistant_agent, max_workers=5)

inputs = [
    "Summarize the benefits of renewable energy",
    "Explain quantum computing in simple terms",
    "Describe the impact of AI on healthcare",
    "Analyze trends in electric vehicle adoption"
]

results = asyncio.run(batch_processor.process_batch(inputs))
for result in results:
    print(f"Task {result['index']}: {result['answer'][:100]}...")
```

## Best Practices and Design Patterns

### Agent Design Patterns

#### Specialized Agent Factory

```python
class AgentFactory:
    """Factory for creating specialized agents with predefined configurations."""

    @staticmethod
    def create_research_agent(api_key: str) -> Agent:
        """Create a research-specialized agent."""
        return Agent(
            name="Research Specialist",
            conf=AgentConfig(
                llm_provider="openai",
                llm_model_name="gpt-4",
                llm_api_key=api_key,
                llm_temperature=0.3  # Lower temperature for factual accuracy
            ),
            system_prompt="""You are a research specialist with expertise in:
            - Academic research and literature review
            - Data analysis and statistical interpretation
            - Fact-checking and source verification
            - Report writing and documentation

            Always cite sources and provide evidence for your claims.""",
            tool_names=["web_search", "document_reader", "citation_tool"]
        )

    @staticmethod
    def create_creative_agent(api_key: str) -> Agent:
        """Create a creative-specialized agent."""
        return Agent(
            name="Creative Specialist",
            conf=AgentConfig(
                llm_provider="openai",
                llm_model_name="gpt-4",
                llm_api_key=api_key,
                llm_temperature=0.9  # Higher temperature for creativity
            ),
            system_prompt="""You are a creative specialist with expertise in:
            - Creative writing and storytelling
            - Brainstorming and ideation
            - Content creation and marketing
            - Design thinking and innovation

            Think outside the box and provide original, engaging content.""",
            tool_names=["image_generator", "content_formatter", "style_analyzer"]
        )

    @staticmethod
    def create_code_agent(api_key: str) -> Agent:
        """Create a code-specialized agent."""
        return Agent(
            name="Code Specialist",
            conf=AgentConfig(
                llm_provider="openai",
                llm_model_name="gpt-4",
                llm_api_key=api_key,
                llm_temperature=0.1  # Very low temperature for code accuracy
            ),
            system_prompt="""You are a software engineering specialist with expertise in:
            - Code generation and optimization
            - Debugging and troubleshooting
            - Architecture design and best practices
            - Testing and quality assurance

            Always write clean, efficient, and well-documented code.""",
            tool_names=["code_executor", "linter", "test_runner", "documentation_generator"]
        )
```

#### Agent Composition Pattern

```python
class CompositeAgent:
    """Composite agent that delegates to specialized sub-agents."""

    def __init__(self, api_key: str):
        self.research_agent = AgentFactory.create_research_agent(api_key)
        self.creative_agent = AgentFactory.create_creative_agent(api_key)
        self.code_agent = AgentFactory.create_code_agent(api_key)

        # Router agent to determine which specialist to use
        self.router_agent = Agent(
            name="Router Agent",
            conf=AgentConfig(
                llm_provider="openai",
                llm_model_name="gpt-3.5-turbo",
                llm_api_key=api_key,
                llm_temperature=0.0
            ),
            system_prompt="""You are a routing agent that determines which specialist
            should handle a given task. Respond with one of: RESEARCH, CREATIVE, CODE, or GENERAL.

            RESEARCH: For factual questions, analysis, data interpretation
            CREATIVE: For creative writing, brainstorming, content creation
            CODE: For programming, debugging, technical implementation
            GENERAL: For general conversation or unclear requests"""
        )

    async def process_request(self, user_input: str, session_id: str = None) -> str:
        """Route request to appropriate specialist agent."""

        # Determine routing
        routing_result = await Runners.run(
            input=f"Classify this request: {user_input}",
            agent=self.router_agent,
            session_id=session_id
        )

        route = routing_result.answer.strip().upper()

        # Select appropriate agent
        if route == "RESEARCH":
            selected_agent = self.research_agent
        elif route == "CREATIVE":
            selected_agent = self.creative_agent
        elif route == "CODE":
            selected_agent = self.code_agent
        else:
            selected_agent = self.research_agent  # Default fallback

        # Execute with selected agent
        result = await Runners.run(
            input=user_input,
            agent=selected_agent,
            session_id=session_id
        )

        return result.answer

# Usage
composite = CompositeAgent(os.getenv("OPENAI_API_KEY"))

# Research request
research_response = await composite.process_request(
    "What are the latest developments in quantum computing?",
    session_id="user_123"
)

# Creative request
creative_response = await composite.process_request(
    "Write a short story about AI and humans working together",
    session_id="user_123"
)

# Code request
code_response = await composite.process_request(
    "Create a Python function to calculate fibonacci numbers efficiently",
    session_id="user_123"
)
```

### Testing Strategies

#### Unit Testing for Agents

```python
import pytest
import asyncio
from unittest.mock import Mock, AsyncMock
from aworld.agents.llm_agent import Agent
from aworld.config.conf import AgentConfig
from aworld.core.common import Observation

class TestAgent:
    """Comprehensive test suite for Agent functionality."""

    @pytest.fixture
    def mock_config(self):
        """Mock configuration for testing."""
        return AgentConfig(
            llm_provider="mock",
            llm_model_name="mock-model",
            llm_api_key="mock-key",
            llm_temperature=0.7
        )

    @pytest.fixture
    def test_agent(self, mock_config):
        """Create test agent instance."""
        return Agent(
            name="Test Agent",
            conf=mock_config,
            system_prompt="You are a test agent."
        )

    @pytest.mark.asyncio
    async def test_agent_policy_execution(self, test_agent):
        """Test agent policy execution."""
        # Mock LLM response
        test_agent._call_llm_model = AsyncMock(return_value=Mock(
            content="Test response",
            tool_calls=None
        ))

        observation = Observation(content="Test input")
        result = await test_agent.async_policy(observation)

        assert len(result) == 1
        assert result[0].policy_info == "Test response"

    @pytest.mark.asyncio
    async def test_agent_tool_calling(self, test_agent):
        """Test agent tool calling functionality."""
        # Mock LLM response with tool calls
        mock_tool_call = Mock()
        mock_tool_call.id = "test_call_id"
        mock_tool_call.function.name = "test_tool"
        mock_tool_call.function.arguments = '{"param": "value"}'

        test_agent._call_llm_model = AsyncMock(return_value=Mock(
            content="Using test tool",
            tool_calls=[mock_tool_call]
        ))

        observation = Observation(content="Test input")
        result = await test_agent.async_policy(observation)

        assert len(result) == 1
        assert result[0].tool_name == "test_tool"
        assert result[0].tool_call_id == "test_call_id"
```

#### Integration Testing for Multi-Agent Systems

```python
import pytest
from aworld.core.agent.swarm import TeamSwarm
from aworld.runner import Runners

class TestMultiAgentSystem:
    """Integration tests for multi-agent systems."""

    @pytest.fixture
    def team_swarm(self, mock_config):
        """Create test team swarm."""
        leader = Agent(name="Leader", conf=mock_config, system_prompt="You are a team leader.")
        worker1 = Agent(name="Worker1", conf=mock_config, system_prompt="You are worker 1.")
        worker2 = Agent(name="Worker2", conf=mock_config, system_prompt="You are worker 2.")

        return TeamSwarm(leader, worker1, worker2)

    @pytest.mark.asyncio
    async def test_team_coordination(self, team_swarm):
        """Test team coordination and task delegation."""
        # Mock agent responses
        for agent in team_swarm.agent_list:
            agent._call_llm_model = AsyncMock(return_value=Mock(
                content=f"Response from {agent.name()}",
                tool_calls=None
            ))

        result = await Runners.run(
            input="Coordinate team to solve complex problem",
            swarm=team_swarm
        )

        assert result.success
        assert "Leader" in result.answer or "Worker" in result.answer
```

### Performance Benchmarking

#### Agent Performance Testing

```python
import time
import asyncio
import statistics
from typing import List
from aworld.runner import Runners

class PerformanceBenchmark:
    """Performance benchmarking utilities for AWorld components."""

    @staticmethod
    async def benchmark_agent_latency(agent: Agent, test_inputs: List[str],
                                     iterations: int = 10) -> Dict[str, float]:
        """Benchmark agent response latency."""
        latencies = []

        for _ in range(iterations):
            for input_text in test_inputs:
                start_time = time.time()

                result = await Runners.run(
                    input=input_text,
                    agent=agent
                )

                end_time = time.time()
                latencies.append(end_time - start_time)

        return {
            "mean_latency": statistics.mean(latencies),
            "median_latency": statistics.median(latencies),
            "min_latency": min(latencies),
            "max_latency": max(latencies),
            "std_dev": statistics.stdev(latencies) if len(latencies) > 1 else 0
        }

    @staticmethod
    async def benchmark_memory_operations(memory_system, test_items: List[MemoryItem],
                                        iterations: int = 100) -> Dict[str, float]:
        """Benchmark memory system performance."""
        add_times = []
        search_times = []

        for _ in range(iterations):
            # Benchmark add operations
            start_time = time.time()
            for item in test_items:
                await memory_system.add(item)
            add_time = time.time() - start_time
            add_times.append(add_time)

            # Benchmark search operations
            start_time = time.time()
            await memory_system.search("test query", limit=10)
            search_time = time.time() - start_time
            search_times.append(search_time)

        return {
            "add_mean_time": statistics.mean(add_times),
            "search_mean_time": statistics.mean(search_times),
            "add_throughput": len(test_items) / statistics.mean(add_times),
            "search_throughput": 1 / statistics.mean(search_times)
        }

# Usage
benchmark = PerformanceBenchmark()

# Test agent performance
test_inputs = [
    "What is machine learning?",
    "Explain neural networks",
    "Describe deep learning applications"
]

agent_metrics = await benchmark.benchmark_agent_latency(
    agent=research_agent,
    test_inputs=test_inputs,
    iterations=5
)

print(f"Agent Performance Metrics:")
print(f"Mean Latency: {agent_metrics['mean_latency']:.2f}s")
print(f"Median Latency: {agent_metrics['median_latency']:.2f}s")
```

### Security Best Practices

#### Secure Configuration Management

```python
import os
from cryptography.fernet import Fernet
from aworld.config.conf import AgentConfig

class SecureConfigManager:
    """Secure configuration management with encryption."""

    def __init__(self, encryption_key: bytes = None):
        self.cipher = Fernet(encryption_key or Fernet.generate_key())

    def encrypt_config(self, config: AgentConfig) -> str:
        """Encrypt sensitive configuration data."""
        sensitive_data = {
            "llm_api_key": config.llm_api_key,
            "llm_base_url": config.llm_base_url
        }

        encrypted_data = self.cipher.encrypt(
            json.dumps(sensitive_data).encode()
        )
        return encrypted_data.decode()

    def decrypt_config(self, encrypted_data: str, base_config: AgentConfig) -> AgentConfig:
        """Decrypt and restore configuration."""
        decrypted_data = self.cipher.decrypt(encrypted_data.encode())
        sensitive_data = json.loads(decrypted_data.decode())

        # Update base config with decrypted sensitive data
        base_config.llm_api_key = sensitive_data["llm_api_key"]
        base_config.llm_base_url = sensitive_data["llm_base_url"]

        return base_config

# Usage
config_manager = SecureConfigManager()

# Create base configuration (non-sensitive data)
base_config = AgentConfig(
    llm_provider="openai",
    llm_model_name="gpt-4",
    llm_temperature=0.7
)

# Add sensitive data
base_config.llm_api_key = os.getenv("OPENAI_API_KEY")
base_config.llm_base_url = "https://api.openai.com/v1"

# Encrypt for storage
encrypted_config = config_manager.encrypt_config(base_config)

# Later: decrypt for use
secure_config = config_manager.decrypt_config(encrypted_config, base_config)
```

#### Input Validation and Sanitization

```python
import re
from typing import Any, Dict
from aworld.core.tool.action import ExecutableAction

class SecureAction(ExecutableAction):
    """Base class for secure action implementations."""

    def validate_input(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and sanitize input parameters."""
        sanitized_params = {}

        for key, value in params.items():
            # Remove potentially dangerous characters
            if isinstance(value, str):
                # Remove script tags and other dangerous content
                value = re.sub(r'<script.*?</script>', '', value, flags=re.IGNORECASE | re.DOTALL)
                value = re.sub(r'javascript:', '', value, flags=re.IGNORECASE)
                value = value.strip()

            # Validate parameter length
            if isinstance(value, str) and len(value) > 10000:
                raise ValueError(f"Parameter {key} exceeds maximum length")

            sanitized_params[key] = value

        return sanitized_params

    async def async_act(self, **kwargs) -> ActionResult:
        """Execute action with input validation."""
        try:
            validated_params = self.validate_input(kwargs)
            return await self._secure_execute(validated_params)
        except ValueError as e:
            return ActionResult(
                success=False,
                error=f"Input validation failed: {str(e)}"
            )

    async def _secure_execute(self, params: Dict[str, Any]) -> ActionResult:
        """Override this method in subclasses."""
        raise NotImplementedError
```

## Appendix

### Environment Variables Reference

#### Core Configuration

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `LLM_MODEL_NAME` | LLM model identifier | None | Yes |
| `LLM_API_KEY` | LLM provider API key | None | Yes |
| `LLM_BASE_URL` | LLM API endpoint | Provider default | No |
| `LLM_TEMPERATURE` | Response randomness | "0.7" | No |
| `LLM_PROVIDER` | LLM provider name | "openai" | No |

#### Memory Configuration

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `MEMORY_STORE_POSTGRES_DSN` | PostgreSQL connection string | None | No |
| `CHROMA_DATA_PATH` | ChromaDB data directory | "./chroma_db" | No |
| `EMBEDDING_MODEL` | Embedding model name | "text-embedding-3-small" | No |

#### Tracing Configuration

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `LOGFIRE_WRITE_TOKEN` | Logfire authentication token | None | No |
| `START_TRACE_SERVER` | Enable trace server | "false" | No |
| `TRACE_SERVER_PORT` | Trace server port | "7079" | No |
| `OTLP_TRACES_ENDPOINT` | OTLP traces endpoint | None | No |

### Supported LLM Providers

| Provider | Models | Configuration |
|----------|--------|---------------|
| OpenAI | gpt-4, gpt-3.5-turbo, gpt-4-turbo | `llm_provider="openai"` |
| Anthropic | claude-3-sonnet, claude-3-opus | `llm_provider="anthropic"` |
| Ollama | llama2, mistral, codellama | `llm_provider="ollama"` |
| Azure OpenAI | gpt-4, gpt-35-turbo | `llm_provider="azure"` |
| Google | gemini-pro, palm-2 | `llm_provider="google"` |

### Supported Vector Databases

| Provider | Features | Configuration |
|----------|----------|---------------|
| ChromaDB | Local/Cloud, HNSW indexing | `provider="chroma"` |
| Pinecone | Cloud-native, high performance | `provider="pinecone"` |
| Weaviate | Open-source, GraphQL API | `provider="weaviate"` |
| Qdrant | High-performance, Rust-based | `provider="qdrant"` |

### Supported Embedding Models

| Provider | Models | Dimensions |
|----------|--------|------------|
| OpenAI | text-embedding-3-small | 1536 |
| OpenAI | text-embedding-3-large | 3072 |
| Ollama | nomic-embed-text | 768 |
| HuggingFace | sentence-transformers/all-MiniLM-L6-v2 | 384 |
| HuggingFace | sentence-transformers/all-mpnet-base-v2 | 768 |

### File Structure Reference

```
AWorld/
├── aworld/                          # Main framework package
│   ├── agents/                      # Agent implementations
│   │   ├── llm_agent.py            # Core LLM agent
│   │   ├── loop_llm_agent.py       # Loopable agent
│   │   ├── parallel_llm_agent.py   # Parallel execution agent
│   │   └── serial_llm_agent.py     # Serial execution agent
│   ├── core/                        # Core framework components
│   │   ├── agent/                   # Agent abstractions
│   │   │   ├── base.py             # BaseAgent class
│   │   │   └── swarm.py            # Multi-agent coordination
│   │   ├── context/                 # Context management
│   │   │   ├── base.py             # Context class
│   │   │   ├── processor/          # Context processors
│   │   │   └── prompts/            # Prompt templates
│   │   ├── tool/                    # Tool framework
│   │   │   ├── base.py             # Tool abstractions
│   │   │   └── action_factory.py   # Action registration
│   │   ├── memory.py               # Memory interfaces
│   │   ├── task.py                 # Task definitions
│   │   └── common.py               # Common data structures
│   ├── memory/                      # Memory system
│   │   ├── main.py                 # Memory factory and implementations
│   │   ├── vector/                 # Vector database integrations
│   │   ├── embeddings/             # Embedding providers
│   │   └── db/                     # Database backends
│   ├── tools/                       # Tool implementations
│   │   ├── mcp_tool/               # MCP protocol tools
│   │   ├── human/                  # Human interaction tools
│   │   └── template_tool.py        # Tool templates
│   ├── trace/                       # Tracing and observability
│   │   ├── opentelemetry/          # OpenTelemetry integration
│   │   ├── instrumentation/        # Auto-instrumentation
│   │   └── server/                 # Trace server
│   ├── runners/                     # Execution engines
│   │   ├── task_runner.py          # Task execution
│   │   ├── event_runner.py         # Event-driven execution
│   │   └── call_driven_runner.py   # Call-driven execution
│   ├── events/                      # Event system
│   │   ├── manager.py              # Event management
│   │   └── util.py                 # Event utilities
│   ├── config/                      # Configuration management
│   │   └── conf.py                 # Configuration classes
│   ├── cmd/                         # Command-line interface
│   │   ├── cli.py                  # CLI commands
│   │   └── web/                    # Web server
│   └── runner.py                    # Main runner interface
├── aworlddistributed/               # Distributed system components
│   ├── main.py                     # Distributed server
│   ├── client/                     # Client implementations
│   ├── aworldspace/                # Workspace management
│   └── mcp_servers/                # MCP server implementations
├── examples/                        # Usage examples
│   ├── gaia/                       # GAIA benchmark example
│   ├── imo/                        # IMO problem solving
│   ├── multi_agents/               # Multi-agent examples
│   └── browser_use/                # Browser automation
└── tests/                          # Test suite
    ├── memory/                     # Memory system tests
    ├── trace/                      # Tracing tests
    └── runners/                    # Runner tests
```

### Glossary

| Term | Definition |
|------|------------|
| **Agent** | An autonomous AI entity that can perceive, reason, and act in an environment |
| **Swarm** | A collection of agents working together to solve complex problems |
| **Task** | A complete execution unit with input, configuration, and expected output |
| **Context** | State management system that tracks agent execution and configuration |
| **Tool** | External capability that agents can use to interact with environments |
| **MCP** | Model Context Protocol for standardized tool integration |
| **Memory** | System for storing and retrieving agent experiences and knowledge |
| **Trace** | Observability system for monitoring and debugging agent execution |
| **Runner** | Execution engine that manages agent and task lifecycle |
| **Action** | Specific operation that can be performed by a tool |
| **Observation** | Information perceived by an agent from the environment |
| **Policy** | Decision-making logic that determines agent actions |

### Contributing Guidelines

#### Code Style

- Follow PEP 8 Python style guidelines
- Use type hints for all function parameters and return values
- Include comprehensive docstrings for all classes and methods
- Maintain line length under 79 characters
- Use meaningful variable and function names

#### Documentation Standards

- Include docstrings for all public classes and methods
- Provide usage examples for complex functionality
- Document configuration parameters and their effects
- Include error handling and edge case documentation

#### Testing Requirements

- Write unit tests for all new functionality
- Include integration tests for component interactions
- Provide performance benchmarks for critical paths
- Test error conditions and edge cases

#### Pull Request Process

1. Fork the repository and create a feature branch
2. Implement changes with comprehensive tests
3. Update documentation for any API changes
4. Run the full test suite and ensure all tests pass
5. Submit pull request with detailed description

### Support and Community

#### Getting Help

- **GitHub Issues**: Report bugs and request features
- **Discord Community**: Real-time discussion and support
- **Documentation**: Comprehensive guides and API reference
- **Examples**: Practical usage examples and tutorials

#### Contributing

- **Code Contributions**: Bug fixes, features, and improvements
- **Documentation**: Improve guides, examples, and API docs
- **Testing**: Add test coverage and performance benchmarks
- **Community**: Help other users and share knowledge

---

## Conclusion

AWorld represents a comprehensive framework for building sophisticated AI agent systems with multi-agent coordination, advanced memory management, and cloud-native scalability. The framework's modular architecture enables developers to:

1. **Build Individual Agents**: Create specialized agents with LLM integration and tool access
2. **Orchestrate Multi-Agent Systems**: Design complex agent topologies for collaborative problem-solving
3. **Scale Globally**: Deploy distributed systems with high concurrency and fault tolerance
4. **Monitor and Debug**: Use comprehensive tracing and observability features
5. **Evolve Continuously**: Enable agent self-improvement through memory and experience synthesis

The framework's design principles of modularity, extensibility, and observability make it suitable for both research applications and production deployments. Whether you're building simple chatbots or complex multi-agent research systems, AWorld provides the foundation for scalable and maintainable AI agent applications.

For the latest updates, examples, and community discussions, visit the [AWorld GitHub repository](https://github.com/inclusionAI/AWorld).

---

*This technical documentation is maintained by the AWorld development team. Last updated: 2025-08-14*

*For questions, contributions, or support, please contact: <EMAIL>*
