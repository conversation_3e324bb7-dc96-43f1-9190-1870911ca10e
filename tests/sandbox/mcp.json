{"mcpServers": {"amap-amap-sse": {"type": "sse", "url": "https://mcp.amap.com/sse?key=${AMAP_AMAP_SSE_KEY}", "timeout": 5.0, "sse_read_timeout": 300.0}, "tavily-mcp": {"type": "stdio", "command": "npx", "args": ["-y", "tavily-mcp@0.1.2"], "env": {"TAVILY_API_KEY": "tvly-dev-"}}, "aworldsearch_server": {"type": "function_tool"}, "aworldsearch_server1": {"command": "python", "args": ["-m", "mcp_servers.aworldsearch_server"], "env": {"AWORLD_SEARCH_URL": "${AWORLD_SEARCH_URL}", "AWORLD_SEARCH_TOTAL_NUM": "${AWORLD_SEARCH_TOTAL_NUM}", "AWORLD_SEARCH_SLICE_NUM": "${AWORLD_SEARCH_SLICE_NUM}", "AWORLD_SEARCH_DOMAIN": "${AWORLD_SEARCH_DOMAIN}", "AWORLD_SEARCH_SEARCHMODE": "${AWORLD_SEARCH_SEARCHMODE}", "AWORLD_SEARCH_SOURCE": "${AWORLD_SEARCH_SOURCE}", "AWORLD_SEARCH_UID": "${AWORLD_SEARCH_UID}"}}, "picsearch_server": {"command": "python", "args": ["-m", "mcp_servers.picsearch_server"], "env": {"PIC_SEARCH_URL": "${PIC_SEARCH_URL}", "PIC_SEARCH_TOTAL_NUM": "${PIC_SEARCH_TOTAL_NUM}", "PIC_SEARCH_SLICE_NUM": "${PIC_SEARCH_SLICE_NUM}", "PIC_SEARCH_DOMAIN": "${PIC_SEARCH_DOMAIN}", "PIC_SEARCH_SEARCHMODE": "${PIC_SEARCH_SEARCHMODE}", "PIC_SEARCH_SOURCE": "${PIC_SEARCH_SOURCE}"}}, "gen_audio_server": {"command": "python", "args": ["-m", "mcp_servers.gen_audio_server"], "env": {"AUDIO_TASK_URL": "${AUDIO_TASK_URL}", "AUDIO_QUERY_URL": "${AUDIO_QUERY_URL}", "AUDIO_APP_KEY": "${AUDIO_APP_KEY}", "AUDIO_SECRET": "${AUDIO_SECRET}", "AUDIO_SAMPLE_RATE": "${AUDIO_SAMPLE_RATE}", "AUDIO_AUDIO_FORMAT": "${AUDIO_AUDIO_FORMAT}", "AUDIO_TTS_VOICE": "${AUDIO_TTS_VOICE}", "AUDIO_TTS_SPEECH_RATE": "${AUDIO_TTS_SPEECH_RATE}", "AUDIO_TTS_VOLUME": "${AUDIO_TTS_VOLUME}", "AUDIO_TTS_PITCH": "${AUDIO_TTS_PITCH}", "AUDIO_VOICE_TYPE": "${AUDIO_VOICE_TYPE}"}}, "gen_video_server": {"command": "python", "args": ["-m", "mcp_servers.gen_video_server"], "env": {"DASHSCOPE_API_KEY": "${DASHSCOPE_API_KEY}", "DASHSCOPE_VIDEO_SUBMIT_URL": "${DASHSCOPE_VIDEO_SUBMIT_URL}", "DASHSCOPE_QUERY_BASE_URL": "${DASHSCOPE_QUERY_BASE_URL}", "DASHSCOPE_VIDEO_MODEL": "${DASHSCOPE_VIDEO_MODEL}", "DASHSCOPE_VIDEO_SIZE": "${DASHSCOPE_VIDEO_SIZE}", "DASHSCOPE_VIDEO_SLEEP_TIME": "${DASHSCOPE_VIDEO_SLEEP_TIME}", "DASHSCOPE_VIDEO_RETRY_TIMES": "${DASHSCOPE_VIDEO_RETRY_TIMES}"}}}}