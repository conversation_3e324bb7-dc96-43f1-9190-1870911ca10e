name: AWorld tests and examples

on:
  push:
    branches:
      - main
      - release-[0-9]+.[0-9]+

  pull_request:
    branches:
      - main
      - release-[0-9]+.[0-9]+

permissions:
  contents: read

jobs:
  build:

    runs-on: ubuntu-latest
    timeout-minutes: 60
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python 3.11
        uses: actions/setup-python@v3
        with:
          python-version: "3.11"
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip==25.0.1
          python -m pip install flake8 pytest pytest-asyncio python-dotenv
          python setup.py install
      #    - name: Lint with flake8
      #      run: |
      #        # stop the build if there are Python syntax errors or undefined names
      #        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
      #        # exit-zero treats all errors as warnings. The GitHub editor is 127 chars wide
      #        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
      - name: Run tests with pytest
        run: |
          pip install pytest
          pytest tests