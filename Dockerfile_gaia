FROM python:3.11-slim AS base

RUN mkdir -p /app/aworld

WORKDIR /app/aworld

RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/

RUN rm -fv /etc/apt/sources.list.d/* && \
    echo "deb [trusted=yes] https://mirrors.aliyun.com/debian bookworm main contrib non-free non-free-firmware" > /etc/apt/sources.list && \
    echo "deb [trusted=yes] https://mirrors.aliyun.com/debian bookworm-updates main contrib non-free non-free-firmware" >> /etc/apt/sources.list && \
    echo "deb [trusted=yes] https://mirrors.aliyun.com/debian bookworm-backports main contrib non-free non-free-firmware" >> /etc/apt/sources.list && \
    echo "deb [trusted=yes] https://mirrors.aliyun.com/debian-security bookworm-security main contrib non-free non-free-firmware" >> /etc/apt/sources.list && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

RUN apt-get update && apt-get install -y wget unzip openssh-client procps nodejs npm 

# Config Env
RUN npx playwright install chrome
RUN npm install @playwright/mcp @negokaz/excel-mcp-server

# GAIA Docker Image
FROM base AS gaia_env

# Install aworld
COPY ./aworld aworld
COPY ./setup.py setup.py
RUN python setup.py install

# Copy examples
COPY ./examples/ examples

# Create GAIA benchmark directory
RUN mkdir -p gaia-benchmark/fs && \
    mkdir -p gaia-benchmark/logs && \
    mkdir -p static

ENV PYTHONPATH=/app/aworld${PYTHONPATH:+:${PYTHONPATH}}
ENV GAIA_DATASET_PATH=/app/aworld/examples/gaia/GAIA/2023
ENV LOG_FILE_PATH=/app/aworld/gaia-benchmark/logs

CMD [ "python", "examples/gaia/gaia_agent_server.py" ]
