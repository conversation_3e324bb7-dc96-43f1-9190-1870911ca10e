<svg xmlns="http://www.w3.org/2000/svg" style="background: transparent; background-color: transparent;" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1403px" height="1263px" viewBox="-0.5 -0.5 1403 1263" content="&lt;mxfile&gt;&lt;diagram name=&quot;Context Management RAG Flow&quot; id=&quot;context-rag-flow&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;"><defs/><g><g><rect x="1" y="1" width="1400" height="1260" rx="189" ry="189" fill="none" stroke="#333333" stroke-width="3" pointer-events="all" style="stroke: light-dark(rgb(51, 51, 51), rgb(193, 193, 193));"/></g><g><rect x="411" y="21" width="580" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 578px; height: 1px; padding-top: 41px; margin-left: 412px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 24px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; word-wrap: normal; ">Context Rule</div></div></div></foreignObject><text x="701" y="48" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="24px" text-anchor="middle" font-weight="bold">Context Rule</text></switch></g></g><g><rect x="461" y="91" width="120" height="80" rx="12" ry="12" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" pointer-events="all" style="fill: light-dark(rgb(232, 245, 232), rgb(21, 33, 21)); stroke: light-dark(rgb(76, 175, 80), rgb(50, 135, 54));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 131px; margin-left: 462px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 14px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; word-wrap: normal; ">👤 User<br />Query</div></div></div></foreignObject><text x="521" y="135" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="14px" text-anchor="middle" font-weight="bold">👤 User...</text></switch></g></g><g><rect x="791" y="91" width="120" height="80" rx="12" ry="12" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" pointer-events="all" style="fill: light-dark(rgb(227, 242, 253), rgb(20, 33, 42)); stroke: light-dark(rgb(33, 150, 243), rgb(38, 139, 219));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 131px; margin-left: 792px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 14px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; word-wrap: normal; ">📄 Documents<br />Context History</div></div></div></foreignObject><text x="851" y="135" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="14px" text-anchor="middle" font-weight="bold">📄 Documents...</text></switch></g></g><g><rect x="291" y="231" width="800" height="200" rx="30" ry="30" fill="#fff3e0" stroke="#ff9800" stroke-width="3" pointer-events="all" style="fill: light-dark(rgb(255, 243, 224), rgb(36, 26, 9)); stroke: light-dark(rgb(255, 152, 0), rgb(175, 87, 0));"/></g><g><rect x="311" y="241" width="300" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 298px; height: 1px; padding-top: 256px; margin-left: 313px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; color: #FF9800; "><div style="display: inline-block; font-size: 18px; font-family: &quot;Helvetica&quot;; color: light-dark(#FF9800, #af5700); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; word-wrap: normal; ">Pre-Retrieval - Context Construct</div></div></div></foreignObject><text x="313" y="261" fill="#FF9800" font-family="&quot;Helvetica&quot;" font-size="18px" font-weight="bold">Pre-Retrieval - Context Construct</text></switch></g></g><g><rect x="331" y="281" width="200" height="120" rx="18" ry="18" fill="#fff3e0" stroke="#ff9800" stroke-width="2" pointer-events="all" style="fill: light-dark(rgb(255, 243, 224), rgb(36, 26, 9)); stroke: light-dark(rgb(255, 152, 0), rgb(175, 87, 0));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 198px; height: 1px; padding-top: 341px; margin-left: 333px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; "><div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><div><span style="color: light-dark(rgb(0, 0, 0), rgb(237, 237, 237)); font-size: 12px; background-color: transparent;">📖 </span><b>Query Rewrite</b></div><div><b><br /></b></div>• Fine-grained Data Cleaning<br />• Query Rewrite/Clarification</div></div></div></foreignObject><text x="333" y="344" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px">📖 Query Rewrite...</text></switch></g></g><g><rect x="541" y="491" width="300" height="80" rx="12" ry="12" fill="#f3e5f5" stroke="#9c27b0" stroke-width="2" pointer-events="all" style="fill: light-dark(rgb(243, 229, 245), rgb(45, 33, 47)); stroke: light-dark(rgb(156, 39, 176), rgb(245, 144, 255));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 298px; height: 1px; padding-top: 531px; margin-left: 542px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 14px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; word-wrap: normal; ">📋 Runtime Agent Context<br />(Prompt Template &amp; Retrieved Messages)</div></div></div></foreignObject><text x="691" y="535" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="14px" text-anchor="middle" font-weight="bold">📋 Runtime Agent Context...</text></switch></g></g><g><rect x="291" y="631" width="800" height="200" rx="30" ry="30" fill="#fce4ec" stroke="#e91e63" stroke-width="3" pointer-events="all" style="fill: light-dark(rgb(252, 228, 236), rgb(52, 31, 38)); stroke: light-dark(rgb(233, 30, 99), rgb(255, 128, 187));"/></g><g><rect x="311" y="641" width="290" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 288px; height: 1px; padding-top: 656px; margin-left: 313px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; color: #E91E63; "><div style="display: inline-block; font-size: 18px; font-family: &quot;Helvetica&quot;; color: light-dark(#E91E63, #ff80bb); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; word-wrap: normal; ">Post-Retrieval - Prompt Engine</div></div></div></foreignObject><text x="313" y="661" fill="#E91E63" font-family="&quot;Helvetica&quot;" font-size="18px" font-weight="bold">Post-Retrieval - Prompt Engine</text></switch></g></g><g><rect x="331" y="681" width="200" height="120" rx="18" ry="18" fill="#fce4ec" stroke="#e91e63" stroke-width="2" pointer-events="all" style="fill: light-dark(rgb(252, 228, 236), rgb(52, 31, 38)); stroke: light-dark(rgb(233, 30, 99), rgb(255, 128, 187));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 198px; height: 1px; padding-top: 741px; margin-left: 333px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><b>🔄 Rerank</b><div><b><br /></b>• Relevance scoring<br />• Message priority</div></div></div></div></foreignObject><text x="333" y="745" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">🔄 Rerank...</text></switch></g></g><g><rect x="591" y="681" width="200" height="120" rx="18" ry="18" fill="#fce4ec" stroke="#e91e63" stroke-width="2" pointer-events="all" style="fill: light-dark(rgb(252, 228, 236), rgb(52, 31, 38)); stroke: light-dark(rgb(233, 30, 99), rgb(255, 128, 187));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 198px; height: 1px; padding-top: 741px; margin-left: 593px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><b>✂️ Filter &amp; Truncate</b><div><b><br /></b>• Token budget control<br />• Context length limit<br />• Preserve key messages</div></div></div></div></foreignObject><text x="593" y="745" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">✂️ Filter &amp; Truncate...</text></switch></g></g><g><rect x="841" y="681" width="200" height="120" rx="18" ry="18" fill="#fce4ec" stroke="#e91e63" stroke-width="2" pointer-events="all" style="fill: light-dark(rgb(252, 228, 236), rgb(52, 31, 38)); stroke: light-dark(rgb(233, 30, 99), rgb(255, 128, 187));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 198px; height: 1px; padding-top: 741px; margin-left: 843px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><b>🗜️ Prompt Compression</b><div><b><br /></b>• LLM-based compression<br />• MapReduce-based compression<br />• Entropy-based compression</div></div></div></div></foreignObject><text x="843" y="745" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">🗜️ Prompt Compression...</text></switch></g></g><g><rect x="591" y="1041" width="200" height="80" rx="12" ry="12" fill="#ffebee" stroke="#f44336" stroke-width="2" pointer-events="all" style="fill: light-dark(rgb(255, 235, 238), rgb(44, 27, 30)); stroke: light-dark(rgb(244, 67, 54), rgb(255, 117, 106));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 198px; height: 1px; padding-top: 1081px; margin-left: 592px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 16px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; word-wrap: normal; ">🤖 LLM</div></div></div></foreignObject><text x="691" y="1086" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="16px" text-anchor="middle" font-weight="bold">🤖 LLM</text></switch></g></g><g><rect x="891" y="461" width="220" height="140" rx="21" ry="21" fill="#fff9c4" stroke="#f57f17" stroke-width="2" pointer-events="all" style="fill: light-dark(rgb(255, 249, 196), rgb(33, 28, 0)); stroke: light-dark(rgb(245, 127, 23), rgb(199, 97, 8));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 218px; height: 1px; padding-top: 531px; margin-left: 893px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; "><div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><b>⚙️ Configuration(Context Rule)<br /></b><br /><span style="color: light-dark(rgb(0, 0, 0), rgb(237, 237, 237));">• </span>token budget config<br />• max_model_len: 128000<br />• max_token_budget_ratio: 0.8<br />• compression_config<br />• retrieval_strategy: hybrid</div></div></div></foreignObject><text x="893" y="534" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px">⚙️ Configuration(Context Rule)...</text></switch></g></g><g><path d="M 521 171 L 521 201 L 691 201 L 691 220.76" fill="none" stroke="#666666" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(102, 102, 102), rgb(149, 149, 149));"/><path d="M 691 228.76 L 687 220.76 L 695 220.76 Z" fill="#666666" stroke="#666666" stroke-width="2" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(102, 102, 102), rgb(149, 149, 149)); stroke: light-dark(rgb(102, 102, 102), rgb(149, 149, 149));"/></g><g><path d="M 851 171 L 851 201 L 691 201 L 691 220.76" fill="none" stroke="#666666" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(102, 102, 102), rgb(149, 149, 149));"/><path d="M 691 228.76 L 687 220.76 L 695 220.76 Z" fill="#666666" stroke="#666666" stroke-width="2" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(102, 102, 102), rgb(149, 149, 149)); stroke: light-dark(rgb(102, 102, 102), rgb(149, 149, 149));"/></g><g><path d="M 691 431 L 691 480.76" fill="none" stroke="#9c27b0" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(156, 39, 176), rgb(245, 144, 255));"/><path d="M 691 488.76 L 687 480.76 L 695 480.76 Z" fill="#9c27b0" stroke="#9c27b0" stroke-width="2" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(156, 39, 176), rgb(245, 144, 255)); stroke: light-dark(rgb(156, 39, 176), rgb(245, 144, 255));"/></g><g><path d="M 691 571 L 691 620.76" fill="none" stroke="#9c27b0" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(156, 39, 176), rgb(245, 144, 255));"/><path d="M 691 628.76 L 687 620.76 L 695 620.76 Z" fill="#9c27b0" stroke="#9c27b0" stroke-width="2" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(156, 39, 176), rgb(245, 144, 255)); stroke: light-dark(rgb(156, 39, 176), rgb(245, 144, 255));"/></g><g><path d="M 691 831 L 691 920.76" fill="none" stroke="#e91e63" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(233, 30, 99), rgb(255, 128, 187));"/><path d="M 691 928.76 L 687 920.76 L 695 920.76 Z" fill="#e91e63" stroke="#e91e63" stroke-width="2" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(233, 30, 99), rgb(255, 128, 187)); stroke: light-dark(rgb(233, 30, 99), rgb(255, 128, 187));"/></g><g><path d="M 691 1011 L 691.5 1032 L 691.57 1030.78" fill="none" stroke="#e91e63" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(233, 30, 99), rgb(255, 128, 187));"/><path d="M 691.12 1038.77 L 687.57 1030.56 L 695.56 1031 Z" fill="#e91e63" stroke="#e91e63" stroke-width="2" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(233, 30, 99), rgb(255, 128, 187)); stroke: light-dark(rgb(233, 30, 99), rgb(255, 128, 187));"/></g><g><rect x="971" y="91" width="100" height="80" rx="12" ry="12" fill="#e8eaf6" stroke="#3f51b5" stroke-width="2" pointer-events="all" style="fill: light-dark(rgb(232, 234, 246), rgb(33, 35, 45)); stroke: light-dark(rgb(63, 81, 181), rgb(145, 161, 247));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 98px; height: 1px; padding-top: 131px; margin-left: 973px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; word-wrap: normal; "><div style="text-align: center;"><span style="background-color: transparent;">🗄️ Database</span></div><div style="text-align: center;"><span style="color: light-dark(rgb(0, 0, 0), rgb(237, 237, 237));"><span style="color: light-dark(rgb(0, 0, 0), rgb(237, 237, 237)); font-weight: 400; background-color: transparent;">•</span><span style="color: light-dark(rgb(0, 0, 0), rgb(237, 237, 237)); font-weight: 400; background-color: transparent;"> </span>Vector-based</span></div><div><font color="#000000" style="color: light-dark(rgb(0, 0, 0), rgb(237, 237, 237));"><span style="color: light-dark(rgb(0, 0, 0), rgb(237, 237, 237)); font-weight: 400; background-color: transparent;">•</span><span style="color: light-dark(rgb(0, 0, 0), rgb(237, 237, 237)); font-weight: 400; background-color: transparent;"> </span>File-based</font></div></div></div></div></foreignObject><text x="973" y="135" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" font-weight="bold">🗄️ Database...</text></switch></g></g><g><path d="M 911 131 L 962.88 131" fill="none" stroke="#3f51b5" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke" style="stroke: light-dark(rgb(63, 81, 181), rgb(145, 161, 247));"/><path d="M 969.88 131 L 962.88 134.5 L 962.88 127.5 Z" fill="#3f51b5" stroke="#3f51b5" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(63, 81, 181), rgb(145, 161, 247)); stroke: light-dark(rgb(63, 81, 181), rgb(145, 161, 247));"/></g><g><rect x="591" y="281" width="200" height="120" rx="18" ry="18" fill="#fff3e0" stroke="#ff9800" stroke-width="2" pointer-events="all" style="fill: light-dark(rgb(255, 243, 224), rgb(36, 26, 9)); stroke: light-dark(rgb(255, 152, 0), rgb(175, 87, 0));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 198px; height: 1px; padding-top: 341px; margin-left: 593px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; "><div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><b><span style="color: light-dark(rgb(0, 0, 0), rgb(237, 237, 237)); font-size: 12px;">📚 </span><span style="font-size: 12px; background-color: transparent;">Structured Memory</span></b><div><span style="font-size: 12px;"><b><br /></b></span><div style="font-size: 12px;"><font>1. Structured memory storage</font></div><div style="font-size: 12px;"><font>2. Sliding window for recent</font></div><div style="font-size: 12px;"><font>conversations</font></div><div style="font-size: 12px;"><font>3. Dynamic memory summarization</font></div><div style="font-size: 12px;"><font>4. Long conversation retrieval (RAG)</font></div></div></div></div></div></foreignObject><text x="593" y="344" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px">📚 Structured Memory...</text></switch></g></g><g><rect x="891" y="841" width="220" height="140" rx="21" ry="21" fill="#fff9c4" stroke="#f57f17" stroke-width="2" pointer-events="all" style="fill: light-dark(rgb(255, 249, 196), rgb(33, 28, 0)); stroke: light-dark(rgb(245, 127, 23), rgb(199, 97, 8));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 218px; height: 1px; padding-top: 911px; margin-left: 893px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; "><div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><div><span style="background-color: transparent;"><b>📊 </b></span><b>Performance Metrics</b></div><div><b><br /></b></div><div>• Context compression ratio</div><div>• Response quality retention</div><div>• Processing speed improvement</div><div>• Memory usage optimization</div></div></div></div></foreignObject><text x="893" y="914" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px">📊 Performance Metrics...</text></switch></g></g><g><rect x="591" y="1151" width="200" height="80" rx="12" ry="12" fill="#ffebee" stroke="#f44336" stroke-width="2" pointer-events="all" style="fill: light-dark(rgb(255, 235, 238), rgb(44, 27, 30)); stroke: light-dark(rgb(244, 67, 54), rgb(255, 117, 106));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 198px; height: 1px; padding-top: 1191px; margin-left: 592px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 16px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; word-wrap: normal; "><font style="color: light-dark(rgb(0, 0, 0), rgb(237, 237, 237));"><span style="font-size: 11px; font-weight: 400; text-align: left; color: light-dark(rgb(63, 63, 63), rgb(183, 183, 183));">⚡ </span>Action</font><div><font style="color: light-dark(rgb(0, 0, 0), rgb(237, 237, 237));">(Tools, Agents)</font></div></div></div></div></foreignObject><text x="691" y="1196" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="16px" text-anchor="middle" font-weight="bold">⚡ Action...</text></switch></g></g><g><path d="M 691 1121 L 691 1141 L 691 1131 L 691 1140.76" fill="none" stroke="#e91e63" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(233, 30, 99), rgb(255, 128, 187));"/><path d="M 691 1148.76 L 687 1140.76 L 695 1140.76 Z" fill="#e91e63" stroke="#e91e63" stroke-width="2" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(233, 30, 99), rgb(255, 128, 187)); stroke: light-dark(rgb(233, 30, 99), rgb(255, 128, 187));"/></g><g><rect x="541" y="931" width="300" height="80" rx="12" ry="12" fill="#f3e5f5" stroke="#9c27b0" stroke-width="2" pointer-events="all" style="fill: light-dark(rgb(243, 229, 245), rgb(45, 33, 47)); stroke: light-dark(rgb(156, 39, 176), rgb(245, 144, 255));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 298px; height: 1px; padding-top: 971px; margin-left: 542px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 14px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; word-wrap: normal; "><font style="color: light-dark(rgb(0, 0, 0), rgb(237, 237, 237));"><font>📝 Optimized Prompt </font><span style="background-color: transparent;">&amp; Final Messages</span></font></div></div></div></foreignObject><text x="691" y="975" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="14px" text-anchor="middle" font-weight="bold">📝 Optimized Prompt &amp; Final Messages</text></switch></g></g><g><path d="M 691 911 L 891 911" fill="none" stroke="#d79b00" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke" style="stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/></g><g><rect x="851" y="281" width="200" height="120" rx="18" ry="18" fill="#fff3e0" stroke="#ff9800" stroke-width="2" pointer-events="all" style="fill: light-dark(rgb(255, 243, 224), rgb(36, 26, 9)); stroke: light-dark(rgb(255, 152, 0), rgb(175, 87, 0));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 198px; height: 1px; padding-top: 341px; margin-left: 853px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; "><div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><b style="font-size: 12px;">🗂️ Historical File Storage</b><div style="font-size: 12px;"><b><br /></b>• Store tools result as files<br />• Extract current query<br />• File-based retrieval</div></div></div></div></foreignObject><text x="853" y="344" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px">🗂️ Historical File Storage...</text></switch></g></g><g><path d="M 841 531 L 891 531" fill="none" stroke="#d79b00" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke" style="stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>