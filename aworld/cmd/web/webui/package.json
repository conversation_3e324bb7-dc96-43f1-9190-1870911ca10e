{"name": "Aworld-UI", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/x": "^1.4.0", "@xyflow/react": "^12.8.1", "antd": "^5.26.0", "antd-style": "^3.7.1", "dagre": "^0.8.5", "mermaid": "^11.7.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^10.1.0", "react-router-dom": "^6.30.1", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/dagre": "^0.7.53", "@types/node": "^24.0.4", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "less": "^4.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}, "repository": "**************:inclusionAI/AWorld.git"}