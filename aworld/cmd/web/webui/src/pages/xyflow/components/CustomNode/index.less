.react-flow__node-customNode {
  border-radius: 6px;

  .custom-node {
    // background: #fadddb;
    // border: 2px solid #E6A5AD;
    border-radius: 4px;
    padding: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    min-width: 200px;
    max-width: 360px;
    &-header {
      font-weight: bold;
      // color: #d58690;
      border-bottom: 1px solid #eee;
      padding-bottom: 5px;
      margin-bottom: 5px;
    }

    &-content {
      color: #666;
      font-size: 12px;
      .custom-node-io {
        font-size: 12px;
        margin-top: 5px;
      }
    }
  }
}
