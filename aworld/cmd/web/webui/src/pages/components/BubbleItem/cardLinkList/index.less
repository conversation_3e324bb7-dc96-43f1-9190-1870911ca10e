.cardwrap {
  background-color: #eee;
  border-radius: 10px;
  padding: 10px;
  position: relative;
  .btn-workspace {
    position: absolute;
    top: -38px;
    right: -6px;
  }
  .card-length {
    font-size: 14px;
    color: #333;
    .ant-tag {
      max-width: 480px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      padding: 0 10px;
      border-radius: 8px;
      line-height: 24px;
    }
    .check-icon {
      color: #1890ff;
      margin-right: 8px;
      font-size: 16px;
    }
  }
}

.cardbox {
  // width: 668px;
  // width: 648px;
  overflow-x: auto;
  margin-top: 10px;
  .card-item {
    width: 175px;
    min-width: 175px;
    // margin-bottom: 16px;
    .ant-card-head {
      padding: 0 14px;
      min-height: 50px;
    }
    .ant-card-body {
      padding: 10px 14px 12px;
      .desc {
        margin-bottom: 0;
      }
    }
    & + .card-item {
      margin-left: 6px;
    }
  }
}
