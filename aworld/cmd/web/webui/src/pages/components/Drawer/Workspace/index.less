.workspacebox {
  width: 100%;
  box-sizing: border-box;
  .btn {
    color: #555;
    height: 28px;
    background-color: #daffd5;
    border-radius: 10px;
    position: fixed;
    top: 14px;
    right: 380px;
    &:hover {
      color: #555 !important;
      border: 1px solid #daffd5 !important;
      background-color: #f6ffed !important;
    }
  }
  &.border,
  .border {
    border: 1px solid #c1c1c1;
    border-radius: 10px;
  }
  .tabbox {
    width: 100%;
    box-sizing: border-box;
    margin-bottom: 12px;
    .num {
      width: 30px;
      height: 30px;
      text-align: center;
      line-height: 30px;
      border-radius: 50%;
      margin-right: 10px;
      background-color: #efefef;
    }
    .tab {
      width: 29%;
      padding: 5px 10px;
      cursor: pointer;
      &.active {
        .num {
          background-color: #c4efa6;
          color: #555;
        }
      }

      .name {
        font-size: 14px;
      }
      .desc {
        font-size: 12px;
        color: #999;
      }
    }
  }
  .listwrap {
    background-color: #fafafa;
    .title {
      text-align: center;
      line-height: 40px;
      border-bottom: 1px solid #a7a7a7;
    }
    .listbox {
      .list {
        padding: 10px 14px;
        .name {
          font-size: 14px;
          margin-bottom: 3px;
          display: flex;
          align-items: center;
          &::before {
            display: inline-block;
            width: 12px;
            height: 12px;
            margin-right: 5px;
            border-radius: 50%;
            border: 1px solid #999;
            background-color: #d8d8d8;
          }
        }
        .desc,
        .link {
          color: #999;
          font-size: 12px;
        }
        .desc {
          margin-bottom: 0;
        }
        &:not(:last-child) {
          border-bottom: 1px solid #a7a7a7;
        }
      }
    }
  }
}
