.traceXYbox {
  @box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  @border-radius: 8px;
  @transition: all 0.3s ease;
  @text-color: #222;
  @border-color: #d9d9d9;
  @light-bg: #f8f9fa;
  @node-bg: linear-gradient(135deg, #fff, #f8f8f8);
  @primary-color: #1890ff;

  width: 80%;
  max-width: 700px;
  height: 100%;
  position: relative;
  top: -20px;
  background: @light-bg;
  border-radius: @border-radius;
  box-shadow: @box-shadow;
  overflow: hidden;


  .react-flow__node {
    width: 300px;
    min-width: 14.5%;
    text-align: center;
    max-width: 30%;
    @node-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    @node-hover-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    @node-selected-shadow: 0 0 0 2px fade(@primary-color, 20%);
    border: 1px solid @border-color;
    border-radius: @border-radius;
    // padding: 12px;
    background: @node-bg;
    box-shadow: @node-shadow;
    font-size: 10px;
    // transition: @transition;
    margin-bottom: 25px;

    &:hover {
      box-shadow: @node-hover-shadow;
      transform: translateY(-2px);
    }

    &-selected {
      border-color: @primary-color;
      box-shadow: @node-selected-shadow;
    }
    .desc {
      margin: 0;
      font-size: 12px;
    }
  }
  .react-flow__handle{
    background-color: #ccc;
  }
  .react-flow__edge-path {
    stroke: #ddd;
    stroke-width: 2;
    animation: dashdraw 0.5s linear;
  }


  .react-flow__controls {
    box-shadow: @box-shadow;
    border-radius: 4px;
    overflow: hidden;
  }

  .trace-id {
    position: absolute;
    bottom: 15px;
    right: 15px;
    background: rgba(255, 255, 255, 0.9);
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 10px;
    color: #666;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    border: 1px solid #eee;
  }

  @keyframes dashdraw {
    from {
      stroke-dashoffset: 100;
    }
  }
}

// .ant-tooltip-content {
// width: 420px;
// }
.Tooltipbox {
  padding: 5px 8px;
  .summary {
    margin: 0;
    line-height: 1.4;
    font-size: 12px;
    text-align: left;
  }
  pre {
    white-space: pre-wrap;
    word-break: break-word;
    word-wrap: break-word;
  }
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #888;
}

//edge click no changes
.virtual-node-edge,
.node-edge {
  &:hover,
  &-selected {
    box-shadow: none !important;
    transform: none !important;
    border-color: transparent !important;
  }
  pointer-events: none !important;
}

//virtual-node hidden handle
// .react-flow__handle {
//   background-color: #999;
//   &.virtual-handle-target {
//     width: 0px;
//     height: 0px;
//     min-width: 0;
//     min-height: 0;
//     border: none;
//   }
// }
