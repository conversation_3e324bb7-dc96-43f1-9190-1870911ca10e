.welcome-container {
  display: flex;
  justify-content: center;
  background-color: #ffffff;
  align-items: center;
  position: relative;
  bottom: 50px;
}

.content {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.logo-title-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-bottom: 8px;

  img {
    transition: transform 0.2s ease;

    &:hover {
      transform: scale(1.1);
    }
  }

  .aworld-link {
    color: inherit;
    text-decoration: none;
    transition: color 0.2s ease;

    &:hover {
      color: #1677ff;
    }
  }
}

.input-area {
  position: relative;
  width: 100%;
  margin-top: 24px;
}

.text-input {
  border-radius: 20px;
  padding: 12px 50px 50px 20px;
  border: 1px solid #d9d9d9;
  font-size: 16px;
}

.submit-button {
  position: absolute;
  right: 12px;
  bottom: 12px;
  width: 40px !important;
  height: 40px !important;
  background-color: #000000;
  border: none;
  transition: opacity 0.2s;
}

.submit-button:hover,
.submit-button:focus {
  background-color: rgba(0, 0, 0, 0.7) !important;
}

.submit-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: rgba(0, 0, 0, 0.1) !important;
}

.submit-button:disabled:hover,
.submit-button:disabled:focus {
  opacity: 0.5;
  background-color: rgba(0, 0, 0, 0.1) !important;
}

.controls-area {
  width: 100%;
  margin-top: 20px;
}


.model-select {
  width: 100%;
  // width: fit-content;
  height: 44px;
  border-radius: 50px;

  .ant-select-selector {
    border-radius: 12px !important;
    padding-left: 12px !important;
    border: 1px solid #d9d9d9 !important;
  }

  .ant-select-selection-item {
    padding-right: 24px !important;
  }

  .ant-select-arrow {
    right: 15px;
  }
}

.select-item {
  line-height: 30px;

  small {
    margin-left: 10px;
    color: #b8b8b8;
    font-weight: normal;
  }

  .icon-right {
    color: #d9d9d9;
  }
}