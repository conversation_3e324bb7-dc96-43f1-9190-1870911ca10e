var Wd=Object.defineProperty;var Vd=(e,t,n)=>t in e?Wd(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var wt=(e,t,n)=>Vd(e,typeof t!="symbol"?t+"":t,n);import{r as d,R as P,g as Ti,a as ti,b as Pa,j as A}from"./index-C7nkBYbk.js";import{I as je,_ as ae,a as He,i as Ud,K as It,u as Xd,B as qe,c as zl,b as Dl,d as te,e as B,f as et,p as rn,g as ve,C as on,h as Gd,j as Ta,k as it,P as Bl,l as Fl,m as $n,n as Hl,D as qd,R as an,o as xt,q as tt,r as H,s as Oi,t as Ht,v as Yn,w as Ve,x as Wt,y as Yd,z as Oa,A as Kd,E as Ri,F as Zd,G as Jd,H as At,S as Wl,J as Vl,L as sn,M as En,N as Ni,O as Ul,Q as Xl,T as eo,U as ki,V as Gl,W as ql,X as Yl,Y as Kl,Z as Qd,$ as ef,a0 as tf,a1 as nf,a2 as Mi,a3 as ji,a4 as Zl,a5 as Kn,a6 as Un,a7 as rf,a8 as of,a9 as af,aa as sf,ab as lf,ac as Li,ad as bt,ae as ln,af as Zn,ag as to,ah as ni,ai as cf,aj as uf,ak as df,al as Ee,am as ff,an as tn,ao as st,ap as pf,aq as Ra,ar as In,as as Jl,at as Fr,au as Ql,av as mf,aw as Pn,ax as Na,ay as hf,az as vn,aA as Rt,aB as Jn,aC as Hr,aD as ec,aE as gf,aF as vf,aG as ri,aH as _i,aI as tc,aJ as yf,aK as bf,aL as xf,aM as nc,aN as Ai,aO as Cf,aP as Sf,aQ as rc,aR as wf,aS as $f,aT as ka,aU as Ef,aV as If,aW as oc,aX as Pf,aY as oi,aZ as ic,a_ as Qt,a$ as Ma,b0 as Tf,b1 as qt,b2 as at,b3 as ac,b4 as sc,b5 as zi,b6 as ot,b7 as Di,b8 as ja,b9 as Of,ba as lc,bb as Rf,bc as Nf,bd as cc,be as kf,bf as Mf,bg as jf,bh as Lf,bi as _f,bj as uc,bk as Af,bl as zf,bm as Df,bn as ct,bo as Bf,bp as Ff,bq as Hf,br as Wf,bs as Vf,bt as yr,bu as br,bv as La,bw as Uf,bx as Xf,by as Gf,bz as qf,bA as Yf,bB as Kf}from"./index-CcSzV6ZK.js";var Zf={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 244c176.18 0 319 142.82 319 319v233a32 32 0 01-32 32H225a32 32 0 01-32-32V563c0-176.18 142.82-319 319-319zM484 68h56a8 8 0 018 8v96a8 8 0 01-8 8h-56a8 8 0 01-8-8V76a8 8 0 018-8zM177.25 191.66a8 8 0 0111.32 0l67.88 67.88a8 8 0 010 11.31l-39.6 39.6a8 8 0 01-11.31 0l-67.88-67.88a8 8 0 010-11.31l39.6-39.6zm669.6 0l39.6 39.6a8 8 0 010 11.3l-67.88 67.9a8 8 0 01-11.32 0l-39.6-39.6a8 8 0 010-11.32l67.89-67.88a8 8 0 0111.31 0zM192 892h640a32 32 0 0132 32v24a8 8 0 01-8 8H168a8 8 0 01-8-8v-24a32 32 0 0132-32zm148-317v253h64V575h-64z"}}]},name:"alert",theme:"filled"},Jf=function(t,n){return d.createElement(je,ae({},t,{ref:n,icon:Zf}))},Qf=d.forwardRef(Jf),ep={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M868 545.5L536.1 163a31.96 31.96 0 00-48.3 0L156 545.5a7.97 7.97 0 006 13.2h81c4.6 0 9-2 12.1-5.5L474 300.9V864c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V300.9l218.9 252.3c3 3.5 7.4 5.5 12.1 5.5h81c6.8 0 10.5-8 6-13.2z"}}]},name:"arrow-up",theme:"outlined"},tp=function(t,n){return d.createElement(je,ae({},t,{ref:n,icon:ep}))},dc=d.forwardRef(tp),np={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M682 455V311l-76 76v68c-.1 50.7-42 92.1-94 92a95.8 95.8 0 01-52-15l-54 55c29.1 22.4 65.9 36 106 36 93.8 0 170-75.1 170-168z"}},{tag:"path",attrs:{d:"M833 446h-60c-4.4 0-8 3.6-8 8 0 140.3-113.7 254-254 254-63 0-120.7-23-165-61l-54 54a334.01 334.01 0 00179 81v102H326c-13.9 0-24.9 14.3-25 32v36c.1 4.4 2.9 8 6 8h408c3.2 0 6-3.6 6-8v-36c0-17.7-11-32-25-32H547V782c165.3-17.9 294-157.9 294-328 0-4.4-3.6-8-8-8zm13.1-377.7l-43.5-41.9a8 8 0 00-11.2.1l-129 129C634.3 101.2 577 64 511 64c-93.9 0-170 75.3-170 168v224c0 6.7.4 13.3 1.2 19.8l-68 68A252.33 252.33 0 01258 454c-.2-4.4-3.8-8-8-8h-60c-4.4 0-8 3.6-8 8 0 53 12.5 103 34.6 147.4l-137 137a8.03 8.03 0 000 11.3l42.7 42.7c3.1 3.1 8.2 3.1 11.3 0L846.2 79.8l.1-.1c3.1-3.2 3-8.3-.2-11.4zM417 401V232c0-50.6 41.9-92 94-92 46 0 84.1 32.3 92.3 74.7L417 401z"}}]},name:"audio-muted",theme:"outlined"},rp=function(t,n){return d.createElement(je,ae({},t,{ref:n,icon:np}))},op=d.forwardRef(rp),ip={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M842 454c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8 0 140.3-113.7 254-254 254S258 594.3 258 454c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8 0 168.7 126.6 307.9 290 327.6V884H326.7c-13.7 0-24.7 14.3-24.7 32v36c0 4.4 2.8 8 6.2 8h407.6c3.4 0 6.2-3.6 6.2-8v-36c0-17.7-11-32-24.7-32H548V782.1c165.3-18 294-158 294-328.1zM512 624c93.9 0 170-75.2 170-168V232c0-92.8-76.1-168-170-168s-170 75.2-170 168v224c0 92.8 76.1 168 170 168zm-94-392c0-50.6 41.9-92 94-92s94 41.4 94 92v224c0 50.6-41.9 92-94 92s-94-41.4-94-92V232z"}}]},name:"audio",theme:"outlined"},ap=function(t,n){return d.createElement(je,ae({},t,{ref:n,icon:ip}))},sp=d.forwardRef(ap),lp={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M952 224h-52c-4.4 0-8 3.6-8 8v248h-92V304c0-4.4-3.6-8-8-8H232c-4.4 0-8 3.6-8 8v176h-92V232c0-4.4-3.6-8-8-8H72c-4.4 0-8 3.6-8 8v560c0 4.4 3.6 8 8 8h52c4.4 0 8-3.6 8-8V548h92v172c0 4.4 3.6 8 8 8h560c4.4 0 8-3.6 8-8V548h92v244c0 4.4 3.6 8 8 8h52c4.4 0 8-3.6 8-8V232c0-4.4-3.6-8-8-8zM296 368h88v288h-88V368zm432 288H448V368h280v288z"}}]},name:"box-plot",theme:"outlined"},cp=function(t,n){return d.createElement(je,ae({},t,{ref:n,icon:lp}))},up=d.forwardRef(cp),dp={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M899.1 869.6l-53-305.6H864c14.4 0 26-11.6 26-26V346c0-14.4-11.6-26-26-26H618V138c0-14.4-11.6-26-26-26H432c-14.4 0-26 11.6-26 26v182H160c-14.4 0-26 11.6-26 26v192c0 14.4 11.6 26 26 26h17.9l-53 305.6a25.95 25.95 0 0025.6 30.4h723c1.5 0 3-.1 4.4-.4a25.88 25.88 0 0021.2-30zM204 390h272V182h72v208h272v104H204V390zm468 440V674c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v156H416V674c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v156H202.8l45.1-260H776l45.1 260H672z"}}]},name:"clear",theme:"outlined"},fp=function(t,n){return d.createElement(je,ae({},t,{ref:n,icon:dp}))},pp=d.forwardRef(fp),mp={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M518.3 459a8 8 0 00-12.6 0l-112 141.7a7.98 7.98 0 006.3 12.9h73.9V856c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V613.7H624c6.7 0 10.4-7.7 6.3-12.9L518.3 459z"}},{tag:"path",attrs:{d:"M811.4 366.7C765.6 245.9 648.9 160 512.2 160S258.8 245.8 213 366.6C127.3 389.1 64 467.2 64 560c0 110.5 89.5 200 199.9 200H304c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8h-40.1c-33.7 0-65.4-13.4-89-37.7-23.5-24.2-36-56.8-34.9-90.6.9-26.4 9.9-51.2 26.2-72.1 16.7-21.3 40.1-36.8 66.1-43.7l37.9-9.9 13.9-36.6c8.6-22.8 20.6-44.1 35.7-63.4a245.6 245.6 0 0152.4-49.9c41.1-28.9 89.5-44.2 140-44.2s98.9 15.3 140 44.2c19.9 14 37.5 30.8 52.4 49.9 15.1 19.3 27.1 40.7 35.7 63.4l13.8 36.5 37.8 10C846.1 454.5 884 503.8 884 560c0 33.1-12.9 64.3-36.3 87.7a123.07 123.07 0 01-87.6 36.3H720c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h40.1C870.5 760 960 670.5 960 560c0-92.7-63.1-170.7-148.6-193.3z"}}]},name:"cloud-upload",theme:"outlined"},hp=function(t,n){return d.createElement(je,ae({},t,{ref:n,icon:mp}))},gp=d.forwardRef(hp),vp={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M505.7 661a8 8 0 0012.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V168c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"}}]},name:"download",theme:"outlined"},yp=function(t,n){return d.createElement(je,ae({},t,{ref:n,icon:vp}))},bp=d.forwardRef(yp),xp={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M257.7 752c2 0 4-.2 6-.5L431.9 722c2-.4 3.9-1.3 5.3-2.8l423.9-423.9a9.96 9.96 0 000-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2a33.5 33.5 0 009.4 29.8c6.6 6.4 14.9 9.9 23.8 9.9zm67.4-174.4L687.8 215l73.3 73.3-362.7 362.6-88.9 15.7 15.6-89zM880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z"}}]},name:"edit",theme:"outlined"},Cp=function(t,n){return d.createElement(je,ae({},t,{ref:n,icon:xp}))},Sp=d.forwardRef(Cp),wp={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M864 170h-60c-4.4 0-8 3.6-8 8v518H310v-73c0-6.7-7.8-10.5-13-6.3l-141.9 112a8 8 0 000 12.6l141.9 112c5.3 4.2 13 .4 13-6.3v-75h498c35.3 0 64-28.7 64-64V178c0-4.4-3.6-8-8-8z"}}]},name:"enter",theme:"outlined"},$p=function(t,n){return d.createElement(je,ae({},t,{ref:n,icon:wp}))},Ep=d.forwardRef($p),Ip={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.7c6 6 9.4 14.1 9.4 22.6V928c0 17.7-14.3 32-32 32H192c-17.7 0-32-14.3-32-32V96c0-17.7 14.3-32 32-32h424.7c8.5 0 16.7 3.4 22.7 9.4l215.2 215.3zM790.2 326L602 137.8V326h188.2zM575.34 477.84l-61.22 102.3L452.3 477.8a12 12 0 00-10.27-5.79h-38.44a12 12 0 00-6.4 1.85 12 12 0 00-3.75 16.56l82.34 130.42-83.45 132.78a12 12 0 00-1.84 6.39 12 12 0 0012 12h34.46a12 12 0 0010.21-5.7l62.7-101.47 62.3 101.45a12 12 0 0010.23 5.72h37.48a12 12 0 006.48-1.9 12 12 0 003.62-16.58l-83.83-130.55 85.3-132.47a12 12 0 001.9-6.5 12 12 0 00-12-12h-35.7a12 12 0 00-10.29 5.84z"}}]},name:"file-excel",theme:"filled"},Pp=function(t,n){return d.createElement(je,ae({},t,{ref:n,icon:Ip}))},Tp=d.forwardRef(Pp),Op={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.7L639.4 73.4c-6-6-14.2-9.4-22.7-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.6-9.4-22.6zM400 402c22.1 0 40 17.9 40 40s-17.9 40-40 40-40-17.9-40-40 17.9-40 40-40zm296 294H328c-6.7 0-10.4-7.7-6.3-12.9l99.8-127.2a8 8 0 0112.6 0l41.1 52.4 77.8-99.2a8 8 0 0112.6 0l136.5 174c4.3 5.2.5 12.9-6.1 12.9zm-94-370V137.8L790.2 326H602z"}}]},name:"file-image",theme:"filled"},Rp=function(t,n){return d.createElement(je,ae({},t,{ref:n,icon:Op}))},Np=d.forwardRef(Rp),kp={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.7c6 6 9.4 14.1 9.4 22.6V928c0 17.7-14.3 32-32 32H192c-17.7 0-32-14.3-32-32V96c0-17.7 14.3-32 32-32h424.7c8.5 0 16.7 3.4 22.7 9.4l215.2 215.3zM790.2 326L602 137.8V326h188.2zM426.13 600.93l59.11 132.97a16 16 0 0014.62 9.5h24.06a16 16 0 0014.63-9.51l59.1-133.35V758a16 16 0 0016.01 16H641a16 16 0 0016-16V486a16 16 0 00-16-16h-34.75a16 16 0 00-14.67 9.62L512.1 662.2l-79.48-182.59a16 16 0 00-14.67-9.61H383a16 16 0 00-16 16v272a16 16 0 0016 16h27.13a16 16 0 0016-16V600.93z"}}]},name:"file-markdown",theme:"filled"},Mp=function(t,n){return d.createElement(je,ae({},t,{ref:n,icon:kp}))},jp=d.forwardRef(Mp),Lp={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.7c6 6 9.4 14.1 9.4 22.6V928c0 17.7-14.3 32-32 32H192c-17.7 0-32-14.3-32-32V96c0-17.7 14.3-32 32-32h424.7c8.5 0 16.7 3.4 22.7 9.4l215.2 215.3zM790.2 326L602 137.8V326h188.2zM633.22 637.26c-15.18-.5-31.32.67-49.65 2.96-24.3-14.99-40.66-35.58-52.28-65.83l1.07-4.38 1.24-5.18c4.3-18.13 6.61-31.36 7.3-44.7.52-10.07-.04-19.36-1.83-27.97-3.3-18.59-16.45-29.46-33.02-30.13-15.45-.63-29.65 8-33.28 21.37-5.91 21.62-2.45 50.07 10.08 98.59-15.96 38.05-37.05 82.66-51.2 107.54-18.89 9.74-33.6 18.6-45.96 28.42-16.3 12.97-26.48 26.3-29.28 40.3-1.36 6.49.69 14.97 5.36 21.92 5.3 7.88 13.28 13 22.85 13.74 24.15 1.87 53.83-23.03 86.6-79.26 3.29-1.1 6.77-2.26 11.02-3.7l11.9-4.02c7.53-2.54 12.99-4.36 18.39-6.11 23.4-7.62 41.1-12.43 57.2-15.17 27.98 14.98 60.32 24.8 82.1 24.8 17.98 0 30.13-9.32 34.52-23.99 3.85-12.88.8-27.82-7.48-36.08-8.56-8.41-24.3-12.43-45.65-13.12zM385.23 765.68v-.36l.13-.34a54.86 54.86 0 015.6-10.76c4.28-6.58 10.17-13.5 17.47-20.87 3.92-3.95 8-7.8 12.79-12.12 1.07-.96 7.91-7.05 9.19-8.25l11.17-10.4-8.12 12.93c-12.32 19.64-23.46 33.78-33 43-3.51 3.4-6.6 5.9-9.1 7.51a16.43 16.43 0 01-2.61 1.42c-.41.17-.77.27-1.13.3a2.2 2.2 0 01-1.12-.15 2.07 2.07 0 01-1.27-1.91zM511.17 547.4l-2.26 4-1.4-4.38c-3.1-9.83-5.38-24.64-6.01-38-.72-15.2.49-24.32 5.29-24.32 6.74 0 9.83 10.8 10.07 27.05.22 14.28-2.03 29.14-5.7 35.65zm-5.81 58.46l1.53-4.05 2.09 3.8c11.69 21.24 26.86 38.96 43.54 51.31l3.6 2.66-4.39.9c-16.33 3.38-31.54 8.46-52.34 16.85 2.17-.88-21.62 8.86-27.64 11.17l-5.25 2.01 2.8-4.88c12.35-21.5 23.76-47.32 36.05-79.77zm157.62 76.26c-7.86 3.1-24.78.33-54.57-12.39l-7.56-3.22 8.2-.6c23.3-1.73 39.8-.45 49.42 3.07 4.1 1.5 6.83 3.39 8.04 5.55a4.64 4.64 0 01-1.36 6.31 6.7 6.7 0 01-2.17 1.28z"}}]},name:"file-pdf",theme:"filled"},_p=function(t,n){return d.createElement(je,ae({},t,{ref:n,icon:Lp}))},Ap=d.forwardRef(_p),zp={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.7c6 6 9.4 14.1 9.4 22.6V928c0 17.7-14.3 32-32 32H192c-17.7 0-32-14.3-32-32V96c0-17.7 14.3-32 32-32h424.7c8.5 0 16.7 3.4 22.7 9.4l215.2 215.3zM790.2 326L602 137.8V326h188.2zM468.53 760v-91.54h59.27c60.57 0 100.2-39.65 100.2-98.12 0-58.22-39.58-98.34-99.98-98.34H424a12 12 0 00-12 12v276a12 12 0 0012 12h32.53a12 12 0 0012-12zm0-139.33h34.9c47.82 0 67.19-12.93 67.19-50.33 0-32.05-18.12-50.12-49.87-50.12h-52.22v100.45z"}}]},name:"file-ppt",theme:"filled"},Dp=function(t,n){return d.createElement(je,ae({},t,{ref:n,icon:zp}))},Bp=d.forwardRef(Dp),Fp={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.7c6 6 9.4 14.1 9.4 22.6V928c0 17.7-14.3 32-32 32H192c-17.7 0-32-14.3-32-32V96c0-17.7 14.3-32 32-32h424.7c8.5 0 16.7 3.4 22.7 9.4l215.2 215.3zM790.2 326L602 137.8V326h188.2zM320 482a8 8 0 00-8 8v48a8 8 0 008 8h384a8 8 0 008-8v-48a8 8 0 00-8-8H320zm0 136a8 8 0 00-8 8v48a8 8 0 008 8h184a8 8 0 008-8v-48a8 8 0 00-8-8H320z"}}]},name:"file-text",theme:"filled"},Hp=function(t,n){return d.createElement(je,ae({},t,{ref:n,icon:Fp}))},Wp=d.forwardRef(Hp),Vp={icon:function(t,n){return{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M534 352V136H232v752h560V394H576a42 42 0 01-42-42z",fill:n}},{tag:"path",attrs:{d:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM602 137.8L790.2 326H602V137.8zM792 888H232V136h302v216a42 42 0 0042 42h216v494z",fill:t}}]}},name:"file",theme:"twotone"},Up=function(t,n){return d.createElement(je,ae({},t,{ref:n,icon:Vp}))},Xp=d.forwardRef(Up),Gp={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.7c6 6 9.4 14.1 9.4 22.6V928c0 17.7-14.3 32-32 32H192c-17.7 0-32-14.3-32-32V96c0-17.7 14.3-32 32-32h424.7c8.5 0 16.7 3.4 22.7 9.4l215.2 215.3zM790.2 326L602 137.8V326h188.2zM512 566.1l52.81 197a12 12 0 0011.6 8.9h31.77a12 12 0 0011.6-8.88l74.37-276a12 12 0 00.4-3.12 12 12 0 00-12-12h-35.57a12 12 0 00-11.7 9.31l-45.78 199.1-49.76-199.32A12 12 0 00528.1 472h-32.2a12 12 0 00-11.64 9.1L434.6 680.01 388.5 481.3a12 12 0 00-11.68-9.29h-35.39a12 12 0 00-3.11.41 12 12 0 00-8.47 14.7l74.17 276A12 12 0 00415.6 772h31.99a12 12 0 0011.59-8.9l52.81-197z"}}]},name:"file-word",theme:"filled"},qp=function(t,n){return d.createElement(je,ae({},t,{ref:n,icon:Gp}))},Yp=d.forwardRef(qp),Kp={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.7c6 6 9.4 14.1 9.4 22.6V928c0 17.7-14.3 32-32 32H192c-17.7 0-32-14.3-32-32V96c0-17.7 14.3-32 32-32h424.7c8.5 0 16.7 3.4 22.7 9.4l215.2 215.3zM790.2 326L602 137.8V326h188.2zM296 136v64h64v-64h-64zm64 64v64h64v-64h-64zm-64 64v64h64v-64h-64zm64 64v64h64v-64h-64zm-64 64v64h64v-64h-64zm64 64v64h64v-64h-64zm-64 64v64h64v-64h-64zm0 64v160h128V584H296zm48 48h32v64h-32v-64z"}}]},name:"file-zip",theme:"filled"},Zp=function(t,n){return d.createElement(je,ae({},t,{ref:n,icon:Kp}))},Jp=d.forwardRef(Zp),Qp={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM142.4 642.1L298.7 519a8.84 8.84 0 000-13.9L142.4 381.9c-5.8-4.6-14.4-.5-14.4 6.9v246.3a8.9 8.9 0 0014.4 7z"}}]},name:"menu-unfold",theme:"outlined"},em=function(t,n){return d.createElement(je,ae({},t,{ref:n,icon:Qp}))},fc=d.forwardRef(em),tm={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M464 512a48 48 0 1096 0 48 48 0 10-96 0zm200 0a48 48 0 1096 0 48 48 0 10-96 0zm-400 0a48 48 0 1096 0 48 48 0 10-96 0zm661.2-173.6c-22.6-53.7-55-101.9-96.3-143.3a444.35 444.35 0 00-143.3-96.3C630.6 75.7 572.2 64 512 64h-2c-60.6.3-119.3 12.3-174.5 35.9a445.35 445.35 0 00-142 96.5c-40.9 41.3-73 89.3-95.2 142.8-23 55.4-34.6 114.3-34.3 174.9A449.4 449.4 0 00112 714v152a46 46 0 0046 46h152.1A449.4 449.4 0 00510 960h2.1c59.9 0 118-11.6 172.7-34.3a444.48 444.48 0 00142.8-95.2c41.3-40.9 73.8-88.7 96.5-142 23.6-55.2 35.6-113.9 35.9-174.5.3-60.9-11.5-120-34.8-175.6zm-151.1 438C704 845.8 611 884 512 884h-1.7c-60.3-.3-120.2-15.3-173.1-43.5l-8.4-4.5H188V695.2l-4.5-8.4C155.3 633.9 140.3 574 140 513.7c-.4-99.7 37.7-193.3 107.6-263.8 69.8-70.5 163.1-109.5 262.8-109.9h1.7c50 0 98.5 9.7 144.2 28.9 44.6 18.7 84.6 45.6 119 80 34.3 34.3 61.3 74.4 80 119 19.4 46.2 29.1 95.2 28.9 145.8-.6 99.6-39.7 192.9-110.1 262.7z"}}]},name:"message",theme:"outlined"},nm=function(t,n){return d.createElement(je,ae({},t,{ref:n,icon:tm}))},rm=d.forwardRef(nm),om={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M779.3 196.6c-94.2-94.2-247.6-94.2-341.7 0l-261 260.8c-1.7 1.7-2.6 4-2.6 6.4s.9 4.7 2.6 6.4l36.9 36.9a9 9 0 0012.7 0l261-260.8c32.4-32.4 75.5-50.2 121.3-50.2s88.9 17.8 121.2 50.2c32.4 32.4 50.2 75.5 50.2 121.2 0 45.8-17.8 88.8-50.2 121.2l-266 265.9-43.1 43.1c-40.3 40.3-105.8 40.3-146.1 0-19.5-19.5-30.2-45.4-30.2-73s10.7-53.5 30.2-73l263.9-263.8c6.7-6.6 15.5-10.3 24.9-10.3h.1c9.4 0 18.1 3.7 24.7 10.3 6.7 6.7 10.3 15.5 10.3 24.9 0 9.3-3.7 18.1-10.3 24.7L372.4 653c-1.7 1.7-2.6 4-2.6 6.4s.9 4.7 2.6 6.4l36.9 36.9a9 9 0 0012.7 0l215.6-215.6c19.9-19.9 30.8-46.3 30.8-74.4s-11-54.6-30.8-74.4c-41.1-41.1-107.9-41-149 0L463 364 224.8 602.1A172.22 172.22 0 00174 724.8c0 46.3 18.1 89.8 50.8 122.5 33.9 33.8 78.3 50.7 122.7 50.7 44.4 0 88.8-16.9 122.6-50.7l309.2-309C824.8 492.7 850 432 850 367.5c.1-64.6-25.1-125.3-70.7-170.9z"}}]},name:"paper-clip",theme:"outlined"},im=function(t,n){return d.createElement(je,ae({},t,{ref:n,icon:om}))},pc=d.forwardRef(im),am={icon:function(t,n){return{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 632H136v-39.9l138.5-164.3 150.1 178L658.1 489 888 761.6V792zm0-129.8L664.2 396.8c-3.2-3.8-9-3.8-12.2 0L424.6 666.4l-144-170.7c-3.2-3.8-9-3.8-12.2 0L136 652.7V232h752v430.2z",fill:t}},{tag:"path",attrs:{d:"M424.6 765.8l-150.1-178L136 752.1V792h752v-30.4L658.1 489z",fill:n}},{tag:"path",attrs:{d:"M136 652.7l132.4-157c3.2-3.8 9-3.8 12.2 0l144 170.7L652 396.8c3.2-3.8 9-3.8 12.2 0L888 662.2V232H136v420.7zM304 280a88 88 0 110 176 88 88 0 010-176z",fill:n}},{tag:"path",attrs:{d:"M276 368a28 28 0 1056 0 28 28 0 10-56 0z",fill:n}},{tag:"path",attrs:{d:"M304 456a88 88 0 100-176 88 88 0 000 176zm0-116c15.5 0 28 12.5 28 28s-12.5 28-28 28-28-12.5-28-28 12.5-28 28-28z",fill:t}}]}},name:"picture",theme:"twotone"},sm=function(t,n){return d.createElement(je,ae({},t,{ref:n,icon:am}))},lm=d.forwardRef(sm),cm={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M623.6 316.7C593.6 290.4 554 276 512 276s-81.6 14.5-111.6 40.7C369.2 344 352 380.7 352 420v7.6c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V420c0-44.1 43.1-80 96-80s96 35.9 96 80c0 31.1-22 59.6-56.1 72.7-21.2 8.1-39.2 22.3-52.1 40.9-13.1 19-19.9 41.8-19.9 64.9V620c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8v-22.7a48.3 48.3 0 0130.9-44.8c59-22.7 97.1-74.7 97.1-132.5.1-39.3-17.1-76-48.3-103.3zM472 732a40 40 0 1080 0 40 40 0 10-80 0z"}}]},name:"question-circle",theme:"outlined"},um=function(t,n){return d.createElement(je,ae({},t,{ref:n,icon:cm}))},_a=d.forwardRef(um),dm={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M672 418H144c-17.7 0-32 14.3-32 32v414c0 17.7 14.3 32 32 32h528c17.7 0 32-14.3 32-32V450c0-17.7-14.3-32-32-32zm-44 402H188V494h440v326z"}},{tag:"path",attrs:{d:"M819.3 328.5c-78.8-100.7-196-153.6-314.6-154.2l-.2-64c0-6.5-7.6-10.1-12.6-6.1l-128 101c-4 3.1-3.9 9.1 0 12.3L492 318.6c5.1 4 12.7.4 12.6-6.1v-63.9c12.9.1 25.9.9 38.8 2.5 42.1 5.2 82.1 18.2 119 38.7 38.1 21.2 71.2 49.7 98.4 84.3 27.1 34.7 46.7 73.7 58.1 115.8a325.95 325.95 0 016.5 140.9h74.9c14.8-103.6-11.3-213-81-302.3z"}}]},name:"rotate-left",theme:"outlined"},fm=function(t,n){return d.createElement(je,ae({},t,{ref:n,icon:dm}))},pm=d.forwardRef(fm),mm={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M480.5 251.2c13-1.6 25.9-2.4 38.8-2.5v63.9c0 6.5 7.5 10.1 12.6 6.1L660 217.6c4-3.2 4-9.2 0-12.3l-128-101c-5.1-4-12.6-.4-12.6 6.1l-.2 64c-118.6.5-235.8 53.4-314.6 154.2A399.75 399.75 0 00123.5 631h74.9c-.9-5.3-1.7-10.7-2.4-16.1-5.1-42.1-2.1-84.1 8.9-124.8 11.4-42.2 31-81.1 58.1-115.8 27.2-34.7 60.3-63.2 98.4-84.3 37-20.6 76.9-33.6 119.1-38.8z"}},{tag:"path",attrs:{d:"M880 418H352c-17.7 0-32 14.3-32 32v414c0 17.7 14.3 32 32 32h528c17.7 0 32-14.3 32-32V450c0-17.7-14.3-32-32-32zm-44 402H396V494h440v326z"}}]},name:"rotate-right",theme:"outlined"},hm=function(t,n){return d.createElement(je,ae({},t,{ref:n,icon:mm}))},gm=d.forwardRef(hm),vm={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M847.9 592H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h605.2L612.9 851c-4.1 5.2-.4 13 6.3 13h72.5c4.9 0 9.5-2.2 12.6-6.1l168.8-214.1c16.5-21 1.6-51.8-25.2-51.8zM872 356H266.8l144.3-183c4.1-5.2.4-13-6.3-13h-72.5c-4.9 0-9.5 2.2-12.6 6.1L150.9 380.2c-16.5 21-1.6 51.8 25.1 51.8h696c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"swap",theme:"outlined"},ym=function(t,n){return d.createElement(je,ae({},t,{ref:n,icon:vm}))},Aa=d.forwardRef(ym),bm={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M762 164h-64c-4.4 0-8 3.6-8 8v688c0 4.4 3.6 8 8 8h64c4.4 0 8-3.6 8-8V172c0-4.4-3.6-8-8-8zm-508 0v72.4c0 9.5 4.2 18.4 11.4 24.5L564.6 512 265.4 763.1c-7.2 6.1-11.4 15-11.4 24.5V860c0 6.8 7.9 10.5 13.1 6.1L689 512 267.1 157.9A7.95 7.95 0 00254 164z"}}]},name:"vertical-left",theme:"outlined"},xm=function(t,n){return d.createElement(je,ae({},t,{ref:n,icon:bm}))},za=d.forwardRef(xm),Cm={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M326 164h-64c-4.4 0-8 3.6-8 8v688c0 4.4 3.6 8 8 8h64c4.4 0 8-3.6 8-8V172c0-4.4-3.6-8-8-8zm444 72.4V164c0-6.8-7.9-10.5-13.1-6.1L335 512l421.9 354.1c5.2 4.4 13.1.7 13.1-6.1v-72.4c0-9.4-4.2-18.4-11.4-24.5L459.4 512l299.2-251.1c7.2-6.1 11.4-15.1 11.4-24.5z"}}]},name:"vertical-right",theme:"outlined"},Sm=function(t,n){return d.createElement(je,ae({},t,{ref:n,icon:Cm}))},wm=d.forwardRef(Sm),$m={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M637 443H519V309c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v134H325c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h118v134c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V519h118c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8zm284 424L775 721c122.1-148.9 113.6-369.5-26-509-148-148.1-388.4-148.1-537 0-148.1 148.6-148.1 389 0 537 139.5 139.6 360.1 148.1 509 26l146 146c3.2 2.8 8.3 2.8 11 0l43-43c2.8-2.7 2.8-7.8 0-11zM696 696c-118.8 118.7-311.2 118.7-430 0-118.7-118.8-118.7-311.2 0-430 118.8-118.7 311.2-118.7 430 0 118.7 118.8 118.7 311.2 0 430z"}}]},name:"zoom-in",theme:"outlined"},Em=function(t,n){return d.createElement(je,ae({},t,{ref:n,icon:$m}))},Im=d.forwardRef(Em),Pm={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M637 443H325c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h312c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8zm284 424L775 721c122.1-148.9 113.6-369.5-26-509-148-148.1-388.4-148.1-537 0-148.1 148.6-148.1 389 0 537 139.5 139.6 360.1 148.1 509 26l146 146c3.2 2.8 8.3 2.8 11 0l43-43c2.8-2.7 2.8-7.8 0-11zM696 696c-118.8 118.7-311.2 118.7-430 0-118.7-118.8-118.7-311.2 0-430 118.8-118.7 311.2-118.7 430 0 118.7 118.8 118.7 311.2 0 430z"}}]},name:"zoom-out",theme:"outlined"},Tm=function(t,n){return d.createElement(je,ae({},t,{ref:n,icon:Pm}))},Om=d.forwardRef(Tm);const Rm="1.4.0";function Nm(){const[e,t]=d.useState([]),n=d.useCallback(r=>(t(o=>[].concat(He(o),[r])),()=>{t(o=>o.filter(i=>i!==r))}),[]);return[e,n]}const km=new It("antFadeIn",{"0%":{opacity:0},"100%":{opacity:1}}),Mm=new It("antFadeOut",{"0%":{opacity:1},"100%":{opacity:0}}),Bi=(e,t=!1)=>{const{antCls:n}=e,r=`${n}-fade`,o=t?"&":"";return[Ud(r,km,Mm,e.motionDurationMid,t),{[`
        ${o}${r}-enter,
        ${o}${r}-appear
      `]:{opacity:0,animationTimingFunction:"linear"},[`${o}${r}-leave`]:{animationTimingFunction:"linear"}}]};function xo(e){return!!(e!=null&&e.then)}const mc=e=>{const{type:t,children:n,prefixCls:r,buttonProps:o,close:i,autoFocus:a,emitEvent:s,isSilent:l,quitOnNullishReturnValue:c,actionFn:u}=e,f=d.useRef(!1),m=d.useRef(null),[p,v]=Xd(!1),h=(...b)=>{i==null||i.apply(void 0,b)};d.useEffect(()=>{let b=null;return a&&(b=setTimeout(()=>{var w;(w=m.current)===null||w===void 0||w.focus({preventScroll:!0})})),()=>{b&&clearTimeout(b)}},[]);const C=b=>{xo(b)&&(v(!0),b.then((...w)=>{v(!1,!0),h.apply(void 0,w),f.current=!1},w=>{if(v(!1,!0),f.current=!1,!(l!=null&&l()))return Promise.reject(w)}))},g=b=>{if(f.current)return;if(f.current=!0,!u){h();return}let w;if(s){if(w=u(b),c&&!xo(w)){f.current=!1,h(b);return}}else if(u.length)w=u(i),f.current=!1;else if(w=u(),!xo(w)){h();return}C(w)};return d.createElement(qe,Object.assign({},zl(t),{onClick:g,loading:p,prefixCls:r},o,{ref:m}),n)},ir=P.createContext({}),{Provider:hc}=ir,Da=()=>{const{autoFocusButton:e,cancelButtonProps:t,cancelTextLocale:n,isSilent:r,mergedOkCancel:o,rootPrefixCls:i,close:a,onCancel:s,onConfirm:l}=d.useContext(ir);return o?P.createElement(mc,{isSilent:r,actionFn:s,close:(...c)=>{a==null||a.apply(void 0,c),l==null||l(!1)},autoFocus:e==="cancel",buttonProps:t,prefixCls:`${i}-btn`},n):null},Ba=()=>{const{autoFocusButton:e,close:t,isSilent:n,okButtonProps:r,rootPrefixCls:o,okTextLocale:i,okType:a,onConfirm:s,onOk:l}=d.useContext(ir);return P.createElement(mc,{isSilent:n,type:a||"primary",actionFn:l,close:(...c)=>{t==null||t.apply(void 0,c),s==null||s(!0)},autoFocus:e==="ok",buttonProps:r,prefixCls:`${o}-btn`},i)};var gc=d.createContext({});function Fa(e,t,n){var r=t;return!r&&n&&(r="".concat(e,"-").concat(n)),r}function Ha(e,t){var n=e["page".concat(t?"Y":"X","Offset")],r="scroll".concat(t?"Top":"Left");if(typeof n!="number"){var o=e.document;n=o.documentElement[r],typeof n!="number"&&(n=o.body[r])}return n}function jm(e){var t=e.getBoundingClientRect(),n={left:t.left,top:t.top},r=e.ownerDocument,o=r.defaultView||r.parentWindow;return n.left+=Ha(o),n.top+=Ha(o,!0),n}const Lm=d.memo(function(e){var t=e.children;return t},function(e,t){var n=t.shouldUpdate;return!n});var _m={width:0,height:0,overflow:"hidden",outline:"none"},Am={outline:"none"},vc=P.forwardRef(function(e,t){var n=e.prefixCls,r=e.className,o=e.style,i=e.title,a=e.ariaId,s=e.footer,l=e.closable,c=e.closeIcon,u=e.onClose,f=e.children,m=e.bodyStyle,p=e.bodyProps,v=e.modalRender,h=e.onMouseDown,C=e.onMouseUp,g=e.holderRef,b=e.visible,w=e.forceRender,E=e.width,y=e.height,x=e.classNames,I=e.styles,R=P.useContext(gc),k=R.panel,T=Dl(g,k),O=d.useRef(),N=d.useRef();P.useImperativeHandle(t,function(){return{focus:function(){var q;(q=O.current)===null||q===void 0||q.focus({preventScroll:!0})},changeActive:function(q){var S=document,Y=S.activeElement;q&&Y===N.current?O.current.focus({preventScroll:!0}):!q&&Y===O.current&&N.current.focus({preventScroll:!0})}}});var j={};E!==void 0&&(j.width=E),y!==void 0&&(j.height=y);var M=s?P.createElement("div",{className:B("".concat(n,"-footer"),x==null?void 0:x.footer),style:te({},I==null?void 0:I.footer)},s):null,L=i?P.createElement("div",{className:B("".concat(n,"-header"),x==null?void 0:x.header),style:te({},I==null?void 0:I.header)},P.createElement("div",{className:"".concat(n,"-title"),id:a},i)):null,z=d.useMemo(function(){return et(l)==="object"&&l!==null?l:l?{closeIcon:c??P.createElement("span",{className:"".concat(n,"-close-x")})}:{}},[l,c,n]),D=rn(z,!0),W=et(l)==="object"&&l.disabled,F=l?P.createElement("button",ae({type:"button",onClick:u,"aria-label":"Close"},D,{className:"".concat(n,"-close"),disabled:W}),z.closeIcon):null,$=P.createElement("div",{className:B("".concat(n,"-content"),x==null?void 0:x.content),style:I==null?void 0:I.content},F,L,P.createElement("div",ae({className:B("".concat(n,"-body"),x==null?void 0:x.body),style:te(te({},m),I==null?void 0:I.body)},p),f),M);return P.createElement("div",{key:"dialog-element",role:"dialog","aria-labelledby":i?a:null,"aria-modal":"true",ref:T,style:te(te({},o),j),className:B(n,r),onMouseDown:h,onMouseUp:C},P.createElement("div",{ref:O,tabIndex:0,style:Am},P.createElement(Lm,{shouldUpdate:b||w},v?v($):$)),P.createElement("div",{tabIndex:0,ref:N,style:_m}))}),yc=d.forwardRef(function(e,t){var n=e.prefixCls,r=e.title,o=e.style,i=e.className,a=e.visible,s=e.forceRender,l=e.destroyOnClose,c=e.motionName,u=e.ariaId,f=e.onVisibleChanged,m=e.mousePosition,p=d.useRef(),v=d.useState(),h=ve(v,2),C=h[0],g=h[1],b={};C&&(b.transformOrigin=C);function w(){var E=jm(p.current);g(m&&(m.x||m.y)?"".concat(m.x-E.left,"px ").concat(m.y-E.top,"px"):"")}return d.createElement(on,{visible:a,onVisibleChanged:f,onAppearPrepare:w,onEnterPrepare:w,forceRender:s,motionName:c,removeOnLeave:l,ref:p},function(E,y){var x=E.className,I=E.style;return d.createElement(vc,ae({},e,{ref:t,title:r,ariaId:u,prefixCls:n,holderRef:y,style:te(te(te({},I),o),b),className:B(i,x)}))})});yc.displayName="Content";var zm=function(t){var n=t.prefixCls,r=t.style,o=t.visible,i=t.maskProps,a=t.motionName,s=t.className;return d.createElement(on,{key:"mask",visible:o,motionName:a,leavedClassName:"".concat(n,"-mask-hidden")},function(l,c){var u=l.className,f=l.style;return d.createElement("div",ae({ref:c,style:te(te({},f),r),className:B("".concat(n,"-mask"),u,s)},i))})},Dm=function(t){var n=t.prefixCls,r=n===void 0?"rc-dialog":n,o=t.zIndex,i=t.visible,a=i===void 0?!1:i,s=t.keyboard,l=s===void 0?!0:s,c=t.focusTriggerAfterClose,u=c===void 0?!0:c,f=t.wrapStyle,m=t.wrapClassName,p=t.wrapProps,v=t.onClose,h=t.afterOpenChange,C=t.afterClose,g=t.transitionName,b=t.animation,w=t.closable,E=w===void 0?!0:w,y=t.mask,x=y===void 0?!0:y,I=t.maskTransitionName,R=t.maskAnimation,k=t.maskClosable,T=k===void 0?!0:k,O=t.maskStyle,N=t.maskProps,j=t.rootClassName,M=t.classNames,L=t.styles,z=d.useRef(),D=d.useRef(),W=d.useRef(),F=d.useState(a),$=ve(F,2),G=$[0],q=$[1],S=Gd();function Y(){Ta(D.current,document.activeElement)||(z.current=document.activeElement)}function ne(){if(!Ta(D.current,document.activeElement)){var ie;(ie=W.current)===null||ie===void 0||ie.focus()}}function K(ie){if(ie)ne();else{if(q(!1),x&&z.current&&u){try{z.current.focus({preventScroll:!0})}catch{}z.current=null}G&&(C==null||C())}h==null||h(ie)}function ue(ie){v==null||v(ie)}var ce=d.useRef(!1),me=d.useRef(),fe=function(){clearTimeout(me.current),ce.current=!0},se=function(){me.current=setTimeout(function(){ce.current=!1})},ee=null;T&&(ee=function(be){ce.current?ce.current=!1:D.current===be.target&&ue(be)});function de(ie){if(l&&ie.keyCode===it.ESC){ie.stopPropagation(),ue(ie);return}a&&ie.keyCode===it.TAB&&W.current.changeActive(!ie.shiftKey)}d.useEffect(function(){a&&(q(!0),Y())},[a]),d.useEffect(function(){return function(){clearTimeout(me.current)}},[]);var oe=te(te(te({zIndex:o},f),L==null?void 0:L.wrapper),{},{display:G?null:"none"});return d.createElement("div",ae({className:B("".concat(r,"-root"),j)},rn(t,{data:!0})),d.createElement(zm,{prefixCls:r,visible:x&&a,motionName:Fa(r,I,R),style:te(te({zIndex:o},O),L==null?void 0:L.mask),maskProps:N,className:M==null?void 0:M.mask}),d.createElement("div",ae({tabIndex:-1,onKeyDown:de,className:B("".concat(r,"-wrap"),m,M==null?void 0:M.wrapper),ref:D,onClick:ee,style:oe},p),d.createElement(yc,ae({},t,{onMouseDown:fe,onMouseUp:se,ref:W,closable:E,ariaId:S,prefixCls:r,visible:a&&G,onClose:ue,onVisibleChanged:K,motionName:Fa(r,g,b)}))))},Fi=function(t){var n=t.visible,r=t.getContainer,o=t.forceRender,i=t.destroyOnClose,a=i===void 0?!1:i,s=t.afterClose,l=t.panelRef,c=d.useState(n),u=ve(c,2),f=u[0],m=u[1],p=d.useMemo(function(){return{panel:l}},[l]);return d.useEffect(function(){n&&m(!0)},[n]),!o&&a&&!f?null:d.createElement(gc.Provider,{value:p},d.createElement(Bl,{open:n||o||f,autoDestroy:!1,getContainer:r,autoLock:n||f},d.createElement(Dm,ae({},t,{destroyOnClose:a,afterClose:function(){s==null||s(),m(!1)}}))))};Fi.displayName="Dialog";const Bm=()=>Fl()&&window.document.documentElement,Wa=()=>{const{cancelButtonProps:e,cancelTextLocale:t,onCancel:n}=d.useContext(ir);return P.createElement(qe,Object.assign({onClick:n},e),t)},Va=()=>{const{confirmLoading:e,okButtonProps:t,okType:n,okTextLocale:r,onOk:o}=d.useContext(ir);return P.createElement(qe,Object.assign({},zl(n),{loading:e,onClick:o},t),r)};function bc(e,t){return P.createElement("span",{className:`${e}-close-x`},t||P.createElement(an,{className:`${e}-close-icon`}))}const xc=e=>{const{okText:t,okType:n="primary",cancelText:r,confirmLoading:o,onOk:i,onCancel:a,okButtonProps:s,cancelButtonProps:l,footer:c}=e,[u]=$n("Modal",Hl()),f=t||(u==null?void 0:u.okText),m=r||(u==null?void 0:u.cancelText),p={confirmLoading:o,okButtonProps:s,cancelButtonProps:l,okTextLocale:f,cancelTextLocale:m,okType:n,onOk:i,onCancel:a},v=P.useMemo(()=>p,He(Object.values(p)));let h;return typeof c=="function"||typeof c>"u"?(h=P.createElement(P.Fragment,null,P.createElement(Wa,null),P.createElement(Va,null)),typeof c=="function"&&(h=c(h,{OkBtn:Va,CancelBtn:Wa})),h=P.createElement(hc,{value:v},h)):h=c,P.createElement(qd,{disabled:!1},h)},Fm=e=>{const{componentCls:t}=e;return{[t]:{display:"flex",flexFlow:"row wrap",minWidth:0,"&::before, &::after":{display:"flex"},"&-no-wrap":{flexWrap:"nowrap"},"&-start":{justifyContent:"flex-start"},"&-center":{justifyContent:"center"},"&-end":{justifyContent:"flex-end"},"&-space-between":{justifyContent:"space-between"},"&-space-around":{justifyContent:"space-around"},"&-space-evenly":{justifyContent:"space-evenly"},"&-top":{alignItems:"flex-start"},"&-middle":{alignItems:"center"},"&-bottom":{alignItems:"flex-end"}}}},Hm=e=>{const{componentCls:t}=e;return{[t]:{position:"relative",maxWidth:"100%",minHeight:1}}},Wm=(e,t)=>{const{prefixCls:n,componentCls:r,gridColumns:o}=e,i={};for(let a=o;a>=0;a--)a===0?(i[`${r}${t}-${a}`]={display:"none"},i[`${r}-push-${a}`]={insetInlineStart:"auto"},i[`${r}-pull-${a}`]={insetInlineEnd:"auto"},i[`${r}${t}-push-${a}`]={insetInlineStart:"auto"},i[`${r}${t}-pull-${a}`]={insetInlineEnd:"auto"},i[`${r}${t}-offset-${a}`]={marginInlineStart:0},i[`${r}${t}-order-${a}`]={order:0}):(i[`${r}${t}-${a}`]=[{"--ant-display":"block",display:"block"},{display:"var(--ant-display)",flex:`0 0 ${a/o*100}%`,maxWidth:`${a/o*100}%`}],i[`${r}${t}-push-${a}`]={insetInlineStart:`${a/o*100}%`},i[`${r}${t}-pull-${a}`]={insetInlineEnd:`${a/o*100}%`},i[`${r}${t}-offset-${a}`]={marginInlineStart:`${a/o*100}%`},i[`${r}${t}-order-${a}`]={order:a});return i[`${r}${t}-flex`]={flex:`var(--${n}${t}-flex)`},i},ii=(e,t)=>Wm(e,t),Vm=(e,t,n)=>({[`@media (min-width: ${H(t)})`]:Object.assign({},ii(e,n))}),Um=()=>({}),Xm=()=>({}),Gm=xt("Grid",Fm,Um),Cc=e=>({xs:e.screenXSMin,sm:e.screenSMMin,md:e.screenMDMin,lg:e.screenLGMin,xl:e.screenXLMin,xxl:e.screenXXLMin}),qm=xt("Grid",e=>{const t=tt(e,{gridColumns:24}),n=Cc(t);return delete n.xs,[Hm(t),ii(t,""),ii(t,"-xs"),Object.keys(n).map(r=>Vm(t,n[r],`-${r}`)).reduce((r,o)=>Object.assign(Object.assign({},r),o),{})]},Xm);function Ua(e){return{position:e,inset:0}}const Sc=e=>{const{componentCls:t,antCls:n}=e;return[{[`${t}-root`]:{[`${t}${n}-zoom-enter, ${t}${n}-zoom-appear`]:{transform:"none",opacity:0,animationDuration:e.motionDurationSlow,userSelect:"none"},[`${t}${n}-zoom-leave ${t}-content`]:{pointerEvents:"none"},[`${t}-mask`]:Object.assign(Object.assign({},Ua("fixed")),{zIndex:e.zIndexPopupBase,height:"100%",backgroundColor:e.colorBgMask,pointerEvents:"none",[`${t}-hidden`]:{display:"none"}}),[`${t}-wrap`]:Object.assign(Object.assign({},Ua("fixed")),{zIndex:e.zIndexPopupBase,overflow:"auto",outline:0,WebkitOverflowScrolling:"touch"})}},{[`${t}-root`]:Bi(e)}]},Ym=e=>{const{componentCls:t}=e;return[{[`${t}-root`]:{[`${t}-wrap-rtl`]:{direction:"rtl"},[`${t}-centered`]:{textAlign:"center","&::before":{display:"inline-block",width:0,height:"100%",verticalAlign:"middle",content:'""'},[t]:{top:0,display:"inline-block",paddingBottom:0,textAlign:"start",verticalAlign:"middle"}},[`@media (max-width: ${e.screenSMMax}px)`]:{[t]:{maxWidth:"calc(100vw - 16px)",margin:`${H(e.marginXS)} auto`},[`${t}-centered`]:{[t]:{flex:1}}}}},{[t]:Object.assign(Object.assign({},Ht(e)),{pointerEvents:"none",position:"relative",top:100,width:"auto",maxWidth:`calc(100vw - ${H(e.calc(e.margin).mul(2).equal())})`,margin:"0 auto",paddingBottom:e.paddingLG,[`${t}-title`]:{margin:0,color:e.titleColor,fontWeight:e.fontWeightStrong,fontSize:e.titleFontSize,lineHeight:e.titleLineHeight,wordWrap:"break-word"},[`${t}-content`]:{position:"relative",backgroundColor:e.contentBg,backgroundClip:"padding-box",border:0,borderRadius:e.borderRadiusLG,boxShadow:e.boxShadow,pointerEvents:"auto",padding:e.contentPadding},[`${t}-close`]:Object.assign({position:"absolute",top:e.calc(e.modalHeaderHeight).sub(e.modalCloseBtnSize).div(2).equal(),insetInlineEnd:e.calc(e.modalHeaderHeight).sub(e.modalCloseBtnSize).div(2).equal(),zIndex:e.calc(e.zIndexPopupBase).add(10).equal(),padding:0,color:e.modalCloseIconColor,fontWeight:e.fontWeightStrong,lineHeight:1,textDecoration:"none",background:"transparent",borderRadius:e.borderRadiusSM,width:e.modalCloseBtnSize,height:e.modalCloseBtnSize,border:0,outline:0,cursor:"pointer",transition:`color ${e.motionDurationMid}, background-color ${e.motionDurationMid}`,"&-x":{display:"flex",fontSize:e.fontSizeLG,fontStyle:"normal",lineHeight:H(e.modalCloseBtnSize),justifyContent:"center",textTransform:"none",textRendering:"auto"},"&:disabled":{pointerEvents:"none"},"&:hover":{color:e.modalCloseIconHoverColor,backgroundColor:e.colorBgTextHover,textDecoration:"none"},"&:active":{backgroundColor:e.colorBgTextActive}},Yn(e)),[`${t}-header`]:{color:e.colorText,background:e.headerBg,borderRadius:`${H(e.borderRadiusLG)} ${H(e.borderRadiusLG)} 0 0`,marginBottom:e.headerMarginBottom,padding:e.headerPadding,borderBottom:e.headerBorderBottom},[`${t}-body`]:{fontSize:e.fontSize,lineHeight:e.lineHeight,wordWrap:"break-word",padding:e.bodyPadding,[`${t}-body-skeleton`]:{width:"100%",height:"100%",display:"flex",justifyContent:"center",alignItems:"center",margin:`${H(e.margin)} auto`}},[`${t}-footer`]:{textAlign:"end",background:e.footerBg,marginTop:e.footerMarginTop,padding:e.footerPadding,borderTop:e.footerBorderTop,borderRadius:e.footerBorderRadius,[`> ${e.antCls}-btn + ${e.antCls}-btn`]:{marginInlineStart:e.marginXS}},[`${t}-open`]:{overflow:"hidden"}})},{[`${t}-pure-panel`]:{top:"auto",padding:0,display:"flex",flexDirection:"column",[`${t}-content,
          ${t}-body,
          ${t}-confirm-body-wrapper`]:{display:"flex",flexDirection:"column",flex:"auto"},[`${t}-confirm-body`]:{marginBottom:"auto"}}}]},Km=e=>{const{componentCls:t}=e;return{[`${t}-root`]:{[`${t}-wrap-rtl`]:{direction:"rtl",[`${t}-confirm-body`]:{direction:"rtl"}}}}},Zm=e=>{const{componentCls:t}=e,n=Cc(e);delete n.xs;const r=Object.keys(n).map(o=>({[`@media (min-width: ${H(n[o])})`]:{width:`var(--${t.replace(".","")}-${o}-width)`}}));return{[`${t}-root`]:{[t]:[{width:`var(--${t.replace(".","")}-xs-width)`}].concat(He(r))}}},wc=e=>{const t=e.padding,n=e.fontSizeHeading5,r=e.lineHeightHeading5;return tt(e,{modalHeaderHeight:e.calc(e.calc(r).mul(n).equal()).add(e.calc(t).mul(2).equal()).equal(),modalFooterBorderColorSplit:e.colorSplit,modalFooterBorderStyle:e.lineType,modalFooterBorderWidth:e.lineWidth,modalCloseIconColor:e.colorIcon,modalCloseIconHoverColor:e.colorIconHover,modalCloseBtnSize:e.controlHeight,modalConfirmIconSize:e.fontHeight,modalTitleHeight:e.calc(e.titleFontSize).mul(e.titleLineHeight).equal()})},$c=e=>({footerBg:"transparent",headerBg:e.colorBgElevated,titleLineHeight:e.lineHeightHeading5,titleFontSize:e.fontSizeHeading5,contentBg:e.colorBgElevated,titleColor:e.colorTextHeading,contentPadding:e.wireframe?0:`${H(e.paddingMD)} ${H(e.paddingContentHorizontalLG)}`,headerPadding:e.wireframe?`${H(e.padding)} ${H(e.paddingLG)}`:0,headerBorderBottom:e.wireframe?`${H(e.lineWidth)} ${e.lineType} ${e.colorSplit}`:"none",headerMarginBottom:e.wireframe?0:e.marginXS,bodyPadding:e.wireframe?e.paddingLG:0,footerPadding:e.wireframe?`${H(e.paddingXS)} ${H(e.padding)}`:0,footerBorderTop:e.wireframe?`${H(e.lineWidth)} ${e.lineType} ${e.colorSplit}`:"none",footerBorderRadius:e.wireframe?`0 0 ${H(e.borderRadiusLG)} ${H(e.borderRadiusLG)}`:0,footerMarginTop:e.wireframe?0:e.marginSM,confirmBodyPadding:e.wireframe?`${H(e.padding*2)} ${H(e.padding*2)} ${H(e.paddingLG)}`:0,confirmIconMarginInlineEnd:e.wireframe?e.margin:e.marginSM,confirmBtnsMarginTop:e.wireframe?e.marginLG:e.marginSM}),Ec=xt("Modal",e=>{const t=wc(e);return[Ym(t),Km(t),Sc(t),Oi(t,"zoom"),Zm(t)]},$c,{unitless:{titleLineHeight:!0}});var Jm=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};let ai;const Qm=e=>{ai={x:e.pageX,y:e.pageY},setTimeout(()=>{ai=null},100)};Bm()&&document.documentElement.addEventListener("click",Qm,!0);const Ic=e=>{const{prefixCls:t,className:n,rootClassName:r,open:o,wrapClassName:i,centered:a,getContainer:s,focusTriggerAfterClose:l=!0,style:c,visible:u,width:f=520,footer:m,classNames:p,styles:v,children:h,loading:C,confirmLoading:g,zIndex:b,mousePosition:w,onOk:E,onCancel:y,destroyOnHidden:x,destroyOnClose:I}=e,R=Jm(e,["prefixCls","className","rootClassName","open","wrapClassName","centered","getContainer","focusTriggerAfterClose","style","visible","width","footer","classNames","styles","children","loading","confirmLoading","zIndex","mousePosition","onOk","onCancel","destroyOnHidden","destroyOnClose"]),{getPopupContainer:k,getPrefixCls:T,direction:O,modal:N}=d.useContext(Ve),j=de=>{g||y==null||y(de)},M=de=>{E==null||E(de)},L=T("modal",t),z=T(),D=Wt(L),[W,F,$]=Ec(L,D),G=B(i,{[`${L}-centered`]:a??(N==null?void 0:N.centered),[`${L}-wrap-rtl`]:O==="rtl"}),q=m!==null&&!C?d.createElement(xc,Object.assign({},e,{onOk:M,onCancel:j})):null,[S,Y,ne,K]=Yd(Oa(e),Oa(N),{closable:!0,closeIcon:d.createElement(an,{className:`${L}-close-icon`}),closeIconRender:de=>bc(L,de)}),ue=Kd(`.${L}-content`),[ce,me]=Ri("Modal",b),[fe,se]=d.useMemo(()=>f&&typeof f=="object"?[void 0,f]:[f,void 0],[f]),ee=d.useMemo(()=>{const de={};return se&&Object.keys(se).forEach(oe=>{const ie=se[oe];ie!==void 0&&(de[`--${L}-${oe}-width`]=typeof ie=="number"?`${ie}px`:ie)}),de},[se]);return W(d.createElement(Zd,{form:!0,space:!0},d.createElement(Jd.Provider,{value:me},d.createElement(Fi,Object.assign({width:fe},R,{zIndex:ce,getContainer:s===void 0?k:s,prefixCls:L,rootClassName:B(F,r,$,D),footer:q,visible:o??u,mousePosition:w??ai,onClose:j,closable:S&&Object.assign({disabled:ne,closeIcon:Y},K),closeIcon:Y,focusTriggerAfterClose:l,transitionName:At(z,"zoom",e.transitionName),maskTransitionName:At(z,"fade",e.maskTransitionName),className:B(F,n,N==null?void 0:N.className),style:Object.assign(Object.assign(Object.assign({},N==null?void 0:N.style),c),ee),classNames:Object.assign(Object.assign(Object.assign({},N==null?void 0:N.classNames),p),{wrapper:B(G,p==null?void 0:p.wrapper)}),styles:Object.assign(Object.assign({},N==null?void 0:N.styles),v),panelRef:ue,destroyOnClose:x??I}),C?d.createElement(Wl,{active:!0,title:!1,paragraph:{rows:4},className:`${L}-body-skeleton`}):h))))},eh=e=>{const{componentCls:t,titleFontSize:n,titleLineHeight:r,modalConfirmIconSize:o,fontSize:i,lineHeight:a,modalTitleHeight:s,fontHeight:l,confirmBodyPadding:c}=e,u=`${t}-confirm`;return{[u]:{"&-rtl":{direction:"rtl"},[`${e.antCls}-modal-header`]:{display:"none"},[`${u}-body-wrapper`]:Object.assign({},sn()),[`&${t} ${t}-body`]:{padding:c},[`${u}-body`]:{display:"flex",flexWrap:"nowrap",alignItems:"start",[`> ${e.iconCls}`]:{flex:"none",fontSize:o,marginInlineEnd:e.confirmIconMarginInlineEnd,marginTop:e.calc(e.calc(l).sub(o).equal()).div(2).equal()},[`&-has-title > ${e.iconCls}`]:{marginTop:e.calc(e.calc(s).sub(o).equal()).div(2).equal()}},[`${u}-paragraph`]:{display:"flex",flexDirection:"column",flex:"auto",rowGap:e.marginXS,maxWidth:`calc(100% - ${H(e.marginSM)})`},[`${e.iconCls} + ${u}-paragraph`]:{maxWidth:`calc(100% - ${H(e.calc(e.modalConfirmIconSize).add(e.marginSM).equal())})`},[`${u}-title`]:{color:e.colorTextHeading,fontWeight:e.fontWeightStrong,fontSize:n,lineHeight:r},[`${u}-content`]:{color:e.colorText,fontSize:i,lineHeight:a},[`${u}-btns`]:{textAlign:"end",marginTop:e.confirmBtnsMarginTop,[`${e.antCls}-btn + ${e.antCls}-btn`]:{marginBottom:0,marginInlineStart:e.marginXS}}},[`${u}-error ${u}-body > ${e.iconCls}`]:{color:e.colorError},[`${u}-warning ${u}-body > ${e.iconCls},
        ${u}-confirm ${u}-body > ${e.iconCls}`]:{color:e.colorWarning},[`${u}-info ${u}-body > ${e.iconCls}`]:{color:e.colorInfo},[`${u}-success ${u}-body > ${e.iconCls}`]:{color:e.colorSuccess}}},th=Vl(["Modal","confirm"],e=>{const t=wc(e);return[eh(t)]},$c,{order:-1e3});var nh=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};function Pc(e){const{prefixCls:t,icon:n,okText:r,cancelText:o,confirmPrefixCls:i,type:a,okCancel:s,footer:l,locale:c}=e,u=nh(e,["prefixCls","icon","okText","cancelText","confirmPrefixCls","type","okCancel","footer","locale"]);let f=n;if(!n&&n!==null)switch(a){case"info":f=d.createElement(Gl,null);break;case"success":f=d.createElement(ki,null);break;case"error":f=d.createElement(eo,null);break;default:f=d.createElement(Xl,null)}const m=s??a==="confirm",p=e.autoFocusButton===null?!1:e.autoFocusButton||"ok",[v]=$n("Modal"),h=c||v,C=r||(m?h==null?void 0:h.okText:h==null?void 0:h.justOkText),g=o||(h==null?void 0:h.cancelText),b=Object.assign({autoFocusButton:p,cancelTextLocale:g,okTextLocale:C,mergedOkCancel:m},u),w=d.useMemo(()=>b,He(Object.values(b))),E=d.createElement(d.Fragment,null,d.createElement(Da,null),d.createElement(Ba,null)),y=e.title!==void 0&&e.title!==null,x=`${i}-body`;return d.createElement("div",{className:`${i}-body-wrapper`},d.createElement("div",{className:B(x,{[`${x}-has-title`]:y})},f,d.createElement("div",{className:`${i}-paragraph`},y&&d.createElement("span",{className:`${i}-title`},e.title),d.createElement("div",{className:`${i}-content`},e.content))),l===void 0||typeof l=="function"?d.createElement(hc,{value:w},d.createElement("div",{className:`${i}-btns`},typeof l=="function"?l(E,{OkBtn:Ba,CancelBtn:Da}):E)):l,d.createElement(th,{prefixCls:t}))}const rh=e=>{const{close:t,zIndex:n,maskStyle:r,direction:o,prefixCls:i,wrapClassName:a,rootPrefixCls:s,bodyStyle:l,closable:c=!1,onConfirm:u,styles:f}=e,m=`${i}-confirm`,p=e.width||416,v=e.style||{},h=e.mask===void 0?!0:e.mask,C=e.maskClosable===void 0?!1:e.maskClosable,g=B(m,`${m}-${e.type}`,{[`${m}-rtl`]:o==="rtl"},e.className),[,b]=Ni(),w=d.useMemo(()=>n!==void 0?n:b.zIndexPopupBase+Ul,[n,b]);return d.createElement(Ic,Object.assign({},e,{className:g,wrapClassName:B({[`${m}-centered`]:!!e.centered},a),onCancel:()=>{t==null||t({triggerCancel:!0}),u==null||u(!1)},title:"",footer:null,transitionName:At(s||"","zoom",e.transitionName),maskTransitionName:At(s||"","fade",e.maskTransitionName),mask:h,maskClosable:C,style:v,styles:Object.assign({body:l,mask:r},f),width:p,zIndex:w,closable:c}),d.createElement(Pc,Object.assign({},e,{confirmPrefixCls:m})))},Tc=e=>{const{rootPrefixCls:t,iconPrefixCls:n,direction:r,theme:o}=e;return d.createElement(En,{prefixCls:t,iconPrefixCls:n,direction:r,theme:o},d.createElement(rh,Object.assign({},e)))},Jt=[];let Oc="";function Rc(){return Oc}const oh=e=>{var t,n;const{prefixCls:r,getContainer:o,direction:i}=e,a=Hl(),s=d.useContext(Ve),l=Rc()||s.getPrefixCls(),c=r||`${l}-modal`;let u=o;return u===!1&&(u=void 0),P.createElement(Tc,Object.assign({},e,{rootPrefixCls:l,prefixCls:c,iconPrefixCls:s.iconPrefixCls,theme:s.theme,direction:i??s.direction,locale:(n=(t=s.locale)===null||t===void 0?void 0:t.Modal)!==null&&n!==void 0?n:a,getContainer:u}))};function ar(e){const t=ql(),n=document.createDocumentFragment();let r=Object.assign(Object.assign({},e),{close:l,open:!0}),o,i;function a(...u){var f;if(u.some(v=>v==null?void 0:v.triggerCancel)){var p;(f=e.onCancel)===null||f===void 0||(p=f).call.apply(p,[e,()=>{}].concat(He(u.slice(1))))}for(let v=0;v<Jt.length;v++)if(Jt[v]===l){Jt.splice(v,1);break}i()}function s(u){clearTimeout(o),o=setTimeout(()=>{const f=t.getPrefixCls(void 0,Rc()),m=t.getIconPrefixCls(),p=t.getTheme(),v=P.createElement(oh,Object.assign({},u));i=Yl()(P.createElement(En,{prefixCls:f,iconPrefixCls:m,theme:p},t.holderRender?t.holderRender(v):v),n)})}function l(...u){r=Object.assign(Object.assign({},r),{open:!1,afterClose:()=>{typeof e.afterClose=="function"&&e.afterClose(),a.apply(this,u)}}),r.visible&&delete r.visible,s(r)}function c(u){typeof u=="function"?r=u(r):r=Object.assign(Object.assign({},r),u),s(r)}return s(r),Jt.push(l),{destroy:l,update:c}}function Nc(e){return Object.assign(Object.assign({},e),{type:"warning"})}function kc(e){return Object.assign(Object.assign({},e),{type:"info"})}function Mc(e){return Object.assign(Object.assign({},e),{type:"success"})}function jc(e){return Object.assign(Object.assign({},e),{type:"error"})}function Lc(e){return Object.assign(Object.assign({},e),{type:"confirm"})}function ih({rootPrefixCls:e}){Oc=e}var ah=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const sh=(e,t)=>{var n,{afterClose:r,config:o}=e,i=ah(e,["afterClose","config"]);const[a,s]=d.useState(!0),[l,c]=d.useState(o),{direction:u,getPrefixCls:f}=d.useContext(Ve),m=f("modal"),p=f(),v=()=>{var b;r(),(b=l.afterClose)===null||b===void 0||b.call(l)},h=(...b)=>{var w;if(s(!1),b.some(x=>x==null?void 0:x.triggerCancel)){var y;(w=l.onCancel)===null||w===void 0||(y=w).call.apply(y,[l,()=>{}].concat(He(b.slice(1))))}};d.useImperativeHandle(t,()=>({destroy:h,update:b=>{c(w=>{const E=typeof b=="function"?b(w):b;return Object.assign(Object.assign({},w),E)})}}));const C=(n=l.okCancel)!==null&&n!==void 0?n:l.type==="confirm",[g]=$n("Modal",Kl.Modal);return d.createElement(Tc,Object.assign({prefixCls:m,rootPrefixCls:p},l,{close:h,open:a,afterClose:v,okText:l.okText||(C?g==null?void 0:g.okText:g==null?void 0:g.justOkText),direction:l.direction||u,cancelText:l.cancelText||(g==null?void 0:g.cancelText)},i))},lh=d.forwardRef(sh);let Xa=0;const ch=d.memo(d.forwardRef((e,t)=>{const[n,r]=Nm();return d.useImperativeHandle(t,()=>({patchElement:r}),[]),d.createElement(d.Fragment,null,n)}));function uh(){const e=d.useRef(null),[t,n]=d.useState([]);d.useEffect(()=>{t.length&&(He(t).forEach(a=>{a()}),n([]))},[t]);const r=d.useCallback(i=>function(s){var l;Xa+=1;const c=d.createRef();let u;const f=new Promise(C=>{u=C});let m=!1,p;const v=d.createElement(lh,{key:`modal-${Xa}`,config:i(s),ref:c,afterClose:()=>{p==null||p()},isSilent:()=>m,onConfirm:C=>{u(C)}});return p=(l=e.current)===null||l===void 0?void 0:l.patchElement(v),p&&Jt.push(p),{destroy:()=>{function C(){var g;(g=c.current)===null||g===void 0||g.destroy()}c.current?C():n(g=>[].concat(He(g),[C]))},update:C=>{function g(){var b;(b=c.current)===null||b===void 0||b.update(C)}c.current?g():n(b=>[].concat(He(b),[g]))},then:C=>(m=!0,f.then(C))}},[]);return[d.useMemo(()=>({info:r(kc),success:r(Mc),error:r(jc),warning:r(Nc),confirm:r(Lc)}),[]),d.createElement(ch,{key:"modal-holder",ref:e})]}const dh=e=>{const{componentCls:t,notificationMarginEdge:n,animationMaxHeight:r}=e,o=`${t}-notice`,i=new It("antNotificationFadeIn",{"0%":{transform:"translate3d(100%, 0, 0)",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",opacity:1}}),a=new It("antNotificationTopFadeIn",{"0%":{top:-r,opacity:0},"100%":{top:0,opacity:1}}),s=new It("antNotificationBottomFadeIn",{"0%":{bottom:e.calc(r).mul(-1).equal(),opacity:0},"100%":{bottom:0,opacity:1}}),l=new It("antNotificationLeftFadeIn",{"0%":{transform:"translate3d(-100%, 0, 0)",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",opacity:1}});return{[t]:{[`&${t}-top, &${t}-bottom`]:{marginInline:0,[o]:{marginInline:"auto auto"}},[`&${t}-top`]:{[`${t}-fade-enter${t}-fade-enter-active, ${t}-fade-appear${t}-fade-appear-active`]:{animationName:a}},[`&${t}-bottom`]:{[`${t}-fade-enter${t}-fade-enter-active, ${t}-fade-appear${t}-fade-appear-active`]:{animationName:s}},[`&${t}-topRight, &${t}-bottomRight`]:{[`${t}-fade-enter${t}-fade-enter-active, ${t}-fade-appear${t}-fade-appear-active`]:{animationName:i}},[`&${t}-topLeft, &${t}-bottomLeft`]:{marginRight:{value:0,_skip_check_:!0},marginLeft:{value:n,_skip_check_:!0},[o]:{marginInlineEnd:"auto",marginInlineStart:0},[`${t}-fade-enter${t}-fade-enter-active, ${t}-fade-appear${t}-fade-appear-active`]:{animationName:l}}}}},fh=["top","topLeft","topRight","bottom","bottomLeft","bottomRight"],ph={topLeft:"left",topRight:"right",bottomLeft:"left",bottomRight:"right",top:"left",bottom:"left"},mh=(e,t)=>{const{componentCls:n}=e;return{[`${n}-${t}`]:{[`&${n}-stack > ${n}-notice-wrapper`]:{[t.startsWith("top")?"top":"bottom"]:0,[ph[t]]:{value:0,_skip_check_:!0}}}}},hh=e=>{const t={};for(let n=1;n<e.notificationStackLayer;n++)t[`&:nth-last-child(${n+1})`]={overflow:"hidden",[`& > ${e.componentCls}-notice`]:{opacity:0,transition:`opacity ${e.motionDurationMid}`}};return Object.assign({[`&:not(:nth-last-child(-n+${e.notificationStackLayer}))`]:{opacity:0,overflow:"hidden",color:"transparent",pointerEvents:"none"}},t)},gh=e=>{const t={};for(let n=1;n<e.notificationStackLayer;n++)t[`&:nth-last-child(${n+1})`]={background:e.colorBgBlur,backdropFilter:"blur(10px)","-webkit-backdrop-filter":"blur(10px)"};return Object.assign({},t)},vh=e=>{const{componentCls:t}=e;return Object.assign({[`${t}-stack`]:{[`& > ${t}-notice-wrapper`]:Object.assign({transition:`transform ${e.motionDurationSlow}, backdrop-filter 0s`,willChange:"transform, opacity",position:"absolute"},hh(e))},[`${t}-stack:not(${t}-stack-expanded)`]:{[`& > ${t}-notice-wrapper`]:Object.assign({},gh(e))},[`${t}-stack${t}-stack-expanded`]:{[`& > ${t}-notice-wrapper`]:{"&:not(:nth-last-child(-n + 1))":{opacity:1,overflow:"unset",color:"inherit",pointerEvents:"auto",[`& > ${e.componentCls}-notice`]:{opacity:1}},"&:after":{content:'""',position:"absolute",height:e.margin,width:"100%",insetInline:0,bottom:e.calc(e.margin).mul(-1).equal(),background:"transparent",pointerEvents:"auto"}}}},fh.map(n=>mh(e,n)).reduce((n,r)=>Object.assign(Object.assign({},n),r),{}))},_c=e=>{const{iconCls:t,componentCls:n,boxShadow:r,fontSizeLG:o,notificationMarginBottom:i,borderRadiusLG:a,colorSuccess:s,colorInfo:l,colorWarning:c,colorError:u,colorTextHeading:f,notificationBg:m,notificationPadding:p,notificationMarginEdge:v,notificationProgressBg:h,notificationProgressHeight:C,fontSize:g,lineHeight:b,width:w,notificationIconSize:E,colorText:y}=e,x=`${n}-notice`;return{position:"relative",marginBottom:i,marginInlineStart:"auto",background:m,borderRadius:a,boxShadow:r,[x]:{padding:p,width:w,maxWidth:`calc(100vw - ${H(e.calc(v).mul(2).equal())})`,overflow:"hidden",lineHeight:b,wordWrap:"break-word"},[`${x}-message`]:{marginBottom:e.marginXS,color:f,fontSize:o,lineHeight:e.lineHeightLG},[`${x}-description`]:{fontSize:g,color:y},[`${x}-closable ${x}-message`]:{paddingInlineEnd:e.paddingLG},[`${x}-with-icon ${x}-message`]:{marginBottom:e.marginXS,marginInlineStart:e.calc(e.marginSM).add(E).equal(),fontSize:o},[`${x}-with-icon ${x}-description`]:{marginInlineStart:e.calc(e.marginSM).add(E).equal(),fontSize:g},[`${x}-icon`]:{position:"absolute",fontSize:E,lineHeight:1,[`&-success${t}`]:{color:s},[`&-info${t}`]:{color:l},[`&-warning${t}`]:{color:c},[`&-error${t}`]:{color:u}},[`${x}-close`]:Object.assign({position:"absolute",top:e.notificationPaddingVertical,insetInlineEnd:e.notificationPaddingHorizontal,color:e.colorIcon,outline:"none",width:e.notificationCloseButtonSize,height:e.notificationCloseButtonSize,borderRadius:e.borderRadiusSM,transition:`background-color ${e.motionDurationMid}, color ${e.motionDurationMid}`,display:"flex",alignItems:"center",justifyContent:"center",background:"none",border:"none","&:hover":{color:e.colorIconHover,backgroundColor:e.colorBgTextHover},"&:active":{backgroundColor:e.colorBgTextActive}},Yn(e)),[`${x}-progress`]:{position:"absolute",display:"block",appearance:"none",inlineSize:`calc(100% - ${H(a)} * 2)`,left:{_skip_check_:!0,value:a},right:{_skip_check_:!0,value:a},bottom:0,blockSize:C,border:0,"&, &::-webkit-progress-bar":{borderRadius:a,backgroundColor:"rgba(0, 0, 0, 0.04)"},"&::-moz-progress-bar":{background:h},"&::-webkit-progress-value":{borderRadius:a,background:h}},[`${x}-actions`]:{float:"right",marginTop:e.marginSM}}},yh=e=>{const{componentCls:t,notificationMarginBottom:n,notificationMarginEdge:r,motionDurationMid:o,motionEaseInOut:i}=e,a=`${t}-notice`,s=new It("antNotificationFadeOut",{"0%":{maxHeight:e.animationMaxHeight,marginBottom:n},"100%":{maxHeight:0,marginBottom:0,paddingTop:0,paddingBottom:0,opacity:0}});return[{[t]:Object.assign(Object.assign({},Ht(e)),{position:"fixed",zIndex:e.zIndexPopup,marginRight:{value:r,_skip_check_:!0},[`${t}-hook-holder`]:{position:"relative"},[`${t}-fade-appear-prepare`]:{opacity:"0 !important"},[`${t}-fade-enter, ${t}-fade-appear`]:{animationDuration:e.motionDurationMid,animationTimingFunction:i,animationFillMode:"both",opacity:0,animationPlayState:"paused"},[`${t}-fade-leave`]:{animationTimingFunction:i,animationFillMode:"both",animationDuration:o,animationPlayState:"paused"},[`${t}-fade-enter${t}-fade-enter-active, ${t}-fade-appear${t}-fade-appear-active`]:{animationPlayState:"running"},[`${t}-fade-leave${t}-fade-leave-active`]:{animationName:s,animationPlayState:"running"},"&-rtl":{direction:"rtl",[`${a}-actions`]:{float:"left"}}})},{[t]:{[`${a}-wrapper`]:Object.assign({},_c(e))}}]},Ac=e=>({zIndexPopup:e.zIndexPopupBase+Ul+50,width:384}),zc=e=>{const t=e.paddingMD,n=e.paddingLG;return tt(e,{notificationBg:e.colorBgElevated,notificationPaddingVertical:t,notificationPaddingHorizontal:n,notificationIconSize:e.calc(e.fontSizeLG).mul(e.lineHeightLG).equal(),notificationCloseButtonSize:e.calc(e.controlHeightLG).mul(.55).equal(),notificationMarginBottom:e.margin,notificationPadding:`${H(e.paddingMD)} ${H(e.paddingContentHorizontalLG)}`,notificationMarginEdge:e.marginLG,animationMaxHeight:150,notificationStackLayer:3,notificationProgressHeight:2,notificationProgressBg:`linear-gradient(90deg, ${e.colorPrimaryBorderHover}, ${e.colorPrimary})`})},Dc=xt("Notification",e=>{const t=zc(e);return[yh(t),dh(t),vh(t)]},Ac),bh=Vl(["Notification","PurePanel"],e=>{const t=`${e.componentCls}-notice`,n=zc(e);return{[`${t}-pure-panel`]:Object.assign(Object.assign({},_c(n)),{width:n.width,maxWidth:`calc(100vw - ${H(e.calc(n.notificationMarginEdge).mul(2).equal())})`,margin:0})}},Ac);var xh=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};function Hi(e,t){return t===null||t===!1?null:t||d.createElement(an,{className:`${e}-close-icon`})}const Ch={success:ki,info:Gl,error:eo,warning:Xl},Bc=e=>{const{prefixCls:t,icon:n,type:r,message:o,description:i,actions:a,role:s="alert"}=e;let l=null;return n?l=d.createElement("span",{className:`${t}-icon`},n):r&&(l=d.createElement(Ch[r]||null,{className:B(`${t}-icon`,`${t}-icon-${r}`)})),d.createElement("div",{className:B({[`${t}-with-icon`]:l}),role:s},l,d.createElement("div",{className:`${t}-message`},o),d.createElement("div",{className:`${t}-description`},i),a&&d.createElement("div",{className:`${t}-actions`},a))},Sh=e=>{const{prefixCls:t,className:n,icon:r,type:o,message:i,description:a,btn:s,actions:l,closable:c=!0,closeIcon:u,className:f}=e,m=xh(e,["prefixCls","className","icon","type","message","description","btn","actions","closable","closeIcon","className"]),{getPrefixCls:p}=d.useContext(Ve),v=l??s,h=t||p("notification"),C=`${h}-notice`,g=Wt(h),[b,w,E]=Dc(h,g);return b(d.createElement("div",{className:B(`${C}-pure-panel`,w,n,E,g)},d.createElement(bh,{prefixCls:h}),d.createElement(Qd,Object.assign({},m,{prefixCls:h,eventKey:"pure",duration:null,closable:c,className:B({notificationClassName:f}),closeIcon:Hi(h,u),content:d.createElement(Bc,{prefixCls:C,icon:r,type:o,message:i,description:a,actions:v})}))))};function wh(e,t,n){let r;switch(e){case"top":r={left:"50%",transform:"translateX(-50%)",right:"auto",top:t,bottom:"auto"};break;case"topLeft":r={left:0,top:t,bottom:"auto"};break;case"topRight":r={right:0,top:t,bottom:"auto"};break;case"bottom":r={left:"50%",transform:"translateX(-50%)",right:"auto",top:"auto",bottom:n};break;case"bottomLeft":r={left:0,top:"auto",bottom:n};break;default:r={right:0,top:"auto",bottom:n};break}return r}function $h(e){return{motionName:`${e}-fade`}}function Eh(e,t,n){return typeof e<"u"?e:typeof(t==null?void 0:t.closeIcon)<"u"?t.closeIcon:n==null?void 0:n.closeIcon}var Ih=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const Ga=24,Ph=4.5,Th="topRight",Oh=({children:e,prefixCls:t})=>{const n=Wt(t),[r,o,i]=Dc(t,n);return r(P.createElement(tf,{classNames:{list:B(o,i,n)}},e))},Rh=(e,{prefixCls:t,key:n})=>P.createElement(Oh,{prefixCls:t,key:n},e),Nh=P.forwardRef((e,t)=>{const{top:n,bottom:r,prefixCls:o,getContainer:i,maxCount:a,rtl:s,onAllRemoved:l,stack:c,duration:u,pauseOnHover:f=!0,showProgress:m}=e,{getPrefixCls:p,getPopupContainer:v,notification:h,direction:C}=d.useContext(Ve),[,g]=Ni(),b=o||p("notification"),w=R=>wh(R,n??Ga,r??Ga),E=()=>B({[`${b}-rtl`]:s??C==="rtl"}),y=()=>$h(b),[x,I]=ef({prefixCls:b,style:w,className:E,motion:y,closable:!0,closeIcon:Hi(b),duration:u??Ph,getContainer:()=>(i==null?void 0:i())||(v==null?void 0:v())||document.body,maxCount:a,pauseOnHover:f,showProgress:m,onAllRemoved:l,renderNotifications:Rh,stack:c===!1?!1:{threshold:typeof c=="object"?c==null?void 0:c.threshold:void 0,offset:8,gap:g.margin}});return P.useImperativeHandle(t,()=>Object.assign(Object.assign({},x),{prefixCls:b,notification:h})),I});function Fc(e){const t=P.useRef(null);return nf(),[P.useMemo(()=>{const r=s=>{var l;if(!t.current)return;const{open:c,prefixCls:u,notification:f}=t.current,m=`${u}-notice`,{message:p,description:v,icon:h,type:C,btn:g,actions:b,className:w,style:E,role:y="alert",closeIcon:x,closable:I}=s,R=Ih(s,["message","description","icon","type","btn","actions","className","style","role","closeIcon","closable"]),k=b??g,T=Hi(m,Eh(x,e,f));return c(Object.assign(Object.assign({placement:(l=e==null?void 0:e.placement)!==null&&l!==void 0?l:Th},R),{content:P.createElement(Bc,{prefixCls:m,icon:h,type:C,message:p,description:v,actions:k,role:y}),className:B(C&&`${m}-${C}`,w,f==null?void 0:f.className),style:Object.assign(Object.assign({},f==null?void 0:f.style),E),closeIcon:T,closable:I??!!T}))},i={open:r,destroy:s=>{var l,c;s!==void 0?(l=t.current)===null||l===void 0||l.close(s):(c=t.current)===null||c===void 0||c.destroy()}};return["success","info","warning","error"].forEach(s=>{i[s]=l=>r(Object.assign(Object.assign({},l),{type:s}))}),i},[]),P.createElement(Nh,Object.assign({key:"notification-holder"},e,{ref:t}))]}function kh(e){return Fc(e)}const si=d.createContext({}),Mh=e=>{const{antCls:t,componentCls:n,iconCls:r,avatarBg:o,avatarColor:i,containerSize:a,containerSizeLG:s,containerSizeSM:l,textFontSize:c,textFontSizeLG:u,textFontSizeSM:f,borderRadius:m,borderRadiusLG:p,borderRadiusSM:v,lineWidth:h,lineType:C}=e,g=(b,w,E)=>({width:b,height:b,borderRadius:"50%",[`&${n}-square`]:{borderRadius:E},[`&${n}-icon`]:{fontSize:w,[`> ${r}`]:{margin:0}}});return{[n]:Object.assign(Object.assign(Object.assign(Object.assign({},Ht(e)),{position:"relative",display:"inline-flex",justifyContent:"center",alignItems:"center",overflow:"hidden",color:i,whiteSpace:"nowrap",textAlign:"center",verticalAlign:"middle",background:o,border:`${H(h)} ${C} transparent`,"&-image":{background:"transparent"},[`${t}-image-img`]:{display:"block"}}),g(a,c,m)),{"&-lg":Object.assign({},g(s,u,p)),"&-sm":Object.assign({},g(l,f,v)),"> img":{display:"block",width:"100%",height:"100%",objectFit:"cover"}})}},jh=e=>{const{componentCls:t,groupBorderColor:n,groupOverlapping:r,groupSpace:o}=e;return{[`${t}-group`]:{display:"inline-flex",[t]:{borderColor:n},"> *:not(:first-child)":{marginInlineStart:r}},[`${t}-group-popover`]:{[`${t} + ${t}`]:{marginInlineStart:o}}}},Lh=e=>{const{controlHeight:t,controlHeightLG:n,controlHeightSM:r,fontSize:o,fontSizeLG:i,fontSizeXL:a,fontSizeHeading3:s,marginXS:l,marginXXS:c,colorBorderBg:u}=e;return{containerSize:t,containerSizeLG:n,containerSizeSM:r,textFontSize:Math.round((i+a)/2),textFontSizeLG:s,textFontSizeSM:o,groupSpace:c,groupOverlapping:-l,groupBorderColor:u}},Hc=xt("Avatar",e=>{const{colorTextLightSolid:t,colorTextPlaceholder:n}=e,r=tt(e,{avatarBg:n,avatarColor:t});return[Mh(r),jh(r)]},Lh);var _h=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const Wc=d.forwardRef((e,t)=>{const{prefixCls:n,shape:r,size:o,src:i,srcSet:a,icon:s,className:l,rootClassName:c,style:u,alt:f,draggable:m,children:p,crossOrigin:v,gap:h=4,onError:C}=e,g=_h(e,["prefixCls","shape","size","src","srcSet","icon","className","rootClassName","style","alt","draggable","children","crossOrigin","gap","onError"]),[b,w]=d.useState(1),[E,y]=d.useState(!1),[x,I]=d.useState(!0),R=d.useRef(null),k=d.useRef(null),T=Mi(t,R),{getPrefixCls:O,avatar:N}=d.useContext(Ve),j=d.useContext(si),M=()=>{if(!k.current||!R.current)return;const se=k.current.offsetWidth,ee=R.current.offsetWidth;se!==0&&ee!==0&&h*2<ee&&w(ee-h*2<se?(ee-h*2)/se:1)};d.useEffect(()=>{y(!0)},[]),d.useEffect(()=>{I(!0),w(1)},[i]),d.useEffect(M,[h]);const L=()=>{(C==null?void 0:C())!==!1&&I(!1)},z=ji(se=>{var ee,de;return(de=(ee=o??(j==null?void 0:j.size))!==null&&ee!==void 0?ee:se)!==null&&de!==void 0?de:"default"}),D=Object.keys(typeof z=="object"?z||{}:{}).some(se=>["xs","sm","md","lg","xl","xxl"].includes(se)),W=Zl(D),F=d.useMemo(()=>{if(typeof z!="object")return{};const se=Kn.find(de=>W[de]),ee=z[se];return ee?{width:ee,height:ee,fontSize:ee&&(s||p)?ee/2:18}:{}},[W,z]),$=O("avatar",n),G=Wt($),[q,S,Y]=Hc($,G),ne=B({[`${$}-lg`]:z==="large",[`${$}-sm`]:z==="small"}),K=d.isValidElement(i),ue=r||(j==null?void 0:j.shape)||"circle",ce=B($,ne,N==null?void 0:N.className,`${$}-${ue}`,{[`${$}-image`]:K||i&&x,[`${$}-icon`]:!!s},Y,G,l,c,S),me=typeof z=="number"?{width:z,height:z,fontSize:s?z/2:18}:{};let fe;if(typeof i=="string"&&x)fe=d.createElement("img",{src:i,draggable:m,srcSet:a,onError:L,alt:f,crossOrigin:v});else if(K)fe=i;else if(s)fe=s;else if(E||b!==1){const se=`scale(${b})`,ee={msTransform:se,WebkitTransform:se,transform:se};fe=d.createElement(Un,{onResize:M},d.createElement("span",{className:`${$}-string`,ref:k,style:Object.assign({},ee)},p))}else fe=d.createElement("span",{className:`${$}-string`,style:{opacity:0},ref:k},p);return q(d.createElement("span",Object.assign({},g,{style:Object.assign(Object.assign(Object.assign(Object.assign({},me),F),N==null?void 0:N.style),u),className:ce,ref:T}),fe))}),Wr=e=>e?typeof e=="function"?e():e:null,Ah=e=>{const{componentCls:t,popoverColor:n,titleMinWidth:r,fontWeightStrong:o,innerPadding:i,boxShadowSecondary:a,colorTextHeading:s,borderRadiusLG:l,zIndexPopup:c,titleMarginBottom:u,colorBgElevated:f,popoverBg:m,titleBorderBottom:p,innerContentPadding:v,titlePadding:h}=e;return[{[t]:Object.assign(Object.assign({},Ht(e)),{position:"absolute",top:0,left:{_skip_check_:!0,value:0},zIndex:c,fontWeight:"normal",whiteSpace:"normal",textAlign:"start",cursor:"auto",userSelect:"text","--valid-offset-x":"var(--arrow-offset-horizontal, var(--arrow-x))",transformOrigin:["var(--valid-offset-x, 50%)","var(--arrow-y, 50%)"].join(" "),"--antd-arrow-background-color":f,width:"max-content",maxWidth:"100vw","&-rtl":{direction:"rtl"},"&-hidden":{display:"none"},[`${t}-content`]:{position:"relative"},[`${t}-inner`]:{backgroundColor:m,backgroundClip:"padding-box",borderRadius:l,boxShadow:a,padding:i},[`${t}-title`]:{minWidth:r,marginBottom:u,color:s,fontWeight:o,borderBottom:p,padding:h},[`${t}-inner-content`]:{color:n,padding:v}})},rf(e,"var(--antd-arrow-background-color)"),{[`${t}-pure`]:{position:"relative",maxWidth:"none",margin:e.sizePopupArrow,display:"inline-block",[`${t}-content`]:{display:"inline-block"}}}]},zh=e=>{const{componentCls:t}=e;return{[t]:of.map(n=>{const r=e[`${n}6`];return{[`&${t}-${n}`]:{"--antd-arrow-background-color":r,[`${t}-inner`]:{backgroundColor:r},[`${t}-arrow`]:{background:"transparent"}}}})}},Dh=e=>{const{lineWidth:t,controlHeight:n,fontHeight:r,padding:o,wireframe:i,zIndexPopupBase:a,borderRadiusLG:s,marginXS:l,lineType:c,colorSplit:u,paddingSM:f}=e,m=n-r,p=m/2,v=m/2-t,h=o;return Object.assign(Object.assign(Object.assign({titleMinWidth:177,zIndexPopup:a+30},af(e)),sf({contentRadius:s,limitVerticalRadius:!0})),{innerPadding:i?0:12,titleMarginBottom:i?0:l,titlePadding:i?`${p}px ${h}px ${v}px`:0,titleBorderBottom:i?`${t}px ${c} ${u}`:"none",innerContentPadding:i?`${f}px ${h}px`:0})},Vc=xt("Popover",e=>{const{colorBgElevated:t,colorText:n}=e,r=tt(e,{popoverBg:t,popoverColor:n});return[Ah(r),zh(r),Oi(r,"zoom-big")]},Dh,{resetStyle:!1,deprecatedTokens:[["width","titleMinWidth"],["minWidth","titleMinWidth"]]});var Bh=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const Uc=({title:e,content:t,prefixCls:n})=>!e&&!t?null:d.createElement(d.Fragment,null,e&&d.createElement("div",{className:`${n}-title`},e),t&&d.createElement("div",{className:`${n}-inner-content`},t)),Fh=e=>{const{hashId:t,prefixCls:n,className:r,style:o,placement:i="top",title:a,content:s,children:l}=e,c=Wr(a),u=Wr(s),f=B(t,n,`${n}-pure`,`${n}-placement-${i}`,r);return d.createElement("div",{className:f,style:o},d.createElement("div",{className:`${n}-arrow`}),d.createElement(lf,Object.assign({},e,{className:t,prefixCls:n}),l||d.createElement(Uc,{prefixCls:n,title:c,content:u})))},Hh=e=>{const{prefixCls:t,className:n}=e,r=Bh(e,["prefixCls","className"]),{getPrefixCls:o}=d.useContext(Ve),i=o("popover",t),[a,s,l]=Vc(i);return a(d.createElement(Fh,Object.assign({},r,{prefixCls:i,hashId:s,className:B(n,l)})))};var Wh=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const Vh=d.forwardRef((e,t)=>{var n,r;const{prefixCls:o,title:i,content:a,overlayClassName:s,placement:l="top",trigger:c="hover",children:u,mouseEnterDelay:f=.1,mouseLeaveDelay:m=.1,onOpenChange:p,overlayStyle:v={},styles:h,classNames:C}=e,g=Wh(e,["prefixCls","title","content","overlayClassName","placement","trigger","children","mouseEnterDelay","mouseLeaveDelay","onOpenChange","overlayStyle","styles","classNames"]),{getPrefixCls:b,className:w,style:E,classNames:y,styles:x}=Li("popover"),I=b("popover",o),[R,k,T]=Vc(I),O=b(),N=B(s,k,T,w,y.root,C==null?void 0:C.root),j=B(y.body,C==null?void 0:C.body),[M,L]=bt(!1,{value:(n=e.open)!==null&&n!==void 0?n:e.visible,defaultValue:(r=e.defaultOpen)!==null&&r!==void 0?r:e.defaultVisible}),z=(G,q)=>{L(G,!0),p==null||p(G,q)},D=G=>{G.keyCode===it.ESC&&z(!1,G)},W=G=>{z(G)},F=Wr(i),$=Wr(a);return R(d.createElement(ln,Object.assign({placement:l,trigger:c,mouseEnterDelay:f,mouseLeaveDelay:m},g,{prefixCls:I,classNames:{root:N,body:j},styles:{root:Object.assign(Object.assign(Object.assign(Object.assign({},x.root),E),v),h==null?void 0:h.root),body:Object.assign(Object.assign({},x.body),h==null?void 0:h.body)},ref:t,open:M,onOpenChange:W,overlay:F||$?d.createElement(Uc,{prefixCls:I,title:F,content:$}):null,transitionName:At(O,"zoom-big",g.transitionName),"data-popover-inject":!0}),Zn(u,{onKeyDown:G=>{var q,S;d.isValidElement(u)&&((S=u==null?void 0:(q=u.props).onKeyDown)===null||S===void 0||S.call(q,G)),D(G)}})))}),Xc=Vh;Xc._InternalPanelDoNotUseOrYouWillBeFired=Hh;const qa=e=>{const{size:t,shape:n}=d.useContext(si),r=d.useMemo(()=>({size:e.size||t,shape:e.shape||n}),[e.size,e.shape,t,n]);return d.createElement(si.Provider,{value:r},e.children)},Uh=e=>{var t,n,r,o;const{getPrefixCls:i,direction:a}=d.useContext(Ve),{prefixCls:s,className:l,rootClassName:c,style:u,maxCount:f,maxStyle:m,size:p,shape:v,maxPopoverPlacement:h,maxPopoverTrigger:C,children:g,max:b}=e,w=i("avatar",s),E=`${w}-group`,y=Wt(w),[x,I,R]=Hc(w,y),k=B(E,{[`${E}-rtl`]:a==="rtl"},R,y,l,c,I),T=to(g).map((j,M)=>Zn(j,{key:`avatar-key-${M}`})),O=(b==null?void 0:b.count)||f,N=T.length;if(O&&O<N){const j=T.slice(0,O),M=T.slice(O,N),L=(b==null?void 0:b.style)||m,z=((t=b==null?void 0:b.popover)===null||t===void 0?void 0:t.trigger)||C||"hover",D=((n=b==null?void 0:b.popover)===null||n===void 0?void 0:n.placement)||h||"top",W=Object.assign(Object.assign({content:M},b==null?void 0:b.popover),{classNames:{root:B(`${E}-popover`,(o=(r=b==null?void 0:b.popover)===null||r===void 0?void 0:r.classNames)===null||o===void 0?void 0:o.root)},placement:D,trigger:z});return j.push(d.createElement(Xc,Object.assign({key:"avatar-popover-key",destroyOnHidden:!0},W),d.createElement(Wc,{style:L},`+${N-O}`))),x(d.createElement(qa,{shape:v,size:p},d.createElement("div",{className:k,style:u},j)))}return x(d.createElement(qa,{shape:v,size:p},d.createElement("div",{className:k,style:u},T)))},Vr=Wc;Vr.Group=Uh;const no=d.createContext(null);var Xh=function(t){var n=t.activeTabOffset,r=t.horizontal,o=t.rtl,i=t.indicator,a=i===void 0?{}:i,s=a.size,l=a.align,c=l===void 0?"center":l,u=d.useState(),f=ve(u,2),m=f[0],p=f[1],v=d.useRef(),h=P.useCallback(function(g){return typeof s=="function"?s(g):typeof s=="number"?s:g},[s]);function C(){ni.cancel(v.current)}return d.useEffect(function(){var g={};if(n)if(r){g.width=h(n.width);var b=o?"right":"left";c==="start"&&(g[b]=n[b]),c==="center"&&(g[b]=n[b]+n.width/2,g.transform=o?"translateX(50%)":"translateX(-50%)"),c==="end"&&(g[b]=n[b]+n.width,g.transform="translateX(-100%)")}else g.height=h(n.height),c==="start"&&(g.top=n.top),c==="center"&&(g.top=n.top+n.height/2,g.transform="translateY(-50%)"),c==="end"&&(g.top=n.top+n.height,g.transform="translateY(-100%)");return C(),v.current=ni(function(){var w=m&&g&&Object.keys(g).every(function(E){var y=g[E],x=m[E];return typeof y=="number"&&typeof x=="number"?Math.round(y)===Math.round(x):y===x});w||p(g)}),C},[JSON.stringify(n),r,o,c,h]),{style:m}},Ya={width:0,height:0,left:0,top:0};function Gh(e,t,n){return d.useMemo(function(){for(var r,o=new Map,i=t.get((r=e[0])===null||r===void 0?void 0:r.key)||Ya,a=i.left+i.width,s=0;s<e.length;s+=1){var l=e[s].key,c=t.get(l);if(!c){var u;c=t.get((u=e[s-1])===null||u===void 0?void 0:u.key)||Ya}var f=o.get(l)||te({},c);f.right=a-f.left-f.width,o.set(l,f)}return o},[e.map(function(r){return r.key}).join("_"),t,n])}function Ka(e,t){var n=d.useRef(e),r=d.useState({}),o=ve(r,2),i=o[1];function a(s){var l=typeof s=="function"?s(n.current):s;l!==n.current&&t(l,n.current),n.current=l,i({})}return[n.current,a]}var qh=.1,Za=.01,jr=20,Ja=Math.pow(.995,jr);function Yh(e,t){var n=d.useState(),r=ve(n,2),o=r[0],i=r[1],a=d.useState(0),s=ve(a,2),l=s[0],c=s[1],u=d.useState(0),f=ve(u,2),m=f[0],p=f[1],v=d.useState(),h=ve(v,2),C=h[0],g=h[1],b=d.useRef();function w(k){var T=k.touches[0],O=T.screenX,N=T.screenY;i({x:O,y:N}),window.clearInterval(b.current)}function E(k){if(o){var T=k.touches[0],O=T.screenX,N=T.screenY;i({x:O,y:N});var j=O-o.x,M=N-o.y;t(j,M);var L=Date.now();c(L),p(L-l),g({x:j,y:M})}}function y(){if(o&&(i(null),g(null),C)){var k=C.x/m,T=C.y/m,O=Math.abs(k),N=Math.abs(T);if(Math.max(O,N)<qh)return;var j=k,M=T;b.current=window.setInterval(function(){if(Math.abs(j)<Za&&Math.abs(M)<Za){window.clearInterval(b.current);return}j*=Ja,M*=Ja,t(j*jr,M*jr)},jr)}}var x=d.useRef();function I(k){var T=k.deltaX,O=k.deltaY,N=0,j=Math.abs(T),M=Math.abs(O);j===M?N=x.current==="x"?T:O:j>M?(N=T,x.current="x"):(N=O,x.current="y"),t(-N,-N)&&k.preventDefault()}var R=d.useRef(null);R.current={onTouchStart:w,onTouchMove:E,onTouchEnd:y,onWheel:I},d.useEffect(function(){function k(j){R.current.onTouchStart(j)}function T(j){R.current.onTouchMove(j)}function O(j){R.current.onTouchEnd(j)}function N(j){R.current.onWheel(j)}return document.addEventListener("touchmove",T,{passive:!1}),document.addEventListener("touchend",O,{passive:!0}),e.current.addEventListener("touchstart",k,{passive:!0}),e.current.addEventListener("wheel",N,{passive:!1}),function(){document.removeEventListener("touchmove",T),document.removeEventListener("touchend",O)}},[])}function Gc(e){var t=d.useState(0),n=ve(t,2),r=n[0],o=n[1],i=d.useRef(0),a=d.useRef();return a.current=e,cf(function(){var s;(s=a.current)===null||s===void 0||s.call(a)},[r]),function(){i.current===r&&(i.current+=1,o(i.current))}}function Kh(e){var t=d.useRef([]),n=d.useState({}),r=ve(n,2),o=r[1],i=d.useRef(typeof e=="function"?e():e),a=Gc(function(){var l=i.current;t.current.forEach(function(c){l=c(l)}),t.current=[],i.current=l,o({})});function s(l){t.current.push(l),a()}return[i.current,s]}var Qa={width:0,height:0,left:0,top:0,right:0};function Zh(e,t,n,r,o,i,a){var s=a.tabs,l=a.tabPosition,c=a.rtl,u,f,m;return["top","bottom"].includes(l)?(u="width",f=c?"right":"left",m=Math.abs(n)):(u="height",f="top",m=-n),d.useMemo(function(){if(!s.length)return[0,0];for(var p=s.length,v=p,h=0;h<p;h+=1){var C=e.get(s[h].key)||Qa;if(Math.floor(C[f]+C[u])>Math.floor(m+t)){v=h-1;break}}for(var g=0,b=p-1;b>=0;b-=1){var w=e.get(s[b].key)||Qa;if(w[f]<m){g=b+1;break}}return g>=v?[0,0]:[g,v]},[e,t,r,o,i,m,l,s.map(function(p){return p.key}).join("_"),c])}function es(e){var t;return e instanceof Map?(t={},e.forEach(function(n,r){t[r]=n})):t=e,JSON.stringify(t)}var Jh="TABS_DQ";function qc(e){return String(e).replace(/"/g,Jh)}function Wi(e,t,n,r){return!(!n||r||e===!1||e===void 0&&(t===!1||t===null))}var Yc=d.forwardRef(function(e,t){var n=e.prefixCls,r=e.editable,o=e.locale,i=e.style;return!r||r.showAdd===!1?null:d.createElement("button",{ref:t,type:"button",className:"".concat(n,"-nav-add"),style:i,"aria-label":(o==null?void 0:o.addAriaLabel)||"Add tab",onClick:function(s){r.onEdit("add",{event:s})}},r.addIcon||"+")}),ts=d.forwardRef(function(e,t){var n=e.position,r=e.prefixCls,o=e.extra;if(!o)return null;var i,a={};return et(o)==="object"&&!d.isValidElement(o)?a=o:a.right=o,n==="right"&&(i=a.right),n==="left"&&(i=a.left),i?d.createElement("div",{className:"".concat(r,"-extra-content"),ref:t},i):null}),Qh=d.forwardRef(function(e,t){var n=e.prefixCls,r=e.id,o=e.tabs,i=e.locale,a=e.mobile,s=e.more,l=s===void 0?{}:s,c=e.style,u=e.className,f=e.editable,m=e.tabBarGutter,p=e.rtl,v=e.removeAriaLabel,h=e.onTabClick,C=e.getPopupContainer,g=e.popupClassName,b=d.useState(!1),w=ve(b,2),E=w[0],y=w[1],x=d.useState(null),I=ve(x,2),R=I[0],k=I[1],T=l.icon,O=T===void 0?"More":T,N="".concat(r,"-more-popup"),j="".concat(n,"-dropdown"),M=R!==null?"".concat(N,"-").concat(R):null,L=i==null?void 0:i.dropdownAriaLabel;function z(S,Y){S.preventDefault(),S.stopPropagation(),f.onEdit("remove",{key:Y,event:S})}var D=d.createElement(uf,{onClick:function(Y){var ne=Y.key,K=Y.domEvent;h(ne,K),y(!1)},prefixCls:"".concat(j,"-menu"),id:N,tabIndex:-1,role:"listbox","aria-activedescendant":M,selectedKeys:[R],"aria-label":L!==void 0?L:"expanded dropdown"},o.map(function(S){var Y=S.closable,ne=S.disabled,K=S.closeIcon,ue=S.key,ce=S.label,me=Wi(Y,K,f,ne);return d.createElement(df,{key:ue,id:"".concat(N,"-").concat(ue),role:"option","aria-controls":r&&"".concat(r,"-panel-").concat(ue),disabled:ne},d.createElement("span",null,ce),me&&d.createElement("button",{type:"button","aria-label":v||"remove",tabIndex:0,className:"".concat(j,"-menu-item-remove"),onClick:function(se){se.stopPropagation(),z(se,ue)}},K||f.removeIcon||"×"))}));function W(S){for(var Y=o.filter(function(me){return!me.disabled}),ne=Y.findIndex(function(me){return me.key===R})||0,K=Y.length,ue=0;ue<K;ue+=1){ne=(ne+S+K)%K;var ce=Y[ne];if(!ce.disabled){k(ce.key);return}}}function F(S){var Y=S.which;if(!E){[it.DOWN,it.SPACE,it.ENTER].includes(Y)&&(y(!0),S.preventDefault());return}switch(Y){case it.UP:W(-1),S.preventDefault();break;case it.DOWN:W(1),S.preventDefault();break;case it.ESC:y(!1);break;case it.SPACE:case it.ENTER:R!==null&&h(R,S);break}}d.useEffect(function(){var S=document.getElementById(M);S&&S.scrollIntoView&&S.scrollIntoView(!1)},[R]),d.useEffect(function(){E||k(null)},[E]);var $=Ee({},p?"marginRight":"marginLeft",m);o.length||($.visibility="hidden",$.order=1);var G=B(Ee({},"".concat(j,"-rtl"),p)),q=a?null:d.createElement(ff,ae({prefixCls:j,overlay:D,visible:o.length?E:!1,onVisibleChange:y,overlayClassName:B(G,g),mouseEnterDelay:.1,mouseLeaveDelay:.1,getPopupContainer:C},l),d.createElement("button",{type:"button",className:"".concat(n,"-nav-more"),style:$,"aria-haspopup":"listbox","aria-controls":N,id:"".concat(r,"-more"),"aria-expanded":E,onKeyDown:F},O));return d.createElement("div",{className:B("".concat(n,"-nav-operations"),u),style:c,ref:t},q,d.createElement(Yc,{prefixCls:n,locale:i,editable:f}))});const eg=d.memo(Qh,function(e,t){return t.tabMoving});var tg=function(t){var n=t.prefixCls,r=t.id,o=t.active,i=t.focus,a=t.tab,s=a.key,l=a.label,c=a.disabled,u=a.closeIcon,f=a.icon,m=t.closable,p=t.renderWrapper,v=t.removeAriaLabel,h=t.editable,C=t.onClick,g=t.onFocus,b=t.onBlur,w=t.onKeyDown,E=t.onMouseDown,y=t.onMouseUp,x=t.style,I=t.tabCount,R=t.currentPosition,k="".concat(n,"-tab"),T=Wi(m,u,h,c);function O(z){c||C(z)}function N(z){z.preventDefault(),z.stopPropagation(),h.onEdit("remove",{key:s,event:z})}var j=d.useMemo(function(){return f&&typeof l=="string"?d.createElement("span",null,l):l},[l,f]),M=d.useRef(null);d.useEffect(function(){i&&M.current&&M.current.focus()},[i]);var L=d.createElement("div",{key:s,"data-node-key":qc(s),className:B(k,Ee(Ee(Ee(Ee({},"".concat(k,"-with-remove"),T),"".concat(k,"-active"),o),"".concat(k,"-disabled"),c),"".concat(k,"-focus"),i)),style:x,onClick:O},d.createElement("div",{ref:M,role:"tab","aria-selected":o,id:r&&"".concat(r,"-tab-").concat(s),className:"".concat(k,"-btn"),"aria-controls":r&&"".concat(r,"-panel-").concat(s),"aria-disabled":c,tabIndex:c?null:o?0:-1,onClick:function(D){D.stopPropagation(),O(D)},onKeyDown:w,onMouseDown:E,onMouseUp:y,onFocus:g,onBlur:b},i&&d.createElement("div",{"aria-live":"polite",style:{width:0,height:0,position:"absolute",overflow:"hidden",opacity:0}},"Tab ".concat(R," of ").concat(I)),f&&d.createElement("span",{className:"".concat(k,"-icon")},f),l&&j),T&&d.createElement("button",{type:"button",role:"tab","aria-label":v||"remove",tabIndex:o?0:-1,className:"".concat(k,"-remove"),onClick:function(D){D.stopPropagation(),N(D)}},u||h.removeIcon||"×"));return p?p(L):L},ng=function(t,n){var r=t.offsetWidth,o=t.offsetHeight,i=t.offsetTop,a=t.offsetLeft,s=t.getBoundingClientRect(),l=s.width,c=s.height,u=s.left,f=s.top;return Math.abs(l-r)<1?[l,c,u-n.left,f-n.top]:[r,o,a,i]},pn=function(t){var n=t.current||{},r=n.offsetWidth,o=r===void 0?0:r,i=n.offsetHeight,a=i===void 0?0:i;if(t.current){var s=t.current.getBoundingClientRect(),l=s.width,c=s.height;if(Math.abs(l-o)<1)return[l,c]}return[o,a]},xr=function(t,n){return t[n?0:1]},ns=d.forwardRef(function(e,t){var n=e.className,r=e.style,o=e.id,i=e.animated,a=e.activeKey,s=e.rtl,l=e.extra,c=e.editable,u=e.locale,f=e.tabPosition,m=e.tabBarGutter,p=e.children,v=e.onTabClick,h=e.onTabScroll,C=e.indicator,g=d.useContext(no),b=g.prefixCls,w=g.tabs,E=d.useRef(null),y=d.useRef(null),x=d.useRef(null),I=d.useRef(null),R=d.useRef(null),k=d.useRef(null),T=d.useRef(null),O=f==="top"||f==="bottom",N=Ka(0,function(_e,Se){O&&h&&h({direction:_e>Se?"left":"right"})}),j=ve(N,2),M=j[0],L=j[1],z=Ka(0,function(_e,Se){!O&&h&&h({direction:_e>Se?"top":"bottom"})}),D=ve(z,2),W=D[0],F=D[1],$=d.useState([0,0]),G=ve($,2),q=G[0],S=G[1],Y=d.useState([0,0]),ne=ve(Y,2),K=ne[0],ue=ne[1],ce=d.useState([0,0]),me=ve(ce,2),fe=me[0],se=me[1],ee=d.useState([0,0]),de=ve(ee,2),oe=de[0],ie=de[1],be=Kh(new Map),Ie=ve(be,2),ye=Ie[0],Oe=Ie[1],xe=Gh(w,ye,K[0]),$e=xr(q,O),Ae=xr(K,O),Re=xr(fe,O),Ue=xr(oe,O),Ke=Math.floor($e)<Math.floor(Ae+Re),_=Ke?$e-Ue:$e-Re,U="".concat(b,"-nav-operations-hidden"),le=0,he=0;O&&s?(le=0,he=Math.max(0,Ae-_)):(le=Math.min(0,_-Ae),he=0);function Ce(_e){return _e<le?le:_e>he?he:_e}var ze=d.useRef(null),V=d.useState(),re=ve(V,2),Z=re[0],pe=re[1];function X(){pe(Date.now())}function J(){ze.current&&clearTimeout(ze.current)}Yh(I,function(_e,Se){function Fe(Xe,Tt){Xe(function(Ct){var dn=Ce(Ct+Tt);return dn})}return Ke?(O?Fe(L,_e):Fe(F,Se),J(),X(),!0):!1}),d.useEffect(function(){return J(),Z&&(ze.current=setTimeout(function(){pe(0)},100)),J},[Z]);var ge=Zh(xe,_,O?M:W,Ae,Re,Ue,te(te({},e),{},{tabs:w})),Te=ve(ge,2),We=Te[0],nt=Te[1],mt=tn(function(){var _e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:a,Se=xe.get(_e)||{width:0,height:0,left:0,right:0,top:0};if(O){var Fe=M;s?Se.right<M?Fe=Se.right:Se.right+Se.width>M+_&&(Fe=Se.right+Se.width-_):Se.left<-M?Fe=-Se.left:Se.left+Se.width>-M+_&&(Fe=-(Se.left+Se.width-_)),F(0),L(Ce(Fe))}else{var Xe=W;Se.top<-W?Xe=-Se.top:Se.top+Se.height>-W+_&&(Xe=-(Se.top+Se.height-_)),L(0),F(Ce(Xe))}}),Nn=d.useState(),kn=ve(Nn,2),Pt=kn[0],Vt=kn[1],hr=d.useState(!1),Mn=ve(hr,2),ht=Mn[0],jn=Mn[1],Bt=w.filter(function(_e){return!_e.disabled}).map(function(_e){return _e.key}),un=function(Se){var Fe=Bt.indexOf(Pt||a),Xe=Bt.length,Tt=(Fe+Se+Xe)%Xe,Ct=Bt[Tt];Vt(Ct)},jd=function(Se){var Fe=Se.code,Xe=s&&O,Tt=Bt[0],Ct=Bt[Bt.length-1];switch(Fe){case"ArrowLeft":{O&&un(Xe?1:-1);break}case"ArrowRight":{O&&un(Xe?-1:1);break}case"ArrowUp":{Se.preventDefault(),O||un(-1);break}case"ArrowDown":{Se.preventDefault(),O||un(1);break}case"Home":{Se.preventDefault(),Vt(Tt);break}case"End":{Se.preventDefault(),Vt(Ct);break}case"Enter":case"Space":{Se.preventDefault(),v(Pt??a,Se);break}case"Backspace":case"Delete":{var dn=Bt.indexOf(Pt),St=w.find(function(fn){return fn.key===Pt}),bo=Wi(St==null?void 0:St.closable,St==null?void 0:St.closeIcon,c,St==null?void 0:St.disabled);bo&&(Se.preventDefault(),Se.stopPropagation(),c.onEdit("remove",{key:Pt,event:Se}),dn===Bt.length-1?un(-1):un(1));break}}},gr={};O?gr[s?"marginRight":"marginLeft"]=m:gr.marginTop=m;var xa=w.map(function(_e,Se){var Fe=_e.key;return d.createElement(tg,{id:o,prefixCls:b,key:Fe,tab:_e,style:Se===0?void 0:gr,closable:_e.closable,editable:c,active:Fe===a,focus:Fe===Pt,renderWrapper:p,removeAriaLabel:u==null?void 0:u.removeAriaLabel,tabCount:Bt.length,currentPosition:Se+1,onClick:function(Tt){v(Fe,Tt)},onKeyDown:jd,onFocus:function(){ht||Vt(Fe),mt(Fe),X(),I.current&&(s||(I.current.scrollLeft=0),I.current.scrollTop=0)},onBlur:function(){Vt(void 0)},onMouseDown:function(){jn(!0)},onMouseUp:function(){jn(!1)}})}),Ca=function(){return Oe(function(){var Se,Fe=new Map,Xe=(Se=R.current)===null||Se===void 0?void 0:Se.getBoundingClientRect();return w.forEach(function(Tt){var Ct,dn=Tt.key,St=(Ct=R.current)===null||Ct===void 0?void 0:Ct.querySelector('[data-node-key="'.concat(qc(dn),'"]'));if(St){var bo=ng(St,Xe),fn=ve(bo,4),Dd=fn[0],Bd=fn[1],Fd=fn[2],Hd=fn[3];Fe.set(dn,{width:Dd,height:Bd,left:Fd,top:Hd})}}),Fe})};d.useEffect(function(){Ca()},[w.map(function(_e){return _e.key}).join("_")]);var vr=Gc(function(){var _e=pn(E),Se=pn(y),Fe=pn(x);S([_e[0]-Se[0]-Fe[0],_e[1]-Se[1]-Fe[1]]);var Xe=pn(T);se(Xe);var Tt=pn(k);ie(Tt);var Ct=pn(R);ue([Ct[0]-Xe[0],Ct[1]-Xe[1]]),Ca()}),Ld=w.slice(0,We),_d=w.slice(nt+1),Sa=[].concat(He(Ld),He(_d)),wa=xe.get(a),Ad=Xh({activeTabOffset:wa,horizontal:O,indicator:C,rtl:s}),zd=Ad.style;d.useEffect(function(){mt()},[a,le,he,es(wa),es(xe),O]),d.useEffect(function(){vr()},[s]);var $a=!!Sa.length,Ln="".concat(b,"-nav-wrap"),vo,yo,Ea,Ia;return O?s?(yo=M>0,vo=M!==he):(vo=M<0,yo=M!==le):(Ea=W<0,Ia=W!==le),d.createElement(Un,{onResize:vr},d.createElement("div",{ref:Dl(t,E),role:"tablist","aria-orientation":O?"horizontal":"vertical",className:B("".concat(b,"-nav"),n),style:r,onKeyDown:function(){X()}},d.createElement(ts,{ref:y,position:"left",extra:l,prefixCls:b}),d.createElement(Un,{onResize:vr},d.createElement("div",{className:B(Ln,Ee(Ee(Ee(Ee({},"".concat(Ln,"-ping-left"),vo),"".concat(Ln,"-ping-right"),yo),"".concat(Ln,"-ping-top"),Ea),"".concat(Ln,"-ping-bottom"),Ia)),ref:I},d.createElement(Un,{onResize:vr},d.createElement("div",{ref:R,className:"".concat(b,"-nav-list"),style:{transform:"translate(".concat(M,"px, ").concat(W,"px)"),transition:Z?"none":void 0}},xa,d.createElement(Yc,{ref:T,prefixCls:b,locale:u,editable:c,style:te(te({},xa.length===0?void 0:gr),{},{visibility:$a?"hidden":null})}),d.createElement("div",{className:B("".concat(b,"-ink-bar"),Ee({},"".concat(b,"-ink-bar-animated"),i.inkBar)),style:zd}))))),d.createElement(eg,ae({},e,{removeAriaLabel:u==null?void 0:u.removeAriaLabel,ref:k,prefixCls:b,tabs:Sa,className:!$a&&U,tabMoving:!!Z})),d.createElement(ts,{ref:x,position:"right",extra:l,prefixCls:b})))}),Kc=d.forwardRef(function(e,t){var n=e.prefixCls,r=e.className,o=e.style,i=e.id,a=e.active,s=e.tabKey,l=e.children;return d.createElement("div",{id:i&&"".concat(i,"-panel-").concat(s),role:"tabpanel",tabIndex:a?0:-1,"aria-labelledby":i&&"".concat(i,"-tab-").concat(s),"aria-hidden":!a,style:o,className:B(n,a&&"".concat(n,"-active"),r),ref:t},l)}),rg=["renderTabBar"],og=["label","key"],ig=function(t){var n=t.renderTabBar,r=st(t,rg),o=d.useContext(no),i=o.tabs;if(n){var a=te(te({},r),{},{panes:i.map(function(s){var l=s.label,c=s.key,u=st(s,og);return d.createElement(Kc,ae({tab:l,key:c,tabKey:c},u))})});return n(a,ns)}return d.createElement(ns,r)},ag=["key","forceRender","style","className","destroyInactiveTabPane"],sg=function(t){var n=t.id,r=t.activeKey,o=t.animated,i=t.tabPosition,a=t.destroyInactiveTabPane,s=d.useContext(no),l=s.prefixCls,c=s.tabs,u=o.tabPane,f="".concat(l,"-tabpane");return d.createElement("div",{className:B("".concat(l,"-content-holder"))},d.createElement("div",{className:B("".concat(l,"-content"),"".concat(l,"-content-").concat(i),Ee({},"".concat(l,"-content-animated"),u))},c.map(function(m){var p=m.key,v=m.forceRender,h=m.style,C=m.className,g=m.destroyInactiveTabPane,b=st(m,ag),w=p===r;return d.createElement(on,ae({key:p,visible:w,forceRender:v,removeOnLeave:!!(a||g),leavedClassName:"".concat(f,"-hidden")},o.tabPaneMotion),function(E,y){var x=E.style,I=E.className;return d.createElement(Kc,ae({},b,{prefixCls:f,id:n,tabKey:p,animated:u,active:w,style:te(te({},h),x),className:B(C,I),ref:y}))})})))};function lg(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{inkBar:!0,tabPane:!1},t;return e===!1?t={inkBar:!1,tabPane:!1}:e===!0?t={inkBar:!0,tabPane:!1}:t=te({inkBar:!0},et(e)==="object"?e:{}),t.tabPaneMotion&&t.tabPane===void 0&&(t.tabPane=!0),!t.tabPaneMotion&&t.tabPane&&(t.tabPane=!1),t}var cg=["id","prefixCls","className","items","direction","activeKey","defaultActiveKey","editable","animated","tabPosition","tabBarGutter","tabBarStyle","tabBarExtraContent","locale","more","destroyInactiveTabPane","renderTabBar","onChange","onTabClick","onTabScroll","getPopupContainer","popupClassName","indicator"],rs=0,ug=d.forwardRef(function(e,t){var n=e.id,r=e.prefixCls,o=r===void 0?"rc-tabs":r,i=e.className,a=e.items,s=e.direction,l=e.activeKey,c=e.defaultActiveKey,u=e.editable,f=e.animated,m=e.tabPosition,p=m===void 0?"top":m,v=e.tabBarGutter,h=e.tabBarStyle,C=e.tabBarExtraContent,g=e.locale,b=e.more,w=e.destroyInactiveTabPane,E=e.renderTabBar,y=e.onChange,x=e.onTabClick,I=e.onTabScroll,R=e.getPopupContainer,k=e.popupClassName,T=e.indicator,O=st(e,cg),N=d.useMemo(function(){return(a||[]).filter(function(oe){return oe&&et(oe)==="object"&&"key"in oe})},[a]),j=s==="rtl",M=lg(f),L=d.useState(!1),z=ve(L,2),D=z[0],W=z[1];d.useEffect(function(){W(pf())},[]);var F=bt(function(){var oe;return(oe=N[0])===null||oe===void 0?void 0:oe.key},{value:l,defaultValue:c}),$=ve(F,2),G=$[0],q=$[1],S=d.useState(function(){return N.findIndex(function(oe){return oe.key===G})}),Y=ve(S,2),ne=Y[0],K=Y[1];d.useEffect(function(){var oe=N.findIndex(function(be){return be.key===G});if(oe===-1){var ie;oe=Math.max(0,Math.min(ne,N.length-1)),q((ie=N[oe])===null||ie===void 0?void 0:ie.key)}K(oe)},[N.map(function(oe){return oe.key}).join("_"),G,ne]);var ue=bt(null,{value:n}),ce=ve(ue,2),me=ce[0],fe=ce[1];d.useEffect(function(){n||(fe("rc-tabs-".concat(rs)),rs+=1)},[]);function se(oe,ie){x==null||x(oe,ie);var be=oe!==G;q(oe),be&&(y==null||y(oe))}var ee={id:me,activeKey:G,animated:M,tabPosition:p,rtl:j,mobile:D},de=te(te({},ee),{},{editable:u,locale:g,more:b,tabBarGutter:v,onTabClick:se,onTabScroll:I,extra:C,style:h,panes:null,getPopupContainer:R,popupClassName:k,indicator:T});return d.createElement(no.Provider,{value:{tabs:N,prefixCls:o}},d.createElement("div",ae({ref:t,id:n,className:B(o,"".concat(o,"-").concat(p),Ee(Ee(Ee({},"".concat(o,"-mobile"),D),"".concat(o,"-editable"),u),"".concat(o,"-rtl"),j),i)},O),d.createElement(ig,ae({},de,{renderTabBar:E})),d.createElement(sg,ae({destroyInactiveTabPane:w},ee,{animated:M}))))});const dg={motionAppear:!1,motionEnter:!0,motionLeave:!0};function fg(e,t={inkBar:!0,tabPane:!1}){let n;return t===!1?n={inkBar:!1,tabPane:!1}:t===!0?n={inkBar:!0,tabPane:!0}:n=Object.assign({inkBar:!0},typeof t=="object"?t:{}),n.tabPane&&(n.tabPaneMotion=Object.assign(Object.assign({},dg),{motionName:At(e,"switch")})),n}var pg=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};function mg(e){return e.filter(t=>t)}function hg(e,t){if(e)return e.map(r=>{var o;const i=(o=r.destroyOnHidden)!==null&&o!==void 0?o:r.destroyInactiveTabPane;return Object.assign(Object.assign({},r),{destroyInactiveTabPane:i})});const n=to(t).map(r=>{if(d.isValidElement(r)){const{key:o,props:i}=r,a=i||{},{tab:s}=a,l=pg(a,["tab"]);return Object.assign(Object.assign({key:String(o)},l),{label:s})}return null});return mg(n)}const gg=e=>{const{componentCls:t,motionDurationSlow:n}=e;return[{[t]:{[`${t}-switch`]:{"&-appear, &-enter":{transition:"none","&-start":{opacity:0},"&-active":{opacity:1,transition:`opacity ${n}`}},"&-leave":{position:"absolute",transition:"none",inset:0,"&-start":{opacity:1},"&-active":{opacity:0,transition:`opacity ${n}`}}}}},[Ra(e,"slide-up"),Ra(e,"slide-down")]]},vg=e=>{const{componentCls:t,tabsCardPadding:n,cardBg:r,cardGutter:o,colorBorderSecondary:i,itemSelectedColor:a}=e;return{[`${t}-card`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{margin:0,padding:n,background:r,border:`${H(e.lineWidth)} ${e.lineType} ${i}`,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOut}`},[`${t}-tab-active`]:{color:a,background:e.colorBgContainer},[`${t}-tab-focus:has(${t}-tab-btn:focus-visible)`]:Jl(e,-3),[`& ${t}-tab${t}-tab-focus ${t}-tab-btn:focus-visible`]:{outline:"none"},[`${t}-ink-bar`]:{visibility:"hidden"}},[`&${t}-top, &${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginLeft:{_skip_check_:!0,value:H(o)}}}},[`&${t}-top`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:`${H(e.borderRadiusLG)} ${H(e.borderRadiusLG)} 0 0`},[`${t}-tab-active`]:{borderBottomColor:e.colorBgContainer}}},[`&${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:`0 0 ${H(e.borderRadiusLG)} ${H(e.borderRadiusLG)}`},[`${t}-tab-active`]:{borderTopColor:e.colorBgContainer}}},[`&${t}-left, &${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginTop:H(o)}}},[`&${t}-left`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`${H(e.borderRadiusLG)} 0 0 ${H(e.borderRadiusLG)}`}},[`${t}-tab-active`]:{borderRightColor:{_skip_check_:!0,value:e.colorBgContainer}}}},[`&${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`0 ${H(e.borderRadiusLG)} ${H(e.borderRadiusLG)} 0`}},[`${t}-tab-active`]:{borderLeftColor:{_skip_check_:!0,value:e.colorBgContainer}}}}}}},yg=e=>{const{componentCls:t,itemHoverColor:n,dropdownEdgeChildVerticalPadding:r}=e;return{[`${t}-dropdown`]:Object.assign(Object.assign({},Ht(e)),{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:e.zIndexPopup,display:"block","&-hidden":{display:"none"},[`${t}-dropdown-menu`]:{maxHeight:e.tabsDropdownHeight,margin:0,padding:`${H(r)} 0`,overflowX:"hidden",overflowY:"auto",textAlign:{_skip_check_:!0,value:"left"},listStyleType:"none",backgroundColor:e.colorBgContainer,backgroundClip:"padding-box",borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary,"&-item":Object.assign(Object.assign({},In),{display:"flex",alignItems:"center",minWidth:e.tabsDropdownWidth,margin:0,padding:`${H(e.paddingXXS)} ${H(e.paddingSM)}`,color:e.colorText,fontWeight:"normal",fontSize:e.fontSize,lineHeight:e.lineHeight,cursor:"pointer",transition:`all ${e.motionDurationSlow}`,"> span":{flex:1,whiteSpace:"nowrap"},"&-remove":{flex:"none",marginLeft:{_skip_check_:!0,value:e.marginSM},color:e.colorIcon,fontSize:e.fontSizeSM,background:"transparent",border:0,cursor:"pointer","&:hover":{color:n}},"&:hover":{background:e.controlItemBgHover},"&-disabled":{"&, &:hover":{color:e.colorTextDisabled,background:"transparent",cursor:"not-allowed"}}})}})}},bg=e=>{const{componentCls:t,margin:n,colorBorderSecondary:r,horizontalMargin:o,verticalItemPadding:i,verticalItemMargin:a,calc:s}=e;return{[`${t}-top, ${t}-bottom`]:{flexDirection:"column",[`> ${t}-nav, > div > ${t}-nav`]:{margin:o,"&::before":{position:"absolute",right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},borderBottom:`${H(e.lineWidth)} ${e.lineType} ${r}`,content:"''"},[`${t}-ink-bar`]:{height:e.lineWidthBold,"&-animated":{transition:`width ${e.motionDurationSlow}, left ${e.motionDurationSlow},
            right ${e.motionDurationSlow}`}},[`${t}-nav-wrap`]:{"&::before, &::after":{top:0,bottom:0,width:e.controlHeight},"&::before":{left:{_skip_check_:!0,value:0},boxShadow:e.boxShadowTabsOverflowLeft},"&::after":{right:{_skip_check_:!0,value:0},boxShadow:e.boxShadowTabsOverflowRight},[`&${t}-nav-wrap-ping-left::before`]:{opacity:1},[`&${t}-nav-wrap-ping-right::after`]:{opacity:1}}}},[`${t}-top`]:{[`> ${t}-nav,
        > div > ${t}-nav`]:{"&::before":{bottom:0},[`${t}-ink-bar`]:{bottom:0}}},[`${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{order:1,marginTop:n,marginBottom:0,"&::before":{top:0},[`${t}-ink-bar`]:{top:0}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{order:0}},[`${t}-left, ${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{flexDirection:"column",minWidth:s(e.controlHeight).mul(1.25).equal(),[`${t}-tab`]:{padding:i,textAlign:"center"},[`${t}-tab + ${t}-tab`]:{margin:a},[`${t}-nav-wrap`]:{flexDirection:"column","&::before, &::after":{right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},height:e.controlHeight},"&::before":{top:0,boxShadow:e.boxShadowTabsOverflowTop},"&::after":{bottom:0,boxShadow:e.boxShadowTabsOverflowBottom},[`&${t}-nav-wrap-ping-top::before`]:{opacity:1},[`&${t}-nav-wrap-ping-bottom::after`]:{opacity:1}},[`${t}-ink-bar`]:{width:e.lineWidthBold,"&-animated":{transition:`height ${e.motionDurationSlow}, top ${e.motionDurationSlow}`}},[`${t}-nav-list, ${t}-nav-operations`]:{flex:"1 0 auto",flexDirection:"column"}}},[`${t}-left`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-ink-bar`]:{right:{_skip_check_:!0,value:0}}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{marginLeft:{_skip_check_:!0,value:H(s(e.lineWidth).mul(-1).equal())},borderLeft:{_skip_check_:!0,value:`${H(e.lineWidth)} ${e.lineType} ${e.colorBorder}`},[`> ${t}-content > ${t}-tabpane`]:{paddingLeft:{_skip_check_:!0,value:e.paddingLG}}}},[`${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{order:1,[`${t}-ink-bar`]:{left:{_skip_check_:!0,value:0}}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{order:0,marginRight:{_skip_check_:!0,value:s(e.lineWidth).mul(-1).equal()},borderRight:{_skip_check_:!0,value:`${H(e.lineWidth)} ${e.lineType} ${e.colorBorder}`},[`> ${t}-content > ${t}-tabpane`]:{paddingRight:{_skip_check_:!0,value:e.paddingLG}}}}}},xg=e=>{const{componentCls:t,cardPaddingSM:n,cardPaddingLG:r,cardHeightSM:o,cardHeightLG:i,horizontalItemPaddingSM:a,horizontalItemPaddingLG:s}=e;return{[t]:{"&-small":{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:a,fontSize:e.titleFontSizeSM}}},"&-large":{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:s,fontSize:e.titleFontSizeLG,lineHeight:e.lineHeightLG}}}},[`${t}-card`]:{[`&${t}-small`]:{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:n},[`${t}-nav-add`]:{minWidth:o,minHeight:o}},[`&${t}-bottom`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:`0 0 ${H(e.borderRadius)} ${H(e.borderRadius)}`}},[`&${t}-top`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:`${H(e.borderRadius)} ${H(e.borderRadius)} 0 0`}},[`&${t}-right`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`0 ${H(e.borderRadius)} ${H(e.borderRadius)} 0`}}},[`&${t}-left`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`${H(e.borderRadius)} 0 0 ${H(e.borderRadius)}`}}}},[`&${t}-large`]:{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:r},[`${t}-nav-add`]:{minWidth:i,minHeight:i}}}}}},Cg=e=>{const{componentCls:t,itemActiveColor:n,itemHoverColor:r,iconCls:o,tabsHorizontalItemMargin:i,horizontalItemPadding:a,itemSelectedColor:s,itemColor:l}=e,c=`${t}-tab`;return{[c]:{position:"relative",WebkitTouchCallout:"none",WebkitTapHighlightColor:"transparent",display:"inline-flex",alignItems:"center",padding:a,fontSize:e.titleFontSize,background:"transparent",border:0,outline:"none",cursor:"pointer",color:l,"&-btn, &-remove":{"&:focus:not(:focus-visible), &:active":{color:n}},"&-btn":{outline:"none",transition:`all ${e.motionDurationSlow}`,[`${c}-icon:not(:last-child)`]:{marginInlineEnd:e.marginSM}},"&-remove":Object.assign({flex:"none",marginRight:{_skip_check_:!0,value:e.calc(e.marginXXS).mul(-1).equal()},marginLeft:{_skip_check_:!0,value:e.marginXS},color:e.colorIcon,fontSize:e.fontSizeSM,background:"transparent",border:"none",outline:"none",cursor:"pointer",transition:`all ${e.motionDurationSlow}`,"&:hover":{color:e.colorTextHeading}},Yn(e)),"&:hover":{color:r},[`&${c}-active ${c}-btn`]:{color:s,textShadow:e.tabsActiveTextShadow},[`&${c}-focus ${c}-btn:focus-visible`]:Jl(e),[`&${c}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed"},[`&${c}-disabled ${c}-btn, &${c}-disabled ${t}-remove`]:{"&:focus, &:active":{color:e.colorTextDisabled}},[`& ${c}-remove ${o}`]:{margin:0},[`${o}:not(:last-child)`]:{marginRight:{_skip_check_:!0,value:e.marginSM}}},[`${c} + ${c}`]:{margin:{_skip_check_:!0,value:i}}}},Sg=e=>{const{componentCls:t,tabsHorizontalItemMarginRTL:n,iconCls:r,cardGutter:o,calc:i}=e;return{[`${t}-rtl`]:{direction:"rtl",[`${t}-nav`]:{[`${t}-tab`]:{margin:{_skip_check_:!0,value:n},[`${t}-tab:last-of-type`]:{marginLeft:{_skip_check_:!0,value:0}},[r]:{marginRight:{_skip_check_:!0,value:0},marginLeft:{_skip_check_:!0,value:H(e.marginSM)}},[`${t}-tab-remove`]:{marginRight:{_skip_check_:!0,value:H(e.marginXS)},marginLeft:{_skip_check_:!0,value:H(i(e.marginXXS).mul(-1).equal())},[r]:{margin:0}}}},[`&${t}-left`]:{[`> ${t}-nav`]:{order:1},[`> ${t}-content-holder`]:{order:0}},[`&${t}-right`]:{[`> ${t}-nav`]:{order:0},[`> ${t}-content-holder`]:{order:1}},[`&${t}-card${t}-top, &${t}-card${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginRight:{_skip_check_:!0,value:o},marginLeft:{_skip_check_:!0,value:0}}}}},[`${t}-dropdown-rtl`]:{direction:"rtl"},[`${t}-menu-item`]:{[`${t}-dropdown-rtl`]:{textAlign:{_skip_check_:!0,value:"right"}}}}},wg=e=>{const{componentCls:t,tabsCardPadding:n,cardHeight:r,cardGutter:o,itemHoverColor:i,itemActiveColor:a,colorBorderSecondary:s}=e;return{[t]:Object.assign(Object.assign(Object.assign(Object.assign({},Ht(e)),{display:"flex",[`> ${t}-nav, > div > ${t}-nav`]:{position:"relative",display:"flex",flex:"none",alignItems:"center",[`${t}-nav-wrap`]:{position:"relative",display:"flex",flex:"auto",alignSelf:"stretch",overflow:"hidden",whiteSpace:"nowrap",transform:"translate(0)","&::before, &::after":{position:"absolute",zIndex:1,opacity:0,transition:`opacity ${e.motionDurationSlow}`,content:"''",pointerEvents:"none"}},[`${t}-nav-list`]:{position:"relative",display:"flex",transition:`opacity ${e.motionDurationSlow}`},[`${t}-nav-operations`]:{display:"flex",alignSelf:"stretch"},[`${t}-nav-operations-hidden`]:{position:"absolute",visibility:"hidden",pointerEvents:"none"},[`${t}-nav-more`]:{position:"relative",padding:n,background:"transparent",border:0,color:e.colorText,"&::after":{position:"absolute",right:{_skip_check_:!0,value:0},bottom:0,left:{_skip_check_:!0,value:0},height:e.calc(e.controlHeightLG).div(8).equal(),transform:"translateY(100%)",content:"''"}},[`${t}-nav-add`]:Object.assign({minWidth:r,minHeight:r,marginLeft:{_skip_check_:!0,value:o},background:"transparent",border:`${H(e.lineWidth)} ${e.lineType} ${s}`,borderRadius:`${H(e.borderRadiusLG)} ${H(e.borderRadiusLG)} 0 0`,outline:"none",cursor:"pointer",color:e.colorText,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOut}`,"&:hover":{color:i},"&:active, &:focus:not(:focus-visible)":{color:a}},Yn(e,-3))},[`${t}-extra-content`]:{flex:"none"},[`${t}-ink-bar`]:{position:"absolute",background:e.inkBarColor,pointerEvents:"none"}}),Cg(e)),{[`${t}-content`]:{position:"relative",width:"100%"},[`${t}-content-holder`]:{flex:"auto",minWidth:0,minHeight:0},[`${t}-tabpane`]:Object.assign(Object.assign({},Yn(e)),{"&-hidden":{display:"none"}})}),[`${t}-centered`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-nav-wrap`]:{[`&:not([class*='${t}-nav-wrap-ping']) > ${t}-nav-list`]:{margin:"auto"}}}}}},$g=e=>{const{cardHeight:t,cardHeightSM:n,cardHeightLG:r,controlHeight:o,controlHeightLG:i}=e,a=t||i,s=n||o,l=r||i+8;return{zIndexPopup:e.zIndexPopupBase+50,cardBg:e.colorFillAlter,cardHeight:a,cardHeightSM:s,cardHeightLG:l,cardPadding:`${(a-e.fontHeight)/2-e.lineWidth}px ${e.padding}px`,cardPaddingSM:`${(s-e.fontHeight)/2-e.lineWidth}px ${e.paddingXS}px`,cardPaddingLG:`${(l-e.fontHeightLG)/2-e.lineWidth}px ${e.padding}px`,titleFontSize:e.fontSize,titleFontSizeLG:e.fontSizeLG,titleFontSizeSM:e.fontSize,inkBarColor:e.colorPrimary,horizontalMargin:`0 0 ${e.margin}px 0`,horizontalItemGutter:32,horizontalItemMargin:"",horizontalItemMarginRTL:"",horizontalItemPadding:`${e.paddingSM}px 0`,horizontalItemPaddingSM:`${e.paddingXS}px 0`,horizontalItemPaddingLG:`${e.padding}px 0`,verticalItemPadding:`${e.paddingXS}px ${e.paddingLG}px`,verticalItemMargin:`${e.margin}px 0 0 0`,itemColor:e.colorText,itemSelectedColor:e.colorPrimary,itemHoverColor:e.colorPrimaryHover,itemActiveColor:e.colorPrimaryActive,cardGutter:e.marginXXS/2}},Eg=xt("Tabs",e=>{const t=tt(e,{tabsCardPadding:e.cardPadding,dropdownEdgeChildVerticalPadding:e.paddingXXS,tabsActiveTextShadow:"0 0 0.25px currentcolor",tabsDropdownHeight:200,tabsDropdownWidth:120,tabsHorizontalItemMargin:`0 0 0 ${H(e.horizontalItemGutter)}`,tabsHorizontalItemMarginRTL:`0 0 0 ${H(e.horizontalItemGutter)}`});return[xg(t),Sg(t),bg(t),yg(t),vg(t),wg(t),gg(t)]},$g),Ig=()=>null;var Pg=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const Ur=e=>{var t,n,r,o,i,a,s,l,c,u,f;const{type:m,className:p,rootClassName:v,size:h,onEdit:C,hideAdd:g,centered:b,addIcon:w,removeIcon:E,moreIcon:y,more:x,popupClassName:I,children:R,items:k,animated:T,style:O,indicatorSize:N,indicator:j,destroyInactiveTabPane:M,destroyOnHidden:L}=e,z=Pg(e,["type","className","rootClassName","size","onEdit","hideAdd","centered","addIcon","removeIcon","moreIcon","more","popupClassName","children","items","animated","style","indicatorSize","indicator","destroyInactiveTabPane","destroyOnHidden"]),{prefixCls:D}=z,{direction:W,tabs:F,getPrefixCls:$,getPopupContainer:G}=d.useContext(Ve),q=$("tabs",D),S=Wt(q),[Y,ne,K]=Eg(q,S);let ue;m==="editable-card"&&(ue={onEdit:(oe,{key:ie,event:be})=>{C==null||C(oe==="add"?be:ie,oe)},removeIcon:(t=E??(F==null?void 0:F.removeIcon))!==null&&t!==void 0?t:d.createElement(an,null),addIcon:(w??(F==null?void 0:F.addIcon))||d.createElement(Fr,null),showAdd:g!==!0});const ce=$(),me=ji(h),fe=hg(k,R),se=fg(q,T),ee=Object.assign(Object.assign({},F==null?void 0:F.style),O),de={align:(n=j==null?void 0:j.align)!==null&&n!==void 0?n:(r=F==null?void 0:F.indicator)===null||r===void 0?void 0:r.align,size:(s=(i=(o=j==null?void 0:j.size)!==null&&o!==void 0?o:N)!==null&&i!==void 0?i:(a=F==null?void 0:F.indicator)===null||a===void 0?void 0:a.size)!==null&&s!==void 0?s:F==null?void 0:F.indicatorSize};return Y(d.createElement(ug,Object.assign({direction:W,getPopupContainer:G},z,{items:fe,className:B({[`${q}-${me}`]:me,[`${q}-card`]:["card","editable-card"].includes(m),[`${q}-editable-card`]:m==="editable-card",[`${q}-centered`]:b},F==null?void 0:F.className,p,v,ne,K,S),popupClassName:B(I,ne,K,S),style:ee,editable:ue,more:Object.assign({icon:(f=(u=(c=(l=F==null?void 0:F.more)===null||l===void 0?void 0:l.icon)!==null&&c!==void 0?c:F==null?void 0:F.moreIcon)!==null&&u!==void 0?u:y)!==null&&f!==void 0?f:d.createElement(Ql,null),transitionName:`${ce}-slide-up`},x),prefixCls:q,animated:se,indicator:de,destroyInactiveTabPane:L??M})))};Ur.TabPane=Ig;var Tg=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const Zc=e=>{var{prefixCls:t,className:n,hoverable:r=!0}=e,o=Tg(e,["prefixCls","className","hoverable"]);const{getPrefixCls:i}=d.useContext(Ve),a=i("card",t),s=B(`${a}-grid`,n,{[`${a}-grid-hoverable`]:r});return d.createElement("div",Object.assign({},o,{className:s}))},Og=e=>{const{antCls:t,componentCls:n,headerHeight:r,headerPadding:o,tabsMarginBottom:i}=e;return Object.assign(Object.assign({display:"flex",justifyContent:"center",flexDirection:"column",minHeight:r,marginBottom:-1,padding:`0 ${H(o)}`,color:e.colorTextHeading,fontWeight:e.fontWeightStrong,fontSize:e.headerFontSize,background:e.headerBg,borderBottom:`${H(e.lineWidth)} ${e.lineType} ${e.colorBorderSecondary}`,borderRadius:`${H(e.borderRadiusLG)} ${H(e.borderRadiusLG)} 0 0`},sn()),{"&-wrapper":{width:"100%",display:"flex",alignItems:"center"},"&-title":Object.assign(Object.assign({display:"inline-block",flex:1},In),{[`
          > ${n}-typography,
          > ${n}-typography-edit-content
        `]:{insetInlineStart:0,marginTop:0,marginBottom:0}}),[`${t}-tabs-top`]:{clear:"both",marginBottom:i,color:e.colorText,fontWeight:"normal",fontSize:e.fontSize,"&-bar":{borderBottom:`${H(e.lineWidth)} ${e.lineType} ${e.colorBorderSecondary}`}}})},Rg=e=>{const{cardPaddingBase:t,colorBorderSecondary:n,cardShadow:r,lineWidth:o}=e;return{width:"33.33%",padding:t,border:0,borderRadius:0,boxShadow:`
      ${H(o)} 0 0 0 ${n},
      0 ${H(o)} 0 0 ${n},
      ${H(o)} ${H(o)} 0 0 ${n},
      ${H(o)} 0 0 0 ${n} inset,
      0 ${H(o)} 0 0 ${n} inset;
    `,transition:`all ${e.motionDurationMid}`,"&-hoverable:hover":{position:"relative",zIndex:1,boxShadow:r}}},Ng=e=>{const{componentCls:t,iconCls:n,actionsLiMargin:r,cardActionsIconSize:o,colorBorderSecondary:i,actionsBg:a}=e;return Object.assign(Object.assign({margin:0,padding:0,listStyle:"none",background:a,borderTop:`${H(e.lineWidth)} ${e.lineType} ${i}`,display:"flex",borderRadius:`0 0 ${H(e.borderRadiusLG)} ${H(e.borderRadiusLG)}`},sn()),{"& > li":{margin:r,color:e.colorTextDescription,textAlign:"center","> span":{position:"relative",display:"block",minWidth:e.calc(e.cardActionsIconSize).mul(2).equal(),fontSize:e.fontSize,lineHeight:e.lineHeight,cursor:"pointer","&:hover":{color:e.colorPrimary,transition:`color ${e.motionDurationMid}`},[`a:not(${t}-btn), > ${n}`]:{display:"inline-block",width:"100%",color:e.colorIcon,lineHeight:H(e.fontHeight),transition:`color ${e.motionDurationMid}`,"&:hover":{color:e.colorPrimary}},[`> ${n}`]:{fontSize:o,lineHeight:H(e.calc(o).mul(e.lineHeight).equal())}},"&:not(:last-child)":{borderInlineEnd:`${H(e.lineWidth)} ${e.lineType} ${i}`}}})},kg=e=>Object.assign(Object.assign({margin:`${H(e.calc(e.marginXXS).mul(-1).equal())} 0`,display:"flex"},sn()),{"&-avatar":{paddingInlineEnd:e.padding},"&-detail":{overflow:"hidden",flex:1,"> div:not(:last-child)":{marginBottom:e.marginXS}},"&-title":Object.assign({color:e.colorTextHeading,fontWeight:e.fontWeightStrong,fontSize:e.fontSizeLG},In),"&-description":{color:e.colorTextDescription}}),Mg=e=>{const{componentCls:t,colorFillAlter:n,headerPadding:r,bodyPadding:o}=e;return{[`${t}-head`]:{padding:`0 ${H(r)}`,background:n,"&-title":{fontSize:e.fontSize}},[`${t}-body`]:{padding:`${H(e.padding)} ${H(o)}`}}},jg=e=>{const{componentCls:t}=e;return{overflow:"hidden",[`${t}-body`]:{userSelect:"none"}}},Lg=e=>{const{componentCls:t,cardShadow:n,cardHeadPadding:r,colorBorderSecondary:o,boxShadowTertiary:i,bodyPadding:a,extraColor:s}=e;return{[t]:Object.assign(Object.assign({},Ht(e)),{position:"relative",background:e.colorBgContainer,borderRadius:e.borderRadiusLG,[`&:not(${t}-bordered)`]:{boxShadow:i},[`${t}-head`]:Og(e),[`${t}-extra`]:{marginInlineStart:"auto",color:s,fontWeight:"normal",fontSize:e.fontSize},[`${t}-body`]:Object.assign({padding:a,borderRadius:`0 0 ${H(e.borderRadiusLG)} ${H(e.borderRadiusLG)}`},sn()),[`${t}-grid`]:Rg(e),[`${t}-cover`]:{"> *":{display:"block",width:"100%",borderRadius:`${H(e.borderRadiusLG)} ${H(e.borderRadiusLG)} 0 0`}},[`${t}-actions`]:Ng(e),[`${t}-meta`]:kg(e)}),[`${t}-bordered`]:{border:`${H(e.lineWidth)} ${e.lineType} ${o}`,[`${t}-cover`]:{marginTop:-1,marginInlineStart:-1,marginInlineEnd:-1}},[`${t}-hoverable`]:{cursor:"pointer",transition:`box-shadow ${e.motionDurationMid}, border-color ${e.motionDurationMid}`,"&:hover":{borderColor:"transparent",boxShadow:n}},[`${t}-contain-grid`]:{borderRadius:`${H(e.borderRadiusLG)} ${H(e.borderRadiusLG)} 0 0 `,[`${t}-body`]:{display:"flex",flexWrap:"wrap"},[`&:not(${t}-loading) ${t}-body`]:{marginBlockStart:e.calc(e.lineWidth).mul(-1).equal(),marginInlineStart:e.calc(e.lineWidth).mul(-1).equal(),padding:0}},[`${t}-contain-tabs`]:{[`> div${t}-head`]:{minHeight:0,[`${t}-head-title, ${t}-extra`]:{paddingTop:r}}},[`${t}-type-inner`]:Mg(e),[`${t}-loading`]:jg(e),[`${t}-rtl`]:{direction:"rtl"}}},_g=e=>{const{componentCls:t,bodyPaddingSM:n,headerPaddingSM:r,headerHeightSM:o,headerFontSizeSM:i}=e;return{[`${t}-small`]:{[`> ${t}-head`]:{minHeight:o,padding:`0 ${H(r)}`,fontSize:i,[`> ${t}-head-wrapper`]:{[`> ${t}-extra`]:{fontSize:e.fontSize}}},[`> ${t}-body`]:{padding:n}},[`${t}-small${t}-contain-tabs`]:{[`> ${t}-head`]:{[`${t}-head-title, ${t}-extra`]:{paddingTop:0,display:"flex",alignItems:"center"}}}}},Ag=e=>{var t,n;return{headerBg:"transparent",headerFontSize:e.fontSizeLG,headerFontSizeSM:e.fontSize,headerHeight:e.fontSizeLG*e.lineHeightLG+e.padding*2,headerHeightSM:e.fontSize*e.lineHeight+e.paddingXS*2,actionsBg:e.colorBgContainer,actionsLiMargin:`${e.paddingSM}px 0`,tabsMarginBottom:-e.padding-e.lineWidth,extraColor:e.colorText,bodyPaddingSM:12,headerPaddingSM:12,bodyPadding:(t=e.bodyPadding)!==null&&t!==void 0?t:e.paddingLG,headerPadding:(n=e.headerPadding)!==null&&n!==void 0?n:e.paddingLG}},zg=xt("Card",e=>{const t=tt(e,{cardShadow:e.boxShadowCard,cardHeadPadding:e.padding,cardPaddingBase:e.paddingLG,cardActionsIconSize:e.fontSize});return[Lg(t),_g(t)]},Ag);var os=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const Dg=e=>{const{actionClasses:t,actions:n=[],actionStyle:r}=e;return d.createElement("ul",{className:t,style:r},n.map((o,i)=>{const a=`action-${i}`;return d.createElement("li",{style:{width:`${100/n.length}%`},key:a},d.createElement("span",null,o))}))},Bg=d.forwardRef((e,t)=>{const{prefixCls:n,className:r,rootClassName:o,style:i,extra:a,headStyle:s={},bodyStyle:l={},title:c,loading:u,bordered:f,variant:m,size:p,type:v,cover:h,actions:C,tabList:g,children:b,activeTabKey:w,defaultActiveTabKey:E,tabBarExtraContent:y,hoverable:x,tabProps:I={},classNames:R,styles:k}=e,T=os(e,["prefixCls","className","rootClassName","style","extra","headStyle","bodyStyle","title","loading","bordered","variant","size","type","cover","actions","tabList","children","activeTabKey","defaultActiveTabKey","tabBarExtraContent","hoverable","tabProps","classNames","styles"]),{getPrefixCls:O,direction:N,card:j}=d.useContext(Ve),[M]=mf("card",m,f),L=xe=>{var $e;($e=e.onTabChange)===null||$e===void 0||$e.call(e,xe)},z=xe=>{var $e;return B(($e=j==null?void 0:j.classNames)===null||$e===void 0?void 0:$e[xe],R==null?void 0:R[xe])},D=xe=>{var $e;return Object.assign(Object.assign({},($e=j==null?void 0:j.styles)===null||$e===void 0?void 0:$e[xe]),k==null?void 0:k[xe])},W=d.useMemo(()=>{let xe=!1;return d.Children.forEach(b,$e=>{($e==null?void 0:$e.type)===Zc&&(xe=!0)}),xe},[b]),F=O("card",n),[$,G,q]=zg(F),S=d.createElement(Wl,{loading:!0,active:!0,paragraph:{rows:4},title:!1},b),Y=w!==void 0,ne=Object.assign(Object.assign({},I),{[Y?"activeKey":"defaultActiveKey"]:Y?w:E,tabBarExtraContent:y});let K;const ue=ji(p),ce=!ue||ue==="default"?"large":ue,me=g?d.createElement(Ur,Object.assign({size:ce},ne,{className:`${F}-head-tabs`,onChange:L,items:g.map(xe=>{var{tab:$e}=xe,Ae=os(xe,["tab"]);return Object.assign({label:$e},Ae)})})):null;if(c||a||me){const xe=B(`${F}-head`,z("header")),$e=B(`${F}-head-title`,z("title")),Ae=B(`${F}-extra`,z("extra")),Re=Object.assign(Object.assign({},s),D("header"));K=d.createElement("div",{className:xe,style:Re},d.createElement("div",{className:`${F}-head-wrapper`},c&&d.createElement("div",{className:$e,style:D("title")},c),a&&d.createElement("div",{className:Ae,style:D("extra")},a)),me)}const fe=B(`${F}-cover`,z("cover")),se=h?d.createElement("div",{className:fe,style:D("cover")},h):null,ee=B(`${F}-body`,z("body")),de=Object.assign(Object.assign({},l),D("body")),oe=d.createElement("div",{className:ee,style:de},u?S:b),ie=B(`${F}-actions`,z("actions")),be=C!=null&&C.length?d.createElement(Dg,{actionClasses:ie,actionStyle:D("actions"),actions:C}):null,Ie=Pn(T,["onTabChange"]),ye=B(F,j==null?void 0:j.className,{[`${F}-loading`]:u,[`${F}-bordered`]:M!=="borderless",[`${F}-hoverable`]:x,[`${F}-contain-grid`]:W,[`${F}-contain-tabs`]:g==null?void 0:g.length,[`${F}-${ue}`]:ue,[`${F}-type-${v}`]:!!v,[`${F}-rtl`]:N==="rtl"},r,o,G,q),Oe=Object.assign(Object.assign({},j==null?void 0:j.style),i);return $(d.createElement("div",Object.assign({ref:t},Ie,{className:ye,style:Oe}),K,se,oe,be))});var Fg=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const Hg=e=>{const{prefixCls:t,className:n,avatar:r,title:o,description:i}=e,a=Fg(e,["prefixCls","className","avatar","title","description"]),{getPrefixCls:s}=d.useContext(Ve),l=s("card",t),c=B(`${l}-meta`,n),u=r?d.createElement("div",{className:`${l}-meta-avatar`},r):null,f=o?d.createElement("div",{className:`${l}-meta-title`},o):null,m=i?d.createElement("div",{className:`${l}-meta-description`},i):null,p=f||m?d.createElement("div",{className:`${l}-meta-detail`},f,m):null;return d.createElement("div",Object.assign({},a,{className:c}),u,p)},Vi=Bg;Vi.Grid=Zc;Vi.Meta=Hg;const Jc=d.createContext({});var Wg=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};function is(e){return typeof e=="number"?`${e} ${e} auto`:/^\d+(\.\d+)?(px|em|rem|%)$/.test(e)?`0 0 ${e}`:e}const Vg=["xs","sm","md","lg","xl","xxl"],Ug=d.forwardRef((e,t)=>{const{getPrefixCls:n,direction:r}=d.useContext(Ve),{gutter:o,wrap:i}=d.useContext(Jc),{prefixCls:a,span:s,order:l,offset:c,push:u,pull:f,className:m,children:p,flex:v,style:h}=e,C=Wg(e,["prefixCls","span","order","offset","push","pull","className","children","flex","style"]),g=n("col",a),[b,w,E]=qm(g),y={};let x={};Vg.forEach(k=>{let T={};const O=e[k];typeof O=="number"?T.span=O:typeof O=="object"&&(T=O||{}),delete C[k],x=Object.assign(Object.assign({},x),{[`${g}-${k}-${T.span}`]:T.span!==void 0,[`${g}-${k}-order-${T.order}`]:T.order||T.order===0,[`${g}-${k}-offset-${T.offset}`]:T.offset||T.offset===0,[`${g}-${k}-push-${T.push}`]:T.push||T.push===0,[`${g}-${k}-pull-${T.pull}`]:T.pull||T.pull===0,[`${g}-rtl`]:r==="rtl"}),T.flex&&(x[`${g}-${k}-flex`]=!0,y[`--${g}-${k}-flex`]=is(T.flex))});const I=B(g,{[`${g}-${s}`]:s!==void 0,[`${g}-order-${l}`]:l,[`${g}-offset-${c}`]:c,[`${g}-push-${u}`]:u,[`${g}-pull-${f}`]:f},m,x,w,E),R={};if(o&&o[0]>0){const k=o[0]/2;R.paddingLeft=k,R.paddingRight=k}return v&&(R.flex=is(v),i===!1&&!R.minWidth&&(R.minWidth=0)),b(d.createElement("div",Object.assign({},C,{style:Object.assign(Object.assign(Object.assign({},R),h),y),className:I,ref:t}),p))});function Xg(e,t){const n=[void 0,void 0],r=Array.isArray(e)?e:[e,void 0],o=t||{xs:!0,sm:!0,md:!0,lg:!0,xl:!0,xxl:!0};return r.forEach((i,a)=>{if(typeof i=="object"&&i!==null)for(let s=0;s<Kn.length;s++){const l=Kn[s];if(o[l]&&i[l]!==void 0){n[a]=i[l];break}}else n[a]=i}),n}var Gg=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};function as(e,t){const[n,r]=d.useState(typeof e=="string"?e:""),o=()=>{if(typeof e=="string"&&r(e),typeof e=="object")for(let i=0;i<Kn.length;i++){const a=Kn[i];if(!t||!t[a])continue;const s=e[a];if(s!==void 0){r(s);return}}};return d.useEffect(()=>{o()},[JSON.stringify(e),t]),n}const qg=d.forwardRef((e,t)=>{const{prefixCls:n,justify:r,align:o,className:i,style:a,children:s,gutter:l=0,wrap:c}=e,u=Gg(e,["prefixCls","justify","align","className","style","children","gutter","wrap"]),{getPrefixCls:f,direction:m}=d.useContext(Ve),p=Zl(!0,null),v=as(o,p),h=as(r,p),C=f("row",n),[g,b,w]=Gm(C),E=Xg(l,p),y=B(C,{[`${C}-no-wrap`]:c===!1,[`${C}-${h}`]:h,[`${C}-${v}`]:v,[`${C}-rtl`]:m==="rtl"},i,b,w),x={},I=E[0]!=null&&E[0]>0?E[0]/-2:void 0;I&&(x.marginLeft=I,x.marginRight=I);const[R,k]=E;x.rowGap=k;const T=d.useMemo(()=>({gutter:[R,k],wrap:c}),[R,k,c]);return g(d.createElement(Jc.Provider,{value:T},d.createElement("div",Object.assign({},u,{className:y,style:Object.assign(Object.assign({},x),a),ref:t}),s)))}),Qc=["wrap","nowrap","wrap-reverse"],eu=["flex-start","flex-end","start","end","center","space-between","space-around","space-evenly","stretch","normal","left","right"],tu=["center","start","end","flex-start","flex-end","self-start","self-end","baseline","normal","stretch"],Yg=(e,t)=>{const n=t.wrap===!0?"wrap":t.wrap;return{[`${e}-wrap-${n}`]:n&&Qc.includes(n)}},Kg=(e,t)=>{const n={};return tu.forEach(r=>{n[`${e}-align-${r}`]=t.align===r}),n[`${e}-align-stretch`]=!t.align&&!!t.vertical,n},Zg=(e,t)=>{const n={};return eu.forEach(r=>{n[`${e}-justify-${r}`]=t.justify===r}),n};function Jg(e,t){return B(Object.assign(Object.assign(Object.assign({},Yg(e,t)),Kg(e,t)),Zg(e,t)))}const Qg=e=>{const{componentCls:t}=e;return{[t]:{display:"flex",margin:0,padding:0,"&-vertical":{flexDirection:"column"},"&-rtl":{direction:"rtl"},"&:empty":{display:"none"}}}},e0=e=>{const{componentCls:t}=e;return{[t]:{"&-gap-small":{gap:e.flexGapSM},"&-gap-middle":{gap:e.flexGap},"&-gap-large":{gap:e.flexGapLG}}}},t0=e=>{const{componentCls:t}=e,n={};return Qc.forEach(r=>{n[`${t}-wrap-${r}`]={flexWrap:r}}),n},n0=e=>{const{componentCls:t}=e,n={};return tu.forEach(r=>{n[`${t}-align-${r}`]={alignItems:r}}),n},r0=e=>{const{componentCls:t}=e,n={};return eu.forEach(r=>{n[`${t}-justify-${r}`]={justifyContent:r}}),n},o0=()=>({}),i0=xt("Flex",e=>{const{paddingXS:t,padding:n,paddingLG:r}=e,o=tt(e,{flexGapSM:t,flexGap:n,flexGapLG:r});return[Qg(o),e0(o),t0(o),n0(o),r0(o)]},o0,{resetStyle:!1});var a0=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const en=P.forwardRef((e,t)=>{const{prefixCls:n,rootClassName:r,className:o,style:i,flex:a,gap:s,children:l,vertical:c=!1,component:u="div"}=e,f=a0(e,["prefixCls","rootClassName","className","style","flex","gap","children","vertical","component"]),{flex:m,direction:p,getPrefixCls:v}=P.useContext(Ve),h=v("flex",n),[C,g,b]=i0(h),w=c??(m==null?void 0:m.vertical),E=B(o,r,m==null?void 0:m.className,h,g,b,Jg(h,e),{[`${h}-rtl`]:p==="rtl",[`${h}-gap-${s}`]:Na(s),[`${h}-vertical`]:w}),y=Object.assign(Object.assign({},m==null?void 0:m.style),i);return a&&(y.flex=a),s&&!Na(s)&&(y.gap=s),C(P.createElement(u,Object.assign({ref:t,className:E,style:y},Pn(f,["justify","wrap","align"])),l))});function nu(){var e=document.documentElement.clientWidth,t=window.innerHeight||document.documentElement.clientHeight;return{width:e,height:t}}function s0(e){var t=e.getBoundingClientRect(),n=document.documentElement;return{left:t.left+(window.pageXOffset||n.scrollLeft)-(n.clientLeft||document.body.clientLeft||0),top:t.top+(window.pageYOffset||n.scrollTop)-(n.clientTop||document.body.clientTop||0)}}var sr=d.createContext(null),l0=function(t){var n=t.visible,r=t.maskTransitionName,o=t.getContainer,i=t.prefixCls,a=t.rootClassName,s=t.icons,l=t.countRender,c=t.showSwitch,u=t.showProgress,f=t.current,m=t.transform,p=t.count,v=t.scale,h=t.minScale,C=t.maxScale,g=t.closeIcon,b=t.onActive,w=t.onClose,E=t.onZoomIn,y=t.onZoomOut,x=t.onRotateRight,I=t.onRotateLeft,R=t.onFlipX,k=t.onFlipY,T=t.onReset,O=t.toolbarRender,N=t.zIndex,j=t.image,M=d.useContext(sr),L=s.rotateLeft,z=s.rotateRight,D=s.zoomIn,W=s.zoomOut,F=s.close,$=s.left,G=s.right,q=s.flipX,S=s.flipY,Y="".concat(i,"-operations-operation");d.useEffect(function(){var be=function(ye){ye.keyCode===it.ESC&&w()};return n&&window.addEventListener("keydown",be),function(){window.removeEventListener("keydown",be)}},[n]);var ne=function(Ie,ye){Ie.preventDefault(),Ie.stopPropagation(),b(ye)},K=d.useCallback(function(be){var Ie=be.type,ye=be.disabled,Oe=be.onClick,xe=be.icon;return d.createElement("div",{key:Ie,className:B(Y,"".concat(i,"-operations-operation-").concat(Ie),Ee({},"".concat(i,"-operations-operation-disabled"),!!ye)),onClick:Oe},xe)},[Y,i]),ue=c?K({icon:$,onClick:function(Ie){return ne(Ie,-1)},type:"prev",disabled:f===0}):void 0,ce=c?K({icon:G,onClick:function(Ie){return ne(Ie,1)},type:"next",disabled:f===p-1}):void 0,me=K({icon:S,onClick:k,type:"flipY"}),fe=K({icon:q,onClick:R,type:"flipX"}),se=K({icon:L,onClick:I,type:"rotateLeft"}),ee=K({icon:z,onClick:x,type:"rotateRight"}),de=K({icon:W,onClick:y,type:"zoomOut",disabled:v<=h}),oe=K({icon:D,onClick:E,type:"zoomIn",disabled:v===C}),ie=d.createElement("div",{className:"".concat(i,"-operations")},me,fe,se,ee,de,oe);return d.createElement(on,{visible:n,motionName:r},function(be){var Ie=be.className,ye=be.style;return d.createElement(Bl,{open:!0,getContainer:o??document.body},d.createElement("div",{className:B("".concat(i,"-operations-wrapper"),Ie,a),style:te(te({},ye),{},{zIndex:N})},g===null?null:d.createElement("button",{className:"".concat(i,"-close"),onClick:w},g||F),c&&d.createElement(d.Fragment,null,d.createElement("div",{className:B("".concat(i,"-switch-left"),Ee({},"".concat(i,"-switch-left-disabled"),f===0)),onClick:function(xe){return ne(xe,-1)}},$),d.createElement("div",{className:B("".concat(i,"-switch-right"),Ee({},"".concat(i,"-switch-right-disabled"),f===p-1)),onClick:function(xe){return ne(xe,1)}},G)),d.createElement("div",{className:"".concat(i,"-footer")},u&&d.createElement("div",{className:"".concat(i,"-progress")},l?l(f+1,p):d.createElement("bdi",null,"".concat(f+1," / ").concat(p))),O?O(ie,te(te({icons:{prevIcon:ue,nextIcon:ce,flipYIcon:me,flipXIcon:fe,rotateLeftIcon:se,rotateRightIcon:ee,zoomOutIcon:de,zoomInIcon:oe},actions:{onActive:b,onFlipY:k,onFlipX:R,onRotateLeft:I,onRotateRight:x,onZoomOut:y,onZoomIn:E,onReset:T,onClose:w},transform:m},M?{current:f,total:p}:{}),{},{image:j})):ie)))})},Cr={x:0,y:0,rotate:0,scale:1,flipX:!1,flipY:!1};function c0(e,t,n,r){var o=d.useRef(null),i=d.useRef([]),a=d.useState(Cr),s=ve(a,2),l=s[0],c=s[1],u=function(v){c(Cr),hf(Cr,l)||r==null||r({transform:Cr,action:v})},f=function(v,h){o.current===null&&(i.current=[],o.current=ni(function(){c(function(C){var g=C;return i.current.forEach(function(b){g=te(te({},g),b)}),o.current=null,r==null||r({transform:g,action:h}),g})})),i.current.push(te(te({},l),v))},m=function(v,h,C,g,b){var w=e.current,E=w.width,y=w.height,x=w.offsetWidth,I=w.offsetHeight,R=w.offsetLeft,k=w.offsetTop,T=v,O=l.scale*v;O>n?(O=n,T=n/l.scale):O<t&&(O=b?O:t,T=O/l.scale);var N=C??innerWidth/2,j=g??innerHeight/2,M=T-1,L=M*E*.5,z=M*y*.5,D=M*(N-l.x-R),W=M*(j-l.y-k),F=l.x-(D-L),$=l.y-(W-z);if(v<1&&O===1){var G=x*O,q=I*O,S=nu(),Y=S.width,ne=S.height;G<=Y&&q<=ne&&(F=0,$=0)}f({x:F,y:$,scale:O},h)};return{transform:l,resetTransform:u,updateTransform:f,dispatchZoomChange:m}}function ss(e,t,n,r){var o=t+n,i=(n-r)/2;if(n>r){if(t>0)return Ee({},e,i);if(t<0&&o<r)return Ee({},e,-i)}else if(t<0||o>r)return Ee({},e,t<0?i:-i);return{}}function ru(e,t,n,r){var o=nu(),i=o.width,a=o.height,s=null;return e<=i&&t<=a?s={x:0,y:0}:(e>i||t>a)&&(s=te(te({},ss("x",n,e,i)),ss("y",r,t,a))),s}var yn=1,u0=1;function d0(e,t,n,r,o,i,a){var s=o.rotate,l=o.scale,c=o.x,u=o.y,f=d.useState(!1),m=ve(f,2),p=m[0],v=m[1],h=d.useRef({diffX:0,diffY:0,transformX:0,transformY:0}),C=function(y){!t||y.button!==0||(y.preventDefault(),y.stopPropagation(),h.current={diffX:y.pageX-c,diffY:y.pageY-u,transformX:c,transformY:u},v(!0))},g=function(y){n&&p&&i({x:y.pageX-h.current.diffX,y:y.pageY-h.current.diffY},"move")},b=function(){if(n&&p){v(!1);var y=h.current,x=y.transformX,I=y.transformY,R=c!==x&&u!==I;if(!R)return;var k=e.current.offsetWidth*l,T=e.current.offsetHeight*l,O=e.current.getBoundingClientRect(),N=O.left,j=O.top,M=s%180!==0,L=ru(M?T:k,M?k:T,N,j);L&&i(te({},L),"dragRebound")}},w=function(y){if(!(!n||y.deltaY==0)){var x=Math.abs(y.deltaY/100),I=Math.min(x,u0),R=yn+I*r;y.deltaY>0&&(R=yn/R),a(R,"wheel",y.clientX,y.clientY)}};return d.useEffect(function(){var E,y,x,I;if(t){x=vn(window,"mouseup",b,!1),I=vn(window,"mousemove",g,!1);try{window.top!==window.self&&(E=vn(window.top,"mouseup",b,!1),y=vn(window.top,"mousemove",g,!1))}catch{}}return function(){var R,k,T,O;(R=x)===null||R===void 0||R.remove(),(k=I)===null||k===void 0||k.remove(),(T=E)===null||T===void 0||T.remove(),(O=y)===null||O===void 0||O.remove()}},[n,p,c,u,s,t]),{isMoving:p,onMouseDown:C,onMouseMove:g,onMouseUp:b,onWheel:w}}function f0(e){return new Promise(function(t){if(!e){t(!1);return}var n=document.createElement("img");n.onerror=function(){return t(!1)},n.onload=function(){return t(!0)},n.src=e})}function ou(e){var t=e.src,n=e.isCustomPlaceholder,r=e.fallback,o=d.useState(n?"loading":"normal"),i=ve(o,2),a=i[0],s=i[1],l=d.useRef(!1),c=a==="error";d.useEffect(function(){var p=!0;return f0(t).then(function(v){!v&&p&&s("error")}),function(){p=!1}},[t]),d.useEffect(function(){n&&!l.current?s("loading"):c&&s("normal")},[t]);var u=function(){s("normal")},f=function(v){l.current=!1,a==="loading"&&v!==null&&v!==void 0&&v.complete&&(v.naturalWidth||v.naturalHeight)&&(l.current=!0,u())},m=c&&r?{src:r}:{onLoad:u,src:t};return[f,m,a]}function Xr(e,t){var n=e.x-t.x,r=e.y-t.y;return Math.hypot(n,r)}function p0(e,t,n,r){var o=Xr(e,n),i=Xr(t,r);if(o===0&&i===0)return[e.x,e.y];var a=o/(o+i),s=e.x+a*(t.x-e.x),l=e.y+a*(t.y-e.y);return[s,l]}function m0(e,t,n,r,o,i,a){var s=o.rotate,l=o.scale,c=o.x,u=o.y,f=d.useState(!1),m=ve(f,2),p=m[0],v=m[1],h=d.useRef({point1:{x:0,y:0},point2:{x:0,y:0},eventType:"none"}),C=function(y){h.current=te(te({},h.current),y)},g=function(y){if(t){y.stopPropagation(),v(!0);var x=y.touches,I=x===void 0?[]:x;I.length>1?C({point1:{x:I[0].clientX,y:I[0].clientY},point2:{x:I[1].clientX,y:I[1].clientY},eventType:"touchZoom"}):C({point1:{x:I[0].clientX-c,y:I[0].clientY-u},eventType:"move"})}},b=function(y){var x=y.touches,I=x===void 0?[]:x,R=h.current,k=R.point1,T=R.point2,O=R.eventType;if(I.length>1&&O==="touchZoom"){var N={x:I[0].clientX,y:I[0].clientY},j={x:I[1].clientX,y:I[1].clientY},M=p0(k,T,N,j),L=ve(M,2),z=L[0],D=L[1],W=Xr(N,j)/Xr(k,T);a(W,"touchZoom",z,D,!0),C({point1:N,point2:j,eventType:"touchZoom"})}else O==="move"&&(i({x:I[0].clientX-k.x,y:I[0].clientY-k.y},"move"),C({eventType:"move"}))},w=function(){if(n){if(p&&v(!1),C({eventType:"none"}),r>l)return i({x:0,y:0,scale:r},"touchZoom");var y=e.current.offsetWidth*l,x=e.current.offsetHeight*l,I=e.current.getBoundingClientRect(),R=I.left,k=I.top,T=s%180!==0,O=ru(T?x:y,T?y:x,R,k);O&&i(te({},O),"dragRebound")}};return d.useEffect(function(){var E;return n&&t&&(E=vn(window,"touchmove",function(y){return y.preventDefault()},{passive:!1})),function(){var y;(y=E)===null||y===void 0||y.remove()}},[n,t]),{isTouching:p,onTouchStart:g,onTouchMove:b,onTouchEnd:w}}var h0=["fallback","src","imgRef"],g0=["prefixCls","src","alt","imageInfo","fallback","movable","onClose","visible","icons","rootClassName","closeIcon","getContainer","current","count","countRender","scaleStep","minScale","maxScale","transitionName","maskTransitionName","imageRender","imgCommonProps","toolbarRender","onTransform","onChange"],v0=function(t){var n=t.fallback,r=t.src,o=t.imgRef,i=st(t,h0),a=ou({src:r,fallback:n}),s=ve(a,2),l=s[0],c=s[1];return P.createElement("img",ae({ref:function(f){o.current=f,l(f)}},i,c))},iu=function(t){var n=t.prefixCls,r=t.src,o=t.alt,i=t.imageInfo,a=t.fallback,s=t.movable,l=s===void 0?!0:s,c=t.onClose,u=t.visible,f=t.icons,m=f===void 0?{}:f,p=t.rootClassName,v=t.closeIcon,h=t.getContainer,C=t.current,g=C===void 0?0:C,b=t.count,w=b===void 0?1:b,E=t.countRender,y=t.scaleStep,x=y===void 0?.5:y,I=t.minScale,R=I===void 0?1:I,k=t.maxScale,T=k===void 0?50:k,O=t.transitionName,N=O===void 0?"zoom":O,j=t.maskTransitionName,M=j===void 0?"fade":j,L=t.imageRender,z=t.imgCommonProps,D=t.toolbarRender,W=t.onTransform,F=t.onChange,$=st(t,g0),G=d.useRef(),q=d.useContext(sr),S=q&&w>1,Y=q&&w>=1,ne=d.useState(!0),K=ve(ne,2),ue=K[0],ce=K[1],me=c0(G,R,T,W),fe=me.transform,se=me.resetTransform,ee=me.updateTransform,de=me.dispatchZoomChange,oe=d0(G,l,u,x,fe,ee,de),ie=oe.isMoving,be=oe.onMouseDown,Ie=oe.onWheel,ye=m0(G,l,u,R,fe,ee,de),Oe=ye.isTouching,xe=ye.onTouchStart,$e=ye.onTouchMove,Ae=ye.onTouchEnd,Re=fe.rotate,Ue=fe.scale,Ke=B(Ee({},"".concat(n,"-moving"),ie));d.useEffect(function(){ue||ce(!0)},[ue]);var _=function(){se("close")},U=function(){de(yn+x,"zoomIn")},le=function(){de(yn/(yn+x),"zoomOut")},he=function(){ee({rotate:Re+90},"rotateRight")},Ce=function(){ee({rotate:Re-90},"rotateLeft")},ze=function(){ee({flipX:!fe.flipX},"flipX")},V=function(){ee({flipY:!fe.flipY},"flipY")},re=function(){se("reset")},Z=function(We){var nt=g+We;!Number.isInteger(nt)||nt<0||nt>w-1||(ce(!1),se(We<0?"prev":"next"),F==null||F(nt,g))},pe=function(We){!u||!S||(We.keyCode===it.LEFT?Z(-1):We.keyCode===it.RIGHT&&Z(1))},X=function(We){u&&(Ue!==1?ee({x:0,y:0,scale:1},"doubleClick"):de(yn+x,"doubleClick",We.clientX,We.clientY))};d.useEffect(function(){var Te=vn(window,"keydown",pe,!1);return function(){Te.remove()}},[u,S,g]);var J=P.createElement(v0,ae({},z,{width:t.width,height:t.height,imgRef:G,className:"".concat(n,"-img"),alt:o,style:{transform:"translate3d(".concat(fe.x,"px, ").concat(fe.y,"px, 0) scale3d(").concat(fe.flipX?"-":"").concat(Ue,", ").concat(fe.flipY?"-":"").concat(Ue,", 1) rotate(").concat(Re,"deg)"),transitionDuration:(!ue||Oe)&&"0s"},fallback:a,src:r,onWheel:Ie,onMouseDown:be,onDoubleClick:X,onTouchStart:xe,onTouchMove:$e,onTouchEnd:Ae,onTouchCancel:Ae})),ge=te({url:r,alt:o},i);return P.createElement(P.Fragment,null,P.createElement(Fi,ae({transitionName:N,maskTransitionName:M,closable:!1,keyboard:!0,prefixCls:n,onClose:c,visible:u,classNames:{wrapper:Ke},rootClassName:p,getContainer:h},$,{afterClose:_}),P.createElement("div",{className:"".concat(n,"-img-wrapper")},L?L(J,te({transform:fe,image:ge},q?{current:g}:{})):J)),P.createElement(l0,{visible:u,transform:fe,maskTransitionName:M,closeIcon:v,getContainer:h,prefixCls:n,rootClassName:p,icons:m,countRender:E,showSwitch:S,showProgress:Y,current:g,count:w,scale:Ue,minScale:R,maxScale:T,toolbarRender:D,onActive:Z,onZoomIn:U,onZoomOut:le,onRotateRight:he,onRotateLeft:Ce,onFlipX:ze,onFlipY:V,onClose:c,onReset:re,zIndex:$.zIndex!==void 0?$.zIndex+1:void 0,image:ge}))},li=["crossOrigin","decoding","draggable","loading","referrerPolicy","sizes","srcSet","useMap","alt"];function y0(e){var t=d.useState({}),n=ve(t,2),r=n[0],o=n[1],i=d.useCallback(function(s,l){return o(function(c){return te(te({},c),{},Ee({},s,l))}),function(){o(function(c){var u=te({},c);return delete u[s],u})}},[]),a=d.useMemo(function(){return e?e.map(function(s){if(typeof s=="string")return{data:{src:s}};var l={};return Object.keys(s).forEach(function(c){["src"].concat(He(li)).includes(c)&&(l[c]=s[c])}),{data:l}}):Object.keys(r).reduce(function(s,l){var c=r[l],u=c.canPreview,f=c.data;return u&&s.push({data:f,id:l}),s},[])},[e,r]);return[a,i,!!e]}var b0=["visible","onVisibleChange","getContainer","current","movable","minScale","maxScale","countRender","closeIcon","onChange","onTransform","toolbarRender","imageRender"],x0=["src"],C0=function(t){var n,r=t.previewPrefixCls,o=r===void 0?"rc-image-preview":r,i=t.children,a=t.icons,s=a===void 0?{}:a,l=t.items,c=t.preview,u=t.fallback,f=et(c)==="object"?c:{},m=f.visible,p=f.onVisibleChange,v=f.getContainer,h=f.current,C=f.movable,g=f.minScale,b=f.maxScale,w=f.countRender,E=f.closeIcon,y=f.onChange,x=f.onTransform,I=f.toolbarRender,R=f.imageRender,k=st(f,b0),T=y0(l),O=ve(T,3),N=O[0],j=O[1],M=O[2],L=bt(0,{value:h}),z=ve(L,2),D=z[0],W=z[1],F=d.useState(!1),$=ve(F,2),G=$[0],q=$[1],S=((n=N[D])===null||n===void 0?void 0:n.data)||{},Y=S.src,ne=st(S,x0),K=bt(!!m,{value:m,onChange:function(Oe,xe){p==null||p(Oe,xe,D)}}),ue=ve(K,2),ce=ue[0],me=ue[1],fe=d.useState(null),se=ve(fe,2),ee=se[0],de=se[1],oe=d.useCallback(function(ye,Oe,xe,$e){var Ae=M?N.findIndex(function(Re){return Re.data.src===Oe}):N.findIndex(function(Re){return Re.id===ye});W(Ae<0?0:Ae),me(!0),de({x:xe,y:$e}),q(!0)},[N,M]);d.useEffect(function(){ce?G||W(0):q(!1)},[ce]);var ie=function(Oe,xe){W(Oe),y==null||y(Oe,xe)},be=function(){me(!1),de(null)},Ie=d.useMemo(function(){return{register:j,onPreview:oe}},[j,oe]);return d.createElement(sr.Provider,{value:Ie},i,d.createElement(iu,ae({"aria-hidden":!ce,movable:C,visible:ce,prefixCls:o,closeIcon:E,onClose:be,mousePosition:ee,imgCommonProps:ne,src:Y,fallback:u,icons:s,minScale:g,maxScale:b,getContainer:v,current:D,count:N.length,countRender:w,onTransform:x,toolbarRender:I,imageRender:R,onChange:ie},k)))},ls=0;function S0(e,t){var n=d.useState(function(){return ls+=1,String(ls)}),r=ve(n,1),o=r[0],i=d.useContext(sr),a={data:t,canPreview:e};return d.useEffect(function(){if(i)return i.register(o,a)},[]),d.useEffect(function(){i&&i.register(o,a)},[e,t]),o}var w0=["src","alt","onPreviewClose","prefixCls","previewPrefixCls","placeholder","fallback","width","height","style","preview","className","onClick","onError","wrapperClassName","wrapperStyle","rootClassName"],$0=["src","visible","onVisibleChange","getContainer","mask","maskClassName","movable","icons","scaleStep","minScale","maxScale","imageRender","toolbarRender"],Ui=function(t){var n=t.src,r=t.alt,o=t.onPreviewClose,i=t.prefixCls,a=i===void 0?"rc-image":i,s=t.previewPrefixCls,l=s===void 0?"".concat(a,"-preview"):s,c=t.placeholder,u=t.fallback,f=t.width,m=t.height,p=t.style,v=t.preview,h=v===void 0?!0:v,C=t.className,g=t.onClick,b=t.onError,w=t.wrapperClassName,E=t.wrapperStyle,y=t.rootClassName,x=st(t,w0),I=c&&c!==!0,R=et(h)==="object"?h:{},k=R.src,T=R.visible,O=T===void 0?void 0:T,N=R.onVisibleChange,j=N===void 0?o:N,M=R.getContainer,L=M===void 0?void 0:M,z=R.mask,D=R.maskClassName,W=R.movable,F=R.icons,$=R.scaleStep,G=R.minScale,q=R.maxScale,S=R.imageRender,Y=R.toolbarRender,ne=st(R,$0),K=k??n,ue=bt(!!O,{value:O,onChange:j}),ce=ve(ue,2),me=ce[0],fe=ce[1],se=ou({src:n,isCustomPlaceholder:I,fallback:u}),ee=ve(se,3),de=ee[0],oe=ee[1],ie=ee[2],be=d.useState(null),Ie=ve(be,2),ye=Ie[0],Oe=Ie[1],xe=d.useContext(sr),$e=!!h,Ae=function(){fe(!1),Oe(null)},Re=B(a,w,y,Ee({},"".concat(a,"-error"),ie==="error")),Ue=d.useMemo(function(){var le={};return li.forEach(function(he){t[he]!==void 0&&(le[he]=t[he])}),le},li.map(function(le){return t[le]})),Ke=d.useMemo(function(){return te(te({},Ue),{},{src:K})},[K,Ue]),_=S0($e,Ke),U=function(he){var Ce=s0(he.target),ze=Ce.left,V=Ce.top;xe?xe.onPreview(_,K,ze,V):(Oe({x:ze,y:V}),fe(!0)),g==null||g(he)};return d.createElement(d.Fragment,null,d.createElement("div",ae({},x,{className:Re,onClick:$e?U:g,style:te({width:f,height:m},E)}),d.createElement("img",ae({},Ue,{className:B("".concat(a,"-img"),Ee({},"".concat(a,"-img-placeholder"),c===!0),C),style:te({height:m},p),ref:de},oe,{width:f,height:m,onError:b})),ie==="loading"&&d.createElement("div",{"aria-hidden":"true",className:"".concat(a,"-placeholder")},c),z&&$e&&d.createElement("div",{className:B("".concat(a,"-mask"),D),style:{display:(p==null?void 0:p.display)==="none"?"none":void 0}},z)),!xe&&$e&&d.createElement(iu,ae({"aria-hidden":!me,visible:me,prefixCls:l,onClose:Ae,mousePosition:ye,src:K,alt:r,imageInfo:{width:f,height:m},fallback:u,getContainer:L,icons:F,movable:W,scaleStep:$,minScale:G,maxScale:q,rootClassName:y,imageRender:S,imgCommonProps:Ue,toolbarRender:Y},ne)))};Ui.PreviewGroup=C0;const ci=e=>({position:e||"absolute",inset:0}),E0=e=>{const{iconCls:t,motionDurationSlow:n,paddingXXS:r,marginXXS:o,prefixCls:i,colorTextLightSolid:a}=e;return{position:"absolute",inset:0,display:"flex",alignItems:"center",justifyContent:"center",color:a,background:new Rt("#000").setA(.5).toRgbString(),cursor:"pointer",opacity:0,transition:`opacity ${n}`,[`.${i}-mask-info`]:Object.assign(Object.assign({},In),{padding:`0 ${H(r)}`,[t]:{marginInlineEnd:o,svg:{verticalAlign:"baseline"}}})}},I0=e=>{const{previewCls:t,modalMaskBg:n,paddingSM:r,marginXL:o,margin:i,paddingLG:a,previewOperationColorDisabled:s,previewOperationHoverColor:l,motionDurationSlow:c,iconCls:u,colorTextLightSolid:f}=e,m=new Rt(n).setA(.1),p=m.clone().setA(.2);return{[`${t}-footer`]:{position:"fixed",bottom:o,left:{_skip_check_:!0,value:"50%"},display:"flex",flexDirection:"column",alignItems:"center",color:e.previewOperationColor,transform:"translateX(-50%)"},[`${t}-progress`]:{marginBottom:i},[`${t}-close`]:{position:"fixed",top:o,right:{_skip_check_:!0,value:o},display:"flex",color:f,backgroundColor:m.toRgbString(),borderRadius:"50%",padding:r,outline:0,border:0,cursor:"pointer",transition:`all ${c}`,"&:hover":{backgroundColor:p.toRgbString()},[`& > ${u}`]:{fontSize:e.previewOperationSize}},[`${t}-operations`]:{display:"flex",alignItems:"center",padding:`0 ${H(a)}`,backgroundColor:m.toRgbString(),borderRadius:100,"&-operation":{marginInlineStart:r,padding:r,cursor:"pointer",transition:`all ${c}`,userSelect:"none",[`&:not(${t}-operations-operation-disabled):hover > ${u}`]:{color:l},"&-disabled":{color:s,cursor:"not-allowed"},"&:first-of-type":{marginInlineStart:0},[`& > ${u}`]:{fontSize:e.previewOperationSize}}}}},P0=e=>{const{modalMaskBg:t,iconCls:n,previewOperationColorDisabled:r,previewCls:o,zIndexPopup:i,motionDurationSlow:a}=e,s=new Rt(t).setA(.1),l=s.clone().setA(.2);return{[`${o}-switch-left, ${o}-switch-right`]:{position:"fixed",insetBlockStart:"50%",zIndex:e.calc(i).add(1).equal(),display:"flex",alignItems:"center",justifyContent:"center",width:e.imagePreviewSwitchSize,height:e.imagePreviewSwitchSize,marginTop:e.calc(e.imagePreviewSwitchSize).mul(-1).div(2).equal(),color:e.previewOperationColor,background:s.toRgbString(),borderRadius:"50%",transform:"translateY(-50%)",cursor:"pointer",transition:`all ${a}`,userSelect:"none","&:hover":{background:l.toRgbString()},"&-disabled":{"&, &:hover":{color:r,background:"transparent",cursor:"not-allowed",[`> ${n}`]:{cursor:"not-allowed"}}},[`> ${n}`]:{fontSize:e.previewOperationSize}},[`${o}-switch-left`]:{insetInlineStart:e.marginSM},[`${o}-switch-right`]:{insetInlineEnd:e.marginSM}}},T0=e=>{const{motionEaseOut:t,previewCls:n,motionDurationSlow:r,componentCls:o}=e;return[{[`${o}-preview-root`]:{[n]:{height:"100%",textAlign:"center",pointerEvents:"none"},[`${n}-body`]:Object.assign(Object.assign({},ci()),{overflow:"hidden"}),[`${n}-img`]:{maxWidth:"100%",maxHeight:"70%",verticalAlign:"middle",transform:"scale3d(1, 1, 1)",cursor:"grab",transition:`transform ${r} ${t} 0s`,userSelect:"none","&-wrapper":Object.assign(Object.assign({},ci()),{transition:`transform ${r} ${t} 0s`,display:"flex",justifyContent:"center",alignItems:"center","& > *":{pointerEvents:"auto"},"&::before":{display:"inline-block",width:1,height:"50%",marginInlineEnd:-1,content:'""'}})},[`${n}-moving`]:{[`${n}-preview-img`]:{cursor:"grabbing","&-wrapper":{transitionDuration:"0s"}}}}},{[`${o}-preview-root`]:{[`${n}-wrap`]:{zIndex:e.zIndexPopup}}},{[`${o}-preview-operations-wrapper`]:{position:"fixed",zIndex:e.calc(e.zIndexPopup).add(1).equal()},"&":[I0(e),P0(e)]}]},O0=e=>{const{componentCls:t}=e;return{[t]:{position:"relative",display:"inline-block",[`${t}-img`]:{width:"100%",height:"auto",verticalAlign:"middle"},[`${t}-img-placeholder`]:{backgroundColor:e.colorBgContainerDisabled,backgroundImage:"url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMTQuNSAyLjVoLTEzQS41LjUgMCAwIDAgMSAzdjEwYS41LjUgMCAwIDAgLjUuNWgxM2EuNS41IDAgMCAwIC41LS41VjNhLjUuNSAwIDAgMC0uNS0uNXpNNS4yODEgNC43NWExIDEgMCAwIDEgMCAyIDEgMSAwIDAgMSAwLTJ6bTguMDMgNi44M2EuMTI3LjEyNyAwIDAgMS0uMDgxLjAzSDIuNzY5YS4xMjUuMTI1IDAgMCAxLS4wOTYtLjIwN2wyLjY2MS0zLjE1NmEuMTI2LjEyNiAwIDAgMSAuMTc3LS4wMTZsLjAxNi4wMTZMNy4wOCAxMC4wOWwyLjQ3LTIuOTNhLjEyNi4xMjYgMCAwIDEgLjE3Ny0uMDE2bC4wMTUuMDE2IDMuNTg4IDQuMjQ0YS4xMjcuMTI3IDAgMCAxLS4wMi4xNzV6IiBmaWxsPSIjOEM4QzhDIiBmaWxsLXJ1bGU9Im5vbnplcm8iLz48L3N2Zz4=')",backgroundRepeat:"no-repeat",backgroundPosition:"center center",backgroundSize:"30%"},[`${t}-mask`]:Object.assign({},E0(e)),[`${t}-mask:hover`]:{opacity:1},[`${t}-placeholder`]:Object.assign({},ci())}}},R0=e=>{const{previewCls:t}=e;return{[`${t}-root`]:Oi(e,"zoom"),"&":Bi(e,!0)}},N0=e=>({zIndexPopup:e.zIndexPopupBase+80,previewOperationColor:new Rt(e.colorTextLightSolid).setA(.65).toRgbString(),previewOperationHoverColor:new Rt(e.colorTextLightSolid).setA(.85).toRgbString(),previewOperationColorDisabled:new Rt(e.colorTextLightSolid).setA(.25).toRgbString(),previewOperationSize:e.fontSizeIcon*1.5}),au=xt("Image",e=>{const t=`${e.componentCls}-preview`,n=tt(e,{previewCls:t,modalMaskBg:new Rt("#000").setA(.45).toRgbString(),imagePreviewSwitchSize:e.controlHeightLG});return[O0(n),T0(n),Sc(tt(n,{componentCls:t})),R0(n)]},N0);var k0=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const su={rotateLeft:d.createElement(pm,null),rotateRight:d.createElement(gm,null),zoomIn:d.createElement(Im,null),zoomOut:d.createElement(Om,null),close:d.createElement(an,null),left:d.createElement(Hr,null),right:d.createElement(Jn,null),flipX:d.createElement(Aa,null),flipY:d.createElement(Aa,{rotate:90})},M0=e=>{var{previewPrefixCls:t,preview:n}=e,r=k0(e,["previewPrefixCls","preview"]);const{getPrefixCls:o,direction:i}=d.useContext(Ve),a=o("image",t),s=`${a}-preview`,l=o(),c=Wt(a),[u,f,m]=au(a,c),[p]=Ri("ImagePreview",typeof n=="object"?n.zIndex:void 0),v=d.useMemo(()=>Object.assign(Object.assign({},su),{left:i==="rtl"?d.createElement(Jn,null):d.createElement(Hr,null),right:i==="rtl"?d.createElement(Hr,null):d.createElement(Jn,null)}),[i]),h=d.useMemo(()=>{var C;if(n===!1)return n;const g=typeof n=="object"?n:{},b=B(f,m,c,(C=g.rootClassName)!==null&&C!==void 0?C:"");return Object.assign(Object.assign({},g),{transitionName:At(l,"zoom",g.transitionName),maskTransitionName:At(l,"fade",g.maskTransitionName),rootClassName:b,zIndex:p})},[n]);return u(d.createElement(Ui.PreviewGroup,Object.assign({preview:h,previewPrefixCls:s,icons:v},r)))};var cs=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const Xi=e=>{const{prefixCls:t,preview:n,className:r,rootClassName:o,style:i}=e,a=cs(e,["prefixCls","preview","className","rootClassName","style"]),{getPrefixCls:s,getPopupContainer:l,className:c,style:u,preview:f}=Li("image"),[m]=$n("Image"),p=s("image",t),v=s(),h=Wt(p),[C,g,b]=au(p,h),w=B(o,g,b,h),E=B(r,g,c),[y]=Ri("ImagePreview",typeof n=="object"?n.zIndex:void 0),x=d.useMemo(()=>{if(n===!1)return n;const R=typeof n=="object"?n:{},{getContainer:k,closeIcon:T,rootClassName:O,destroyOnClose:N,destroyOnHidden:j}=R,M=cs(R,["getContainer","closeIcon","rootClassName","destroyOnClose","destroyOnHidden"]);return Object.assign(Object.assign({mask:d.createElement("div",{className:`${p}-mask-info`},d.createElement(ec,null),m==null?void 0:m.preview),icons:su},M),{destroyOnClose:j??N,rootClassName:B(w,O),getContainer:k??l,transitionName:At(v,"zoom",R.transitionName),maskTransitionName:At(v,"fade",R.maskTransitionName),zIndex:y,closeIcon:T??(f==null?void 0:f.closeIcon)})},[n,m,f==null?void 0:f.closeIcon]),I=Object.assign(Object.assign({},u),i);return C(d.createElement(Ui,Object.assign({prefixCls:p,preview:x,rootClassName:w,className:E,style:I},a)))};Xi.PreviewGroup=M0;const j0=(e,t=!1)=>t&&e==null?[]:Array.isArray(e)?e:[e];var L0=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const _0=e=>{const{prefixCls:t,className:n,closeIcon:r,closable:o,type:i,title:a,children:s,footer:l}=e,c=L0(e,["prefixCls","className","closeIcon","closable","type","title","children","footer"]),{getPrefixCls:u}=d.useContext(Ve),f=u(),m=t||u("modal"),p=Wt(f),[v,h,C]=Ec(m,p),g=`${m}-confirm`;let b={};return i?b={closable:o??!1,title:"",footer:"",children:d.createElement(Pc,Object.assign({},e,{prefixCls:m,confirmPrefixCls:g,rootPrefixCls:f,content:s}))}:b={closable:o??!0,title:a,footer:l!==null&&d.createElement(xc,Object.assign({},e)),children:s},v(d.createElement(vc,Object.assign({prefixCls:m,className:B(h,`${m}-pure-panel`,i&&g,i&&`${g}-${i}`,n,C,p)},c,{closeIcon:bc(m,r),closable:o},b)))},A0=gf(_0);function lu(e){return ar(Nc(e))}const Nt=Ic;Nt.useModal=uh;Nt.info=function(t){return ar(kc(t))};Nt.success=function(t){return ar(Mc(t))};Nt.error=function(t){return ar(jc(t))};Nt.warning=lu;Nt.warn=lu;Nt.confirm=function(t){return ar(Lc(t))};Nt.destroyAll=function(){for(;Jt.length;){const t=Jt.pop();t&&t()}};Nt.config=ih;Nt._InternalPanelDoNotUseOrYouWillBeFired=A0;let Ot=null,Lr=e=>e(),Gr=[],Qn={};function us(){const{getContainer:e,rtl:t,maxCount:n,top:r,bottom:o,showProgress:i,pauseOnHover:a}=Qn,s=(e==null?void 0:e())||document.body;return{getContainer:()=>s,rtl:t,maxCount:n,top:r,bottom:o,showProgress:i,pauseOnHover:a}}const z0=P.forwardRef((e,t)=>{const{notificationConfig:n,sync:r}=e,{getPrefixCls:o}=d.useContext(Ve),i=Qn.prefixCls||o("notification"),a=d.useContext(vf),[s,l]=Fc(Object.assign(Object.assign(Object.assign({},n),{prefixCls:i}),a.notification));return P.useEffect(r,[]),P.useImperativeHandle(t,()=>{const c=Object.assign({},s);return Object.keys(c).forEach(u=>{c[u]=(...f)=>(r(),s[u].apply(s,f))}),{instance:c,sync:r}}),l}),D0=P.forwardRef((e,t)=>{const[n,r]=P.useState(us),o=()=>{r(us)};P.useEffect(o,[]);const i=ql(),a=i.getRootPrefixCls(),s=i.getIconPrefixCls(),l=i.getTheme(),c=P.createElement(z0,{ref:t,sync:o,notificationConfig:n});return P.createElement(En,{prefixCls:a,iconPrefixCls:s,theme:l},i.holderRender?i.holderRender(c):c)});function Gi(){if(!Ot){const e=document.createDocumentFragment(),t={fragment:e};Ot=t,Lr(()=>{Yl()(P.createElement(D0,{ref:r=>{const{instance:o,sync:i}=r||{};Promise.resolve().then(()=>{!t.instance&&o&&(t.instance=o,t.sync=i,Gi())})}}),e)});return}Ot.instance&&(Gr.forEach(e=>{switch(e.type){case"open":{Lr(()=>{Ot.instance.open(Object.assign(Object.assign({},Qn),e.config))});break}case"destroy":Lr(()=>{Ot==null||Ot.instance.destroy(e.key)});break}}),Gr=[])}function B0(e){Qn=Object.assign(Object.assign({},Qn),e),Lr(()=>{var t;(t=Ot==null?void 0:Ot.sync)===null||t===void 0||t.call(Ot)})}function cu(e){Gr.push({type:"open",config:e}),Gi()}const F0=e=>{Gr.push({type:"destroy",key:e}),Gi()},H0=["success","info","warning","error"],W0={open:cu,destroy:F0,config:B0,useNotification:kh,_InternalPanelDoNotUseOrYouWillBeFired:Sh},uu=W0;H0.forEach(e=>{uu[e]=t=>cu(Object.assign(Object.assign({},t),{type:e}))});var V0={percent:0,prefixCls:"rc-progress",strokeColor:"#2db7f5",strokeLinecap:"round",strokeWidth:1,trailColor:"#D9D9D9",trailWidth:1,gapPosition:"bottom"},U0=function(){var t=d.useRef([]),n=d.useRef(null);return d.useEffect(function(){var r=Date.now(),o=!1;t.current.forEach(function(i){if(i){o=!0;var a=i.style;a.transitionDuration=".3s, .3s, .3s, .06s",n.current&&r-n.current<100&&(a.transitionDuration="0s, 0s")}}),o&&(n.current=Date.now())}),t.current},ds=0,X0=Fl();function G0(){var e;return X0?(e=ds,ds+=1):e="TEST_OR_SSR",e}const q0=function(e){var t=d.useState(),n=ve(t,2),r=n[0],o=n[1];return d.useEffect(function(){o("rc_progress_".concat(G0()))},[]),e||r};var fs=function(t){var n=t.bg,r=t.children;return d.createElement("div",{style:{width:"100%",height:"100%",background:n}},r)};function ps(e,t){return Object.keys(e).map(function(n){var r=parseFloat(n),o="".concat(Math.floor(r*t),"%");return"".concat(e[n]," ").concat(o)})}var Y0=d.forwardRef(function(e,t){var n=e.prefixCls,r=e.color,o=e.gradientId,i=e.radius,a=e.style,s=e.ptg,l=e.strokeLinecap,c=e.strokeWidth,u=e.size,f=e.gapDegree,m=r&&et(r)==="object",p=m?"#FFF":void 0,v=u/2,h=d.createElement("circle",{className:"".concat(n,"-circle-path"),r:i,cx:v,cy:v,stroke:p,strokeLinecap:l,strokeWidth:c,opacity:s===0?0:1,style:a,ref:t});if(!m)return h;var C="".concat(o,"-conic"),g=f?"".concat(180+f/2,"deg"):"0deg",b=ps(r,(360-f)/360),w=ps(r,1),E="conic-gradient(from ".concat(g,", ").concat(b.join(", "),")"),y="linear-gradient(to ".concat(f?"bottom":"top",", ").concat(w.join(", "),")");return d.createElement(d.Fragment,null,d.createElement("mask",{id:C},h),d.createElement("foreignObject",{x:0,y:0,width:u,height:u,mask:"url(#".concat(C,")")},d.createElement(fs,{bg:y},d.createElement(fs,{bg:E}))))}),Wn=100,Co=function(t,n,r,o,i,a,s,l,c,u){var f=arguments.length>10&&arguments[10]!==void 0?arguments[10]:0,m=r/100*360*((360-a)/360),p=a===0?0:{bottom:0,top:180,left:90,right:-90}[s],v=(100-o)/100*n;c==="round"&&o!==100&&(v+=u/2,v>=n&&(v=n-.01));var h=Wn/2;return{stroke:typeof l=="string"?l:void 0,strokeDasharray:"".concat(n,"px ").concat(t),strokeDashoffset:v+f,transform:"rotate(".concat(i+m+p,"deg)"),transformOrigin:"".concat(h,"px ").concat(h,"px"),transition:"stroke-dashoffset .3s ease 0s, stroke-dasharray .3s ease 0s, stroke .3s, stroke-width .06s ease .3s, opacity .3s ease 0s",fillOpacity:0}},K0=["id","prefixCls","steps","strokeWidth","trailWidth","gapDegree","gapPosition","trailColor","strokeLinecap","style","className","strokeColor","percent"];function ms(e){var t=e??[];return Array.isArray(t)?t:[t]}var Z0=function(t){var n=te(te({},V0),t),r=n.id,o=n.prefixCls,i=n.steps,a=n.strokeWidth,s=n.trailWidth,l=n.gapDegree,c=l===void 0?0:l,u=n.gapPosition,f=n.trailColor,m=n.strokeLinecap,p=n.style,v=n.className,h=n.strokeColor,C=n.percent,g=st(n,K0),b=Wn/2,w=q0(r),E="".concat(w,"-gradient"),y=b-a/2,x=Math.PI*2*y,I=c>0?90+c/2:-90,R=x*((360-c)/360),k=et(i)==="object"?i:{count:i,gap:2},T=k.count,O=k.gap,N=ms(C),j=ms(h),M=j.find(function(G){return G&&et(G)==="object"}),L=M&&et(M)==="object",z=L?"butt":m,D=Co(x,R,0,100,I,c,u,f,z,a),W=U0(),F=function(){var q=0;return N.map(function(S,Y){var ne=j[Y]||j[j.length-1],K=Co(x,R,q,S,I,c,u,ne,z,a);return q+=S,d.createElement(Y0,{key:Y,color:ne,ptg:S,radius:y,prefixCls:o,gradientId:E,style:K,strokeLinecap:z,strokeWidth:a,gapDegree:c,ref:function(ce){W[Y]=ce},size:Wn})}).reverse()},$=function(){var q=Math.round(T*(N[0]/100)),S=100/T,Y=0;return new Array(T).fill(null).map(function(ne,K){var ue=K<=q-1?j[0]:f,ce=ue&&et(ue)==="object"?"url(#".concat(E,")"):void 0,me=Co(x,R,Y,S,I,c,u,ue,"butt",a,O);return Y+=(R-me.strokeDashoffset+O)*100/R,d.createElement("circle",{key:K,className:"".concat(o,"-circle-path"),r:y,cx:b,cy:b,stroke:ce,strokeWidth:a,opacity:1,style:me,ref:function(se){W[K]=se}})})};return d.createElement("svg",ae({className:B("".concat(o,"-circle"),v),viewBox:"0 0 ".concat(Wn," ").concat(Wn),style:p,id:r,role:"presentation"},g),!T&&d.createElement("circle",{className:"".concat(o,"-circle-trail"),r:y,cx:b,cy:b,stroke:f,strokeLinecap:z,strokeWidth:s||a,style:D}),T?$():F())};function Yt(e){return!e||e<0?0:e>100?100:e}function qr({success:e,successPercent:t}){let n=t;return e&&"progress"in e&&(n=e.progress),e&&"percent"in e&&(n=e.percent),n}const J0=({percent:e,success:t,successPercent:n})=>{const r=Yt(qr({success:t,successPercent:n}));return[r,Yt(Yt(e)-r)]},Q0=({success:e={},strokeColor:t})=>{const{strokeColor:n}=e;return[n||ri.green,t||null]},ro=(e,t,n)=>{var r,o,i,a;let s=-1,l=-1;if(t==="step"){const c=n.steps,u=n.strokeWidth;typeof e=="string"||typeof e>"u"?(s=e==="small"?2:14,l=u??8):typeof e=="number"?[s,l]=[e,e]:[s=14,l=8]=Array.isArray(e)?e:[e.width,e.height],s*=c}else if(t==="line"){const c=n==null?void 0:n.strokeWidth;typeof e=="string"||typeof e>"u"?l=c||(e==="small"?6:8):typeof e=="number"?[s,l]=[e,e]:[s=-1,l=8]=Array.isArray(e)?e:[e.width,e.height]}else(t==="circle"||t==="dashboard")&&(typeof e=="string"||typeof e>"u"?[s,l]=e==="small"?[60,60]:[120,120]:typeof e=="number"?[s,l]=[e,e]:Array.isArray(e)&&(s=(o=(r=e[0])!==null&&r!==void 0?r:e[1])!==null&&o!==void 0?o:120,l=(a=(i=e[0])!==null&&i!==void 0?i:e[1])!==null&&a!==void 0?a:120));return[s,l]},ev=3,tv=e=>ev/e*100,nv=e=>{const{prefixCls:t,trailColor:n=null,strokeLinecap:r="round",gapPosition:o,gapDegree:i,width:a=120,type:s,children:l,success:c,size:u=a,steps:f}=e,[m,p]=ro(u,"circle");let{strokeWidth:v}=e;v===void 0&&(v=Math.max(tv(m),6));const h={width:m,height:p,fontSize:m*.15+6},C=d.useMemo(()=>{if(i||i===0)return i;if(s==="dashboard")return 75},[i,s]),g=J0(e),b=o||s==="dashboard"&&"bottom"||void 0,w=Object.prototype.toString.call(e.strokeColor)==="[object Object]",E=Q0({success:c,strokeColor:e.strokeColor}),y=B(`${t}-inner`,{[`${t}-circle-gradient`]:w}),x=d.createElement(Z0,{steps:f,percent:f?g[1]:g,strokeWidth:v,trailWidth:v,strokeColor:f?E[1]:E,strokeLinecap:r,trailColor:n,prefixCls:t,gapDegree:C,gapPosition:b}),I=m<=20,R=d.createElement("div",{className:y,style:h},x,!I&&l);return I?d.createElement(ln,{title:l},R):R},Yr="--progress-line-stroke-color",du="--progress-percent",hs=e=>{const t=e?"100%":"-100%";return new It(`antProgress${e?"RTL":"LTR"}Active`,{"0%":{transform:`translateX(${t}) scaleX(0)`,opacity:.1},"20%":{transform:`translateX(${t}) scaleX(0)`,opacity:.5},to:{transform:"translateX(0) scaleX(1)",opacity:0}})},rv=e=>{const{componentCls:t,iconCls:n}=e;return{[t]:Object.assign(Object.assign({},Ht(e)),{display:"inline-block","&-rtl":{direction:"rtl"},"&-line":{position:"relative",width:"100%",fontSize:e.fontSize},[`${t}-outer`]:{display:"inline-flex",alignItems:"center",width:"100%"},[`${t}-inner`]:{position:"relative",display:"inline-block",width:"100%",flex:1,overflow:"hidden",verticalAlign:"middle",backgroundColor:e.remainingColor,borderRadius:e.lineBorderRadius},[`${t}-inner:not(${t}-circle-gradient)`]:{[`${t}-circle-path`]:{stroke:e.defaultColor}},[`${t}-success-bg, ${t}-bg`]:{position:"relative",background:e.defaultColor,borderRadius:e.lineBorderRadius,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOutCirc}`},[`${t}-layout-bottom`]:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",[`${t}-text`]:{width:"max-content",marginInlineStart:0,marginTop:e.marginXXS}},[`${t}-bg`]:{overflow:"hidden","&::after":{content:'""',background:{_multi_value_:!0,value:["inherit",`var(${Yr})`]},height:"100%",width:`calc(1 / var(${du}) * 100%)`,display:"block"},[`&${t}-bg-inner`]:{minWidth:"max-content","&::after":{content:"none"},[`${t}-text-inner`]:{color:e.colorWhite,[`&${t}-text-bright`]:{color:"rgba(0, 0, 0, 0.45)"}}}},[`${t}-success-bg`]:{position:"absolute",insetBlockStart:0,insetInlineStart:0,backgroundColor:e.colorSuccess},[`${t}-text`]:{display:"inline-block",marginInlineStart:e.marginXS,color:e.colorText,lineHeight:1,width:"2em",whiteSpace:"nowrap",textAlign:"start",verticalAlign:"middle",wordBreak:"normal",[n]:{fontSize:e.fontSize},[`&${t}-text-outer`]:{width:"max-content"},[`&${t}-text-outer${t}-text-start`]:{width:"max-content",marginInlineStart:0,marginInlineEnd:e.marginXS}},[`${t}-text-inner`]:{display:"flex",justifyContent:"center",alignItems:"center",width:"100%",height:"100%",marginInlineStart:0,padding:`0 ${H(e.paddingXXS)}`,[`&${t}-text-start`]:{justifyContent:"start"},[`&${t}-text-end`]:{justifyContent:"end"}},[`&${t}-status-active`]:{[`${t}-bg::before`]:{position:"absolute",inset:0,backgroundColor:e.colorBgContainer,borderRadius:e.lineBorderRadius,opacity:0,animationName:hs(),animationDuration:e.progressActiveMotionDuration,animationTimingFunction:e.motionEaseOutQuint,animationIterationCount:"infinite",content:'""'}},[`&${t}-rtl${t}-status-active`]:{[`${t}-bg::before`]:{animationName:hs(!0)}},[`&${t}-status-exception`]:{[`${t}-bg`]:{backgroundColor:e.colorError},[`${t}-text`]:{color:e.colorError}},[`&${t}-status-exception ${t}-inner:not(${t}-circle-gradient)`]:{[`${t}-circle-path`]:{stroke:e.colorError}},[`&${t}-status-success`]:{[`${t}-bg`]:{backgroundColor:e.colorSuccess},[`${t}-text`]:{color:e.colorSuccess}},[`&${t}-status-success ${t}-inner:not(${t}-circle-gradient)`]:{[`${t}-circle-path`]:{stroke:e.colorSuccess}}})}},ov=e=>{const{componentCls:t,iconCls:n}=e;return{[t]:{[`${t}-circle-trail`]:{stroke:e.remainingColor},[`&${t}-circle ${t}-inner`]:{position:"relative",lineHeight:1,backgroundColor:"transparent"},[`&${t}-circle ${t}-text`]:{position:"absolute",insetBlockStart:"50%",insetInlineStart:0,width:"100%",margin:0,padding:0,color:e.circleTextColor,fontSize:e.circleTextFontSize,lineHeight:1,whiteSpace:"normal",textAlign:"center",transform:"translateY(-50%)",[n]:{fontSize:e.circleIconFontSize}},[`${t}-circle&-status-exception`]:{[`${t}-text`]:{color:e.colorError}},[`${t}-circle&-status-success`]:{[`${t}-text`]:{color:e.colorSuccess}}},[`${t}-inline-circle`]:{lineHeight:1,[`${t}-inner`]:{verticalAlign:"bottom"}}}},iv=e=>{const{componentCls:t}=e;return{[t]:{[`${t}-steps`]:{display:"inline-block","&-outer":{display:"flex",flexDirection:"row",alignItems:"center"},"&-item":{flexShrink:0,minWidth:e.progressStepMinWidth,marginInlineEnd:e.progressStepMarginInlineEnd,backgroundColor:e.remainingColor,transition:`all ${e.motionDurationSlow}`,"&-active":{backgroundColor:e.defaultColor}}}}}},av=e=>{const{componentCls:t,iconCls:n}=e;return{[t]:{[`${t}-small&-line, ${t}-small&-line ${t}-text ${n}`]:{fontSize:e.fontSizeSM}}}},sv=e=>({circleTextColor:e.colorText,defaultColor:e.colorInfo,remainingColor:e.colorFillSecondary,lineBorderRadius:100,circleTextFontSize:"1em",circleIconFontSize:`${e.fontSize/e.fontSizeSM}em`}),lv=xt("Progress",e=>{const t=e.calc(e.marginXXS).div(2).equal(),n=tt(e,{progressStepMarginInlineEnd:t,progressStepMinWidth:t,progressActiveMotionDuration:"2.4s"});return[rv(n),ov(n),iv(n),av(n)]},sv);var cv=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const uv=e=>{let t=[];return Object.keys(e).forEach(n=>{const r=parseFloat(n.replace(/%/g,""));Number.isNaN(r)||t.push({key:r,value:e[n]})}),t=t.sort((n,r)=>n.key-r.key),t.map(({key:n,value:r})=>`${r} ${n}%`).join(", ")},dv=(e,t)=>{const{from:n=ri.blue,to:r=ri.blue,direction:o=t==="rtl"?"to left":"to right"}=e,i=cv(e,["from","to","direction"]);if(Object.keys(i).length!==0){const s=uv(i),l=`linear-gradient(${o}, ${s})`;return{background:l,[Yr]:l}}const a=`linear-gradient(${o}, ${n}, ${r})`;return{background:a,[Yr]:a}},fv=e=>{const{prefixCls:t,direction:n,percent:r,size:o,strokeWidth:i,strokeColor:a,strokeLinecap:s="round",children:l,trailColor:c=null,percentPosition:u,success:f}=e,{align:m,type:p}=u,v=a&&typeof a!="string"?dv(a,n):{[Yr]:a,background:a},h=s==="square"||s==="butt"?0:void 0,C=o??[-1,i||(o==="small"?6:8)],[g,b]=ro(C,"line",{strokeWidth:i}),w={backgroundColor:c||void 0,borderRadius:h},E=Object.assign(Object.assign({width:`${Yt(r)}%`,height:b,borderRadius:h},v),{[du]:Yt(r)/100}),y=qr(e),x={width:`${Yt(y)}%`,height:b,borderRadius:h,backgroundColor:f==null?void 0:f.strokeColor},I={width:g<0?"100%":g},R=d.createElement("div",{className:`${t}-inner`,style:w},d.createElement("div",{className:B(`${t}-bg`,`${t}-bg-${p}`),style:E},p==="inner"&&l),y!==void 0&&d.createElement("div",{className:`${t}-success-bg`,style:x})),k=p==="outer"&&m==="start",T=p==="outer"&&m==="end";return p==="outer"&&m==="center"?d.createElement("div",{className:`${t}-layout-bottom`},R,l):d.createElement("div",{className:`${t}-outer`,style:I},k&&l,R,T&&l)},pv=e=>{const{size:t,steps:n,rounding:r=Math.round,percent:o=0,strokeWidth:i=8,strokeColor:a,trailColor:s=null,prefixCls:l,children:c}=e,u=r(n*(o/100)),m=t??[t==="small"?2:14,i],[p,v]=ro(m,"step",{steps:n,strokeWidth:i}),h=p/n,C=Array.from({length:n});for(let g=0;g<n;g++){const b=Array.isArray(a)?a[g]:a;C[g]=d.createElement("div",{key:g,className:B(`${l}-steps-item`,{[`${l}-steps-item-active`]:g<=u-1}),style:{backgroundColor:g<=u-1?b:s,width:h,height:v}})}return d.createElement("div",{className:`${l}-steps-outer`},C,c)};var mv=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const hv=["normal","exception","active","success"],fu=d.forwardRef((e,t)=>{const{prefixCls:n,className:r,rootClassName:o,steps:i,strokeColor:a,percent:s=0,size:l="default",showInfo:c=!0,type:u="line",status:f,format:m,style:p,percentPosition:v={}}=e,h=mv(e,["prefixCls","className","rootClassName","steps","strokeColor","percent","size","showInfo","type","status","format","style","percentPosition"]),{align:C="end",type:g="outer"}=v,b=Array.isArray(a)?a[0]:a,w=typeof a=="string"||Array.isArray(a)?a:void 0,E=d.useMemo(()=>{if(b){const F=typeof b=="string"?b:Object.values(b)[0];return new Rt(F).isLight()}return!1},[a]),y=d.useMemo(()=>{var F,$;const G=qr(e);return parseInt(G!==void 0?(F=G??0)===null||F===void 0?void 0:F.toString():($=s??0)===null||$===void 0?void 0:$.toString(),10)},[s,e.success,e.successPercent]),x=d.useMemo(()=>!hv.includes(f)&&y>=100?"success":f||"normal",[f,y]),{getPrefixCls:I,direction:R,progress:k}=d.useContext(Ve),T=I("progress",n),[O,N,j]=lv(T),M=u==="line",L=M&&!i,z=d.useMemo(()=>{if(!c)return null;const F=qr(e);let $;const G=m||(S=>`${S}%`),q=M&&E&&g==="inner";return g==="inner"||m||x!=="exception"&&x!=="success"?$=G(Yt(s),Yt(F)):x==="exception"?$=M?d.createElement(eo,null):d.createElement(an,null):x==="success"&&($=M?d.createElement(ki,null):d.createElement(_i,null)),d.createElement("span",{className:B(`${T}-text`,{[`${T}-text-bright`]:q,[`${T}-text-${C}`]:L,[`${T}-text-${g}`]:L}),title:typeof $=="string"?$:void 0},$)},[c,s,y,x,u,T,m]);let D;u==="line"?D=i?d.createElement(pv,Object.assign({},e,{strokeColor:w,prefixCls:T,steps:typeof i=="object"?i.count:i}),z):d.createElement(fv,Object.assign({},e,{strokeColor:b,prefixCls:T,direction:R,percentPosition:{align:C,type:g}}),z):(u==="circle"||u==="dashboard")&&(D=d.createElement(nv,Object.assign({},e,{strokeColor:b,prefixCls:T,progressStatus:x}),z));const W=B(T,`${T}-status-${x}`,{[`${T}-${u==="dashboard"&&"circle"||u}`]:u!=="line",[`${T}-inline-circle`]:u==="circle"&&ro(l,"circle")[0]<=20,[`${T}-line`]:L,[`${T}-line-align-${C}`]:L,[`${T}-line-position-${g}`]:L,[`${T}-steps`]:i,[`${T}-show-info`]:c,[`${T}-${l}`]:typeof l=="string",[`${T}-rtl`]:R==="rtl"},k==null?void 0:k.className,r,o,N,j);return O(d.createElement("div",Object.assign({ref:t,style:Object.assign(Object.assign({},k==null?void 0:k.style),p),className:W,role:"progressbar","aria-valuenow":y,"aria-valuemin":0,"aria-valuemax":100},Pn(h,["trailColor","strokeWidth","width","gapDegree","gapPosition","strokeLinecap","success","successPercent"])),D))}),gv=e=>{const t=e!=null&&e.algorithm?tc(e.algorithm):yf,n=Object.assign(Object.assign({},bf),e==null?void 0:e.token);return xf(n,{override:e==null?void 0:e.token},t,nc)};function vv(e){const{sizeUnit:t,sizeStep:n}=e,r=n-2;return{sizeXXL:t*(r+10),sizeXL:t*(r+6),sizeLG:t*(r+2),sizeMD:t*(r+2),sizeMS:t*(r+1),size:t*r,sizeSM:t*r,sizeXS:t*(r-1),sizeXXS:t*(r-1)}}const yv=(e,t)=>{const n=t??Ai(e),r=n.fontSizeSM,o=n.controlHeight-4;return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},n),vv(t??e)),Cf(r)),{controlHeight:o}),Sf(Object.assign(Object.assign({},n),{controlHeight:o})))},$t=(e,t)=>new Rt(e).setA(t).toRgbString(),mn=(e,t)=>new Rt(e).lighten(t).toHexString(),bv=e=>{const t=rc(e,{theme:"dark"});return{1:t[0],2:t[1],3:t[2],4:t[3],5:t[6],6:t[5],7:t[4],8:t[6],9:t[5],10:t[4]}},xv=(e,t)=>{const n=e||"#000",r=t||"#fff";return{colorBgBase:n,colorTextBase:r,colorText:$t(r,.85),colorTextSecondary:$t(r,.65),colorTextTertiary:$t(r,.45),colorTextQuaternary:$t(r,.25),colorFill:$t(r,.18),colorFillSecondary:$t(r,.12),colorFillTertiary:$t(r,.08),colorFillQuaternary:$t(r,.04),colorBgSolid:$t(r,.95),colorBgSolidHover:$t(r,1),colorBgSolidActive:$t(r,.9),colorBgElevated:mn(n,12),colorBgContainer:mn(n,8),colorBgLayout:mn(n,0),colorBgSpotlight:mn(n,26),colorBgBlur:$t(r,.04),colorBorder:mn(n,26),colorBorderSecondary:mn(n,19)}},Cv=(e,t)=>{const n=Object.keys(wf).map(i=>{const a=rc(e[i],{theme:"dark"});return Array.from({length:10},()=>1).reduce((s,l,c)=>(s[`${i}-${c+1}`]=a[c],s[`${i}${c+1}`]=a[c],s),{})}).reduce((i,a)=>(i=Object.assign(Object.assign({},i),a),i),{}),r=t??Ai(e),o=$f(e,{generateColorPalettes:bv,generateNeutralColorPalettes:xv});return Object.assign(Object.assign(Object.assign(Object.assign({},r),n),o),{colorPrimaryBg:o.colorPrimaryBorder,colorPrimaryBgHover:o.colorPrimaryBorderHover})};function Sv(){const[e,t,n]=Ni();return{theme:e,token:t,hashId:n}}const nn={defaultSeed:ka.token,useToken:Sv,defaultAlgorithm:Ai,darkAlgorithm:Cv,compactAlgorithm:yv,getDesignToken:gv,defaultConfig:ka,_internalContext:Ef},wv=(e,t,n,r)=>{const{titleMarginBottom:o,fontWeightStrong:i}=r;return{marginBottom:o,color:n,fontWeight:i,fontSize:e,lineHeight:t}},$v=e=>{const t=[1,2,3,4,5],n={};return t.forEach(r=>{n[`
      h${r}&,
      div&-h${r},
      div&-h${r} > textarea,
      h${r}
    `]=wv(e[`fontSizeHeading${r}`],e[`lineHeightHeading${r}`],e.colorTextHeading,e)}),n},Ev=e=>{const{componentCls:t}=e;return{"a&, a":Object.assign(Object.assign({},oc(e)),{userSelect:"text",[`&[disabled], &${t}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed","&:active, &:hover":{color:e.colorTextDisabled},"&:active":{pointerEvents:"none"}}})}},Iv=e=>({code:{margin:"0 0.2em",paddingInline:"0.4em",paddingBlock:"0.2em 0.1em",fontSize:"85%",fontFamily:e.fontFamilyCode,background:"rgba(150, 150, 150, 0.1)",border:"1px solid rgba(100, 100, 100, 0.2)",borderRadius:3},kbd:{margin:"0 0.2em",paddingInline:"0.4em",paddingBlock:"0.15em 0.1em",fontSize:"90%",fontFamily:e.fontFamilyCode,background:"rgba(150, 150, 150, 0.06)",border:"1px solid rgba(100, 100, 100, 0.2)",borderBottomWidth:2,borderRadius:3},mark:{padding:0,backgroundColor:If[2]},"u, ins":{textDecoration:"underline",textDecorationSkipInk:"auto"},"s, del":{textDecoration:"line-through"},strong:{fontWeight:e.fontWeightStrong},"ul, ol":{marginInline:0,marginBlock:"0 1em",padding:0,li:{marginInline:"20px 0",marginBlock:0,paddingInline:"4px 0",paddingBlock:0}},ul:{listStyleType:"circle",ul:{listStyleType:"disc"}},ol:{listStyleType:"decimal"},"pre, blockquote":{margin:"1em 0"},pre:{padding:"0.4em 0.6em",whiteSpace:"pre-wrap",wordWrap:"break-word",background:"rgba(150, 150, 150, 0.1)",border:"1px solid rgba(100, 100, 100, 0.2)",borderRadius:3,fontFamily:e.fontFamilyCode,code:{display:"inline",margin:0,padding:0,fontSize:"inherit",fontFamily:"inherit",background:"transparent",border:0}},blockquote:{paddingInline:"0.6em 0",paddingBlock:0,borderInlineStart:"4px solid rgba(100, 100, 100, 0.2)",opacity:.85}}),Pv=e=>{const{componentCls:t,paddingSM:n}=e,r=n;return{"&-edit-content":{position:"relative","div&":{insetInlineStart:e.calc(e.paddingSM).mul(-1).equal(),marginTop:e.calc(r).mul(-1).equal(),marginBottom:`calc(1em - ${H(r)})`},[`${t}-edit-content-confirm`]:{position:"absolute",insetInlineEnd:e.calc(e.marginXS).add(2).equal(),insetBlockEnd:e.marginXS,color:e.colorIcon,fontWeight:"normal",fontSize:e.fontSize,fontStyle:"normal",pointerEvents:"none"},textarea:{margin:"0!important",MozTransition:"none",height:"1em"}}}},Tv=e=>({[`${e.componentCls}-copy-success`]:{"\n    &,\n    &:hover,\n    &:focus":{color:e.colorSuccess}},[`${e.componentCls}-copy-icon-only`]:{marginInlineStart:0}}),Ov=()=>({"\n  a&-ellipsis,\n  span&-ellipsis\n  ":{display:"inline-block",maxWidth:"100%"},"&-ellipsis-single-line":{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis","a&, span&":{verticalAlign:"bottom"},"> code":{paddingBlock:0,maxWidth:"calc(100% - 1.2em)",display:"inline-block",overflow:"hidden",textOverflow:"ellipsis",verticalAlign:"bottom",boxSizing:"content-box"}},"&-ellipsis-multiple-line":{display:"-webkit-box",overflow:"hidden",WebkitLineClamp:3,WebkitBoxOrient:"vertical"}}),Rv=e=>{const{componentCls:t,titleMarginTop:n}=e;return{[t]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({color:e.colorText,wordBreak:"break-word",lineHeight:e.lineHeight,[`&${t}-secondary`]:{color:e.colorTextDescription},[`&${t}-success`]:{color:e.colorSuccessText},[`&${t}-warning`]:{color:e.colorWarningText},[`&${t}-danger`]:{color:e.colorErrorText,"a&:active, a&:focus":{color:e.colorErrorTextActive},"a&:hover":{color:e.colorErrorTextHover}},[`&${t}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed",userSelect:"none"},"\n        div&,\n        p\n      ":{marginBottom:"1em"}},$v(e)),{[`
      & + h1${t},
      & + h2${t},
      & + h3${t},
      & + h4${t},
      & + h5${t}
      `]:{marginTop:n},"\n      div,\n      ul,\n      li,\n      p,\n      h1,\n      h2,\n      h3,\n      h4,\n      h5":{"\n        + h1,\n        + h2,\n        + h3,\n        + h4,\n        + h5\n        ":{marginTop:n}}}),Iv(e)),Ev(e)),{[`
        ${t}-expand,
        ${t}-collapse,
        ${t}-edit,
        ${t}-copy
      `]:Object.assign(Object.assign({},oc(e)),{marginInlineStart:e.marginXXS})}),Pv(e)),Tv(e)),Ov()),{"&-rtl":{direction:"rtl"}})}},Nv=()=>({titleMarginTop:"1.2em",titleMarginBottom:"0.5em"}),pu=xt("Typography",e=>[Rv(e)],Nv),kv=e=>{const{prefixCls:t,"aria-label":n,className:r,style:o,direction:i,maxLength:a,autoSize:s=!0,value:l,onSave:c,onCancel:u,onEnd:f,component:m,enterIcon:p=d.createElement(Ep,null)}=e,v=d.useRef(null),h=d.useRef(!1),C=d.useRef(null),[g,b]=d.useState(l);d.useEffect(()=>{b(l)},[l]),d.useEffect(()=>{var M;if(!((M=v.current)===null||M===void 0)&&M.resizableTextArea){const{textArea:L}=v.current.resizableTextArea;L.focus();const{length:z}=L.value;L.setSelectionRange(z,z)}},[]);const w=({target:M})=>{b(M.value.replace(/[\n\r]/g,""))},E=()=>{h.current=!0},y=()=>{h.current=!1},x=({keyCode:M})=>{h.current||(C.current=M)},I=()=>{c(g.trim())},R=({keyCode:M,ctrlKey:L,altKey:z,metaKey:D,shiftKey:W})=>{C.current!==M||h.current||L||z||D||W||(M===it.ENTER?(I(),f==null||f()):M===it.ESC&&u())},k=()=>{I()},[T,O,N]=pu(t),j=B(t,`${t}-edit-content`,{[`${t}-rtl`]:i==="rtl",[`${t}-${m}`]:!!m},r,O,N);return T(d.createElement("div",{className:j,style:o},d.createElement(Pf,{ref:v,maxLength:a,value:g,onChange:w,onKeyDown:x,onKeyUp:R,onCompositionStart:E,onCompositionEnd:y,onBlur:k,"aria-label":n,rows:1,autoSize:s}),p!==null?Zn(p,{className:`${t}-edit-content-confirm`}):null))};var So,gs;function Mv(){return gs||(gs=1,So=function(){var e=document.getSelection();if(!e.rangeCount)return function(){};for(var t=document.activeElement,n=[],r=0;r<e.rangeCount;r++)n.push(e.getRangeAt(r));switch(t.tagName.toUpperCase()){case"INPUT":case"TEXTAREA":t.blur();break;default:t=null;break}return e.removeAllRanges(),function(){e.type==="Caret"&&e.removeAllRanges(),e.rangeCount||n.forEach(function(o){e.addRange(o)}),t&&t.focus()}}),So}var wo,vs;function jv(){if(vs)return wo;vs=1;var e=Mv(),t={"text/plain":"Text","text/html":"Url",default:"Text"},n="Copy to clipboard: #{key}, Enter";function r(i){var a=(/mac os x/i.test(navigator.userAgent)?"⌘":"Ctrl")+"+C";return i.replace(/#{\s*key\s*}/g,a)}function o(i,a){var s,l,c,u,f,m,p=!1;a||(a={}),s=a.debug||!1;try{c=e(),u=document.createRange(),f=document.getSelection(),m=document.createElement("span"),m.textContent=i,m.ariaHidden="true",m.style.all="unset",m.style.position="fixed",m.style.top=0,m.style.clip="rect(0, 0, 0, 0)",m.style.whiteSpace="pre",m.style.webkitUserSelect="text",m.style.MozUserSelect="text",m.style.msUserSelect="text",m.style.userSelect="text",m.addEventListener("copy",function(h){if(h.stopPropagation(),a.format)if(h.preventDefault(),typeof h.clipboardData>"u"){s&&console.warn("unable to use e.clipboardData"),s&&console.warn("trying IE specific stuff"),window.clipboardData.clearData();var C=t[a.format]||t.default;window.clipboardData.setData(C,i)}else h.clipboardData.clearData(),h.clipboardData.setData(a.format,i);a.onCopy&&(h.preventDefault(),a.onCopy(h.clipboardData))}),document.body.appendChild(m),u.selectNodeContents(m),f.addRange(u);var v=document.execCommand("copy");if(!v)throw new Error("copy command was unsuccessful");p=!0}catch(h){s&&console.error("unable to copy using execCommand: ",h),s&&console.warn("trying IE specific stuff");try{window.clipboardData.setData(a.format||"text",i),a.onCopy&&a.onCopy(window.clipboardData),p=!0}catch(C){s&&console.error("unable to copy using clipboardData: ",C),s&&console.error("falling back to prompt"),l=r("message"in a?a.message:n),window.prompt(l,i)}}finally{f&&(typeof f.removeRange=="function"?f.removeRange(u):f.removeAllRanges()),m&&document.body.removeChild(m),c()}return p}return wo=o,wo}var Lv=jv();const _v=Ti(Lv);var Av=function(e,t,n,r){function o(i){return i instanceof n?i:new n(function(a){a(i)})}return new(n||(n=Promise))(function(i,a){function s(u){try{c(r.next(u))}catch(f){a(f)}}function l(u){try{c(r.throw(u))}catch(f){a(f)}}function c(u){u.done?i(u.value):o(u.value).then(s,l)}c((r=r.apply(e,t||[])).next())})};const zv=({copyConfig:e,children:t})=>{const[n,r]=d.useState(!1),[o,i]=d.useState(!1),a=d.useRef(null),s=()=>{a.current&&clearTimeout(a.current)},l={};e.format&&(l.format=e.format),d.useEffect(()=>s,[]);const c=tn(u=>Av(void 0,void 0,void 0,function*(){var f;u==null||u.preventDefault(),u==null||u.stopPropagation(),i(!0);try{const m=typeof e.text=="function"?yield e.text():e.text;_v(m||j0(t,!0).join("")||"",l),i(!1),r(!0),s(),a.current=setTimeout(()=>{r(!1)},3e3),(f=e.onCopy)===null||f===void 0||f.call(e,u)}catch(m){throw i(!1),m}}));return{copied:n,copyLoading:o,onClick:c}};function $o(e,t){return d.useMemo(()=>{const n=!!e;return[n,Object.assign(Object.assign({},t),n&&typeof e=="object"?e:null)]},[e])}const Dv=e=>{const t=d.useRef(void 0);return d.useEffect(()=>{t.current=e}),t.current},Bv=(e,t,n)=>d.useMemo(()=>e===!0?{title:t??n}:d.isValidElement(e)?{title:e}:typeof e=="object"?Object.assign({title:t??n},e):{title:e},[e,t,n]);var Fv=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const mu=d.forwardRef((e,t)=>{const{prefixCls:n,component:r="article",className:o,rootClassName:i,setContentRef:a,children:s,direction:l,style:c}=e,u=Fv(e,["prefixCls","component","className","rootClassName","setContentRef","children","direction","style"]),{getPrefixCls:f,direction:m,className:p,style:v}=Li("typography"),h=l??m,C=a?Mi(t,a):t,g=f("typography",n),[b,w,E]=pu(g),y=B(g,p,{[`${g}-rtl`]:h==="rtl"},o,i,w,E),x=Object.assign(Object.assign({},v),c);return b(d.createElement(r,Object.assign({className:y,style:x,ref:C},u),s))});function ys(e){return e===!1?[!1,!1]:Array.isArray(e)?e:[e]}function Eo(e,t,n){return e===!0||e===void 0?t:e||n&&t}function Hv(e){const t=document.createElement("em");e.appendChild(t);const n=e.getBoundingClientRect(),r=t.getBoundingClientRect();return e.removeChild(t),n.left>r.left||r.right>n.right||n.top>r.top||r.bottom>n.bottom}const qi=e=>["string","number"].includes(typeof e),Wv=({prefixCls:e,copied:t,locale:n,iconOnly:r,tooltips:o,icon:i,tabIndex:a,onCopy:s,loading:l})=>{const c=ys(o),u=ys(i),{copied:f,copy:m}=n??{},p=t?f:m,v=Eo(c[t?1:0],p),h=typeof v=="string"?v:p;return d.createElement(ln,{title:v},d.createElement("button",{type:"button",className:B(`${e}-copy`,{[`${e}-copy-success`]:t,[`${e}-copy-icon-only`]:r}),onClick:s,"aria-label":h,tabIndex:a},t?Eo(u[1],d.createElement(_i,null),!0):Eo(u[0],l?d.createElement(oi,null):d.createElement(ic,null),!0)))},Sr=d.forwardRef(({style:e,children:t},n)=>{const r=d.useRef(null);return d.useImperativeHandle(n,()=>({isExceed:()=>{const o=r.current;return o.scrollHeight>o.clientHeight},getHeight:()=>r.current.clientHeight})),d.createElement("span",{"aria-hidden":!0,ref:r,style:Object.assign({position:"fixed",display:"block",left:0,top:0,pointerEvents:"none",backgroundColor:"rgba(255, 0, 0, 0.65)"},e)},t)}),Vv=e=>e.reduce((t,n)=>t+(qi(n)?String(n).length:1),0);function bs(e,t){let n=0;const r=[];for(let o=0;o<e.length;o+=1){if(n===t)return r;const i=e[o],s=qi(i)?String(i).length:1,l=n+s;if(l>t){const c=t-n;return r.push(String(i).slice(0,c)),r}r.push(i),n=l}return e}const Io=0,Po=1,To=2,Oo=3,xs=4,wr={display:"-webkit-box",overflow:"hidden",WebkitBoxOrient:"vertical"};function Uv(e){const{enableMeasure:t,width:n,text:r,children:o,rows:i,expanded:a,miscDeps:s,onEllipsis:l}=e,c=d.useMemo(()=>to(r),[r]),u=d.useMemo(()=>Vv(c),[r]),f=d.useMemo(()=>o(c,!1),[r]),[m,p]=d.useState(null),v=d.useRef(null),h=d.useRef(null),C=d.useRef(null),g=d.useRef(null),b=d.useRef(null),[w,E]=d.useState(!1),[y,x]=d.useState(Io),[I,R]=d.useState(0),[k,T]=d.useState(null);Qt(()=>{x(t&&n&&u?Po:Io)},[n,r,i,t,c]),Qt(()=>{var M,L,z,D;if(y===Po){x(To);const W=h.current&&getComputedStyle(h.current).whiteSpace;T(W)}else if(y===To){const W=!!(!((M=C.current)===null||M===void 0)&&M.isExceed());x(W?Oo:xs),p(W?[0,u]:null),E(W);const F=((L=C.current)===null||L===void 0?void 0:L.getHeight())||0,$=i===1?0:((z=g.current)===null||z===void 0?void 0:z.getHeight())||0,G=((D=b.current)===null||D===void 0?void 0:D.getHeight())||0,q=Math.max(F,$+G);R(q+1),l(W)}},[y]);const O=m?Math.ceil((m[0]+m[1])/2):0;Qt(()=>{var M;const[L,z]=m||[0,0];if(L!==z){const W=(((M=v.current)===null||M===void 0?void 0:M.getHeight())||0)>I;let F=O;z-L===1&&(F=W?L:z),p(W?[L,F]:[F,z])}},[m,O]);const N=d.useMemo(()=>{if(!t)return o(c,!1);if(y!==Oo||!m||m[0]!==m[1]){const M=o(c,!1);return[xs,Io].includes(y)?M:d.createElement("span",{style:Object.assign(Object.assign({},wr),{WebkitLineClamp:i})},M)}return o(a?c:bs(c,m[0]),w)},[a,y,m,c].concat(He(s))),j={width:n,margin:0,padding:0,whiteSpace:k==="nowrap"?"normal":"inherit"};return d.createElement(d.Fragment,null,N,y===To&&d.createElement(d.Fragment,null,d.createElement(Sr,{style:Object.assign(Object.assign(Object.assign({},j),wr),{WebkitLineClamp:i}),ref:C},f),d.createElement(Sr,{style:Object.assign(Object.assign(Object.assign({},j),wr),{WebkitLineClamp:i-1}),ref:g},f),d.createElement(Sr,{style:Object.assign(Object.assign(Object.assign({},j),wr),{WebkitLineClamp:1}),ref:b},o([],!0))),y===Oo&&m&&m[0]!==m[1]&&d.createElement(Sr,{style:Object.assign(Object.assign({},j),{top:400}),ref:v},o(bs(c,O),!0)),y===Po&&d.createElement("span",{style:{whiteSpace:"inherit"},ref:h}))}const Xv=({enableEllipsis:e,isEllipsis:t,children:n,tooltipProps:r})=>!(r!=null&&r.title)||!e?n:d.createElement(ln,Object.assign({open:t?void 0:!1},r),n);var Gv=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};function qv({mark:e,code:t,underline:n,delete:r,strong:o,keyboard:i,italic:a},s){let l=s;function c(u,f){f&&(l=d.createElement(u,{},l))}return c("strong",o),c("u",n),c("del",r),c("code",t),c("mark",e),c("kbd",i),c("i",a),l}const Yv="...",Cs=["delete","mark","code","underline","strong","keyboard","italic"],oo=d.forwardRef((e,t)=>{var n;const{prefixCls:r,className:o,style:i,type:a,disabled:s,children:l,ellipsis:c,editable:u,copyable:f,component:m,title:p}=e,v=Gv(e,["prefixCls","className","style","type","disabled","children","ellipsis","editable","copyable","component","title"]),{getPrefixCls:h,direction:C}=d.useContext(Ve),[g]=$n("Text"),b=d.useRef(null),w=d.useRef(null),E=h("typography",r),y=Pn(v,Cs),[x,I]=$o(u),[R,k]=bt(!1,{value:I.editing}),{triggerType:T=["icon"]}=I,O=X=>{var J;X&&((J=I.onStart)===null||J===void 0||J.call(I)),k(X)},N=Dv(R);Qt(()=>{var X;!R&&N&&((X=w.current)===null||X===void 0||X.focus())},[R]);const j=X=>{X==null||X.preventDefault(),O(!0)},M=X=>{var J;(J=I.onChange)===null||J===void 0||J.call(I,X),O(!1)},L=()=>{var X;(X=I.onCancel)===null||X===void 0||X.call(I),O(!1)},[z,D]=$o(f),{copied:W,copyLoading:F,onClick:$}=zv({copyConfig:D,children:l}),[G,q]=d.useState(!1),[S,Y]=d.useState(!1),[ne,K]=d.useState(!1),[ue,ce]=d.useState(!1),[me,fe]=d.useState(!0),[se,ee]=$o(c,{expandable:!1,symbol:X=>X?g==null?void 0:g.collapse:g==null?void 0:g.expand}),[de,oe]=bt(ee.defaultExpanded||!1,{value:ee.expanded}),ie=se&&(!de||ee.expandable==="collapsible"),{rows:be=1}=ee,Ie=d.useMemo(()=>ie&&(ee.suffix!==void 0||ee.onEllipsis||ee.expandable||x||z),[ie,ee,x,z]);Qt(()=>{se&&!Ie&&(q(Ma("webkitLineClamp")),Y(Ma("textOverflow")))},[Ie,se]);const[ye,Oe]=d.useState(ie),xe=d.useMemo(()=>Ie?!1:be===1?S:G,[Ie,S,G]);Qt(()=>{Oe(xe&&ie)},[xe,ie]);const $e=ie&&(ye?ue:ne),Ae=ie&&be===1&&ye,Re=ie&&be>1&&ye,Ue=(X,J)=>{var ge;oe(J.expanded),(ge=ee.onExpand)===null||ge===void 0||ge.call(ee,X,J)},[Ke,_]=d.useState(0),U=({offsetWidth:X})=>{_(X)},le=X=>{var J;K(X),ne!==X&&((J=ee.onEllipsis)===null||J===void 0||J.call(ee,X))};d.useEffect(()=>{const X=b.current;if(se&&ye&&X){const J=Hv(X);ue!==J&&ce(J)}},[se,ye,l,Re,me,Ke]),d.useEffect(()=>{const X=b.current;if(typeof IntersectionObserver>"u"||!X||!ye||!ie)return;const J=new IntersectionObserver(()=>{fe(!!X.offsetParent)});return J.observe(X),()=>{J.disconnect()}},[ye,ie]);const he=Bv(ee.tooltip,I.text,l),Ce=d.useMemo(()=>{if(!(!se||ye))return[I.text,l,p,he.title].find(qi)},[se,ye,p,he.title,$e]);if(R)return d.createElement(kv,{value:(n=I.text)!==null&&n!==void 0?n:typeof l=="string"?l:"",onSave:M,onCancel:L,onEnd:I.onEnd,prefixCls:E,className:o,style:i,direction:C,component:m,maxLength:I.maxLength,autoSize:I.autoSize,enterIcon:I.enterIcon});const ze=()=>{const{expandable:X,symbol:J}=ee;return X?d.createElement("button",{type:"button",key:"expand",className:`${E}-${de?"collapse":"expand"}`,onClick:ge=>Ue(ge,{expanded:!de}),"aria-label":de?g.collapse:g==null?void 0:g.expand},typeof J=="function"?J(de):J):null},V=()=>{if(!x)return;const{icon:X,tooltip:J,tabIndex:ge}=I,Te=to(J)[0]||(g==null?void 0:g.edit),We=typeof Te=="string"?Te:"";return T.includes("icon")?d.createElement(ln,{key:"edit",title:J===!1?"":Te},d.createElement("button",{type:"button",ref:w,className:`${E}-edit`,onClick:j,"aria-label":We,tabIndex:ge},X||d.createElement(Sp,{role:"button"}))):null},re=()=>z?d.createElement(Wv,Object.assign({key:"copy"},D,{prefixCls:E,copied:W,locale:g,onCopy:$,loading:F,iconOnly:l==null})):null,Z=X=>[X&&ze(),V(),re()],pe=X=>[X&&!de&&d.createElement("span",{"aria-hidden":!0,key:"ellipsis"},Yv),ee.suffix,Z(X)];return d.createElement(Un,{onResize:U,disabled:!ie},X=>d.createElement(Xv,{tooltipProps:he,enableEllipsis:ie,isEllipsis:$e},d.createElement(mu,Object.assign({className:B({[`${E}-${a}`]:a,[`${E}-disabled`]:s,[`${E}-ellipsis`]:se,[`${E}-ellipsis-single-line`]:Ae,[`${E}-ellipsis-multiple-line`]:Re},o),prefixCls:r,style:Object.assign(Object.assign({},i),{WebkitLineClamp:Re?be:void 0}),component:m,ref:Mi(X,b,t),direction:C,onClick:T.includes("text")?j:void 0,"aria-label":Ce==null?void 0:Ce.toString(),title:p},y),d.createElement(Uv,{enableMeasure:ie&&!ye,text:l,rows:be,width:Ke,onEllipsis:le,expanded:de,miscDeps:[W,de,F,x,z,g].concat(He(Cs.map(J=>e[J])))},(J,ge)=>qv(e,d.createElement(d.Fragment,null,J.length>0&&ge&&!de&&Ce?d.createElement("span",{key:"show-content","aria-hidden":!0},J):J,pe(ge)))))))});var Kv=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const Zv=d.forwardRef((e,t)=>{var{ellipsis:n,rel:r}=e,o=Kv(e,["ellipsis","rel"]);const i=Object.assign(Object.assign({},o),{rel:r===void 0&&o.target==="_blank"?"noopener noreferrer":r});return delete i.navigate,d.createElement(oo,Object.assign({},i,{ref:t,ellipsis:!!n,component:"a"}))}),Jv=d.forwardRef((e,t)=>d.createElement(oo,Object.assign({ref:t},e,{component:"div"})));var Qv=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const ey=(e,t)=>{var{ellipsis:n}=e,r=Qv(e,["ellipsis"]);const o=d.useMemo(()=>n&&typeof n=="object"?Pn(n,["expandable","rows"]):n,[n]);return d.createElement(oo,Object.assign({ref:t},r,{ellipsis:o,component:"span"}))},ty=d.forwardRef(ey);var ny=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const ry=[1,2,3,4,5],oy=d.forwardRef((e,t)=>{const{level:n=1}=e,r=ny(e,["level"]),o=ry.includes(n)?`h${n}`:"h1";return d.createElement(oo,Object.assign({ref:t},r,{component:o}))}),Ze=mu;Ze.Text=ty;Ze.Link=Zv;Ze.Title=oy;Ze.Paragraph=Jv;const Ro=function(e,t){if(e&&t){var n=Array.isArray(t)?t:t.split(","),r=e.name||"",o=e.type||"",i=o.replace(/\/.*$/,"");return n.some(function(a){var s=a.trim();if(/^\*(\/\*)?$/.test(a))return!0;if(s.charAt(0)==="."){var l=r.toLowerCase(),c=s.toLowerCase(),u=[c];return(c===".jpg"||c===".jpeg")&&(u=[".jpg",".jpeg"]),u.some(function(f){return l.endsWith(f)})}return/\/\*$/.test(s)?i===s.replace(/\/.*$/,""):o===s?!0:/^\w+$/.test(s)?(Tf(!1,"Upload takes an invalidate 'accept' type '".concat(s,"'.Skip for check.")),!0):!1})}return!0};function iy(e,t){var n="cannot ".concat(e.method," ").concat(e.action," ").concat(t.status,"'"),r=new Error(n);return r.status=t.status,r.method=e.method,r.url=e.action,r}function Ss(e){var t=e.responseText||e.response;if(!t)return t;try{return JSON.parse(t)}catch{return t}}function ay(e){var t=new XMLHttpRequest;e.onProgress&&t.upload&&(t.upload.onprogress=function(i){i.total>0&&(i.percent=i.loaded/i.total*100),e.onProgress(i)});var n=new FormData;e.data&&Object.keys(e.data).forEach(function(o){var i=e.data[o];if(Array.isArray(i)){i.forEach(function(a){n.append("".concat(o,"[]"),a)});return}n.append(o,i)}),e.file instanceof Blob?n.append(e.filename,e.file,e.file.name):n.append(e.filename,e.file),t.onerror=function(i){e.onError(i)},t.onload=function(){return t.status<200||t.status>=300?e.onError(iy(e,t),Ss(t)):e.onSuccess(Ss(t),t)},t.open(e.method,e.action,!0),e.withCredentials&&"withCredentials"in t&&(t.withCredentials=!0);var r=e.headers||{};return r["X-Requested-With"]!==null&&t.setRequestHeader("X-Requested-With","XMLHttpRequest"),Object.keys(r).forEach(function(o){r[o]!==null&&t.setRequestHeader(o,r[o])}),t.send(n),{abort:function(){t.abort()}}}var sy=function(){var e=qt(at().mark(function t(n,r){var o,i,a,s,l,c,u,f;return at().wrap(function(p){for(;;)switch(p.prev=p.next){case 0:c=function(){return c=qt(at().mark(function h(C){return at().wrap(function(b){for(;;)switch(b.prev=b.next){case 0:return b.abrupt("return",new Promise(function(w){C.file(function(E){r(E)?(C.fullPath&&!E.webkitRelativePath&&(Object.defineProperties(E,{webkitRelativePath:{writable:!0}}),E.webkitRelativePath=C.fullPath.replace(/^\//,""),Object.defineProperties(E,{webkitRelativePath:{writable:!1}})),w(E)):w(null)})}));case 1:case"end":return b.stop()}},h)})),c.apply(this,arguments)},l=function(h){return c.apply(this,arguments)},s=function(){return s=qt(at().mark(function h(C){var g,b,w,E,y;return at().wrap(function(I){for(;;)switch(I.prev=I.next){case 0:g=C.createReader(),b=[];case 2:return I.next=5,new Promise(function(R){g.readEntries(R,function(){return R([])})});case 5:if(w=I.sent,E=w.length,E){I.next=9;break}return I.abrupt("break",12);case 9:for(y=0;y<E;y++)b.push(w[y]);I.next=2;break;case 12:return I.abrupt("return",b);case 13:case"end":return I.stop()}},h)})),s.apply(this,arguments)},a=function(h){return s.apply(this,arguments)},o=[],i=[],n.forEach(function(v){return i.push(v.webkitGetAsEntry())}),u=function(){var v=qt(at().mark(function h(C,g){var b,w;return at().wrap(function(y){for(;;)switch(y.prev=y.next){case 0:if(C){y.next=2;break}return y.abrupt("return");case 2:if(C.path=g||"",!C.isFile){y.next=10;break}return y.next=6,l(C);case 6:b=y.sent,b&&o.push(b),y.next=15;break;case 10:if(!C.isDirectory){y.next=15;break}return y.next=13,a(C);case 13:w=y.sent,i.push.apply(i,He(w));case 15:case"end":return y.stop()}},h)}));return function(C,g){return v.apply(this,arguments)}}(),f=0;case 9:if(!(f<i.length)){p.next=15;break}return p.next=12,u(i[f]);case 12:f++,p.next=9;break;case 15:return p.abrupt("return",o);case 16:case"end":return p.stop()}},t)}));return function(n,r){return e.apply(this,arguments)}}(),ly=+new Date,cy=0;function No(){return"rc-upload-".concat(ly,"-").concat(++cy)}var uy=["component","prefixCls","className","classNames","disabled","id","name","style","styles","multiple","accept","capture","children","directory","openFileDialogOnClick","onMouseEnter","onMouseLeave","hasControlInside"],dy=function(e){ac(n,e);var t=sc(n);function n(){var r;zi(this,n);for(var o=arguments.length,i=new Array(o),a=0;a<o;a++)i[a]=arguments[a];return r=t.call.apply(t,[this].concat(i)),Ee(ot(r),"state",{uid:No()}),Ee(ot(r),"reqs",{}),Ee(ot(r),"fileInput",void 0),Ee(ot(r),"_isMounted",void 0),Ee(ot(r),"onChange",function(s){var l=r.props,c=l.accept,u=l.directory,f=s.target.files,m=He(f).filter(function(p){return!u||Ro(p,c)});r.uploadFiles(m),r.reset()}),Ee(ot(r),"onClick",function(s){var l=r.fileInput;if(l){var c=s.target,u=r.props.onClick;if(c&&c.tagName==="BUTTON"){var f=l.parentNode;f.focus(),c.blur()}l.click(),u&&u(s)}}),Ee(ot(r),"onKeyDown",function(s){s.key==="Enter"&&r.onClick(s)}),Ee(ot(r),"onDataTransferFiles",function(){var s=qt(at().mark(function l(c,u){var f,m,p,v,h,C,g;return at().wrap(function(w){for(;;)switch(w.prev=w.next){case 0:if(f=r.props,m=f.multiple,p=f.accept,v=f.directory,h=He(c.items||[]),C=He(c.files||[]),(C.length>0||h.some(function(E){return E.kind==="file"}))&&(u==null||u()),!v){w.next=11;break}return w.next=7,sy(Array.prototype.slice.call(h),function(E){return Ro(E,r.props.accept)});case 7:C=w.sent,r.uploadFiles(C),w.next=14;break;case 11:g=He(C).filter(function(E){return Ro(E,p)}),m===!1&&(g=C.slice(0,1)),r.uploadFiles(g);case 14:case"end":return w.stop()}},l)}));return function(l,c){return s.apply(this,arguments)}}()),Ee(ot(r),"onFilePaste",function(){var s=qt(at().mark(function l(c){var u,f;return at().wrap(function(p){for(;;)switch(p.prev=p.next){case 0:if(u=r.props.pastable,u){p.next=3;break}return p.abrupt("return");case 3:if(c.type!=="paste"){p.next=6;break}return f=c.clipboardData,p.abrupt("return",r.onDataTransferFiles(f,function(){c.preventDefault()}));case 6:case"end":return p.stop()}},l)}));return function(l){return s.apply(this,arguments)}}()),Ee(ot(r),"onFileDragOver",function(s){s.preventDefault()}),Ee(ot(r),"onFileDrop",function(){var s=qt(at().mark(function l(c){var u;return at().wrap(function(m){for(;;)switch(m.prev=m.next){case 0:if(c.preventDefault(),c.type!=="drop"){m.next=4;break}return u=c.dataTransfer,m.abrupt("return",r.onDataTransferFiles(u));case 4:case"end":return m.stop()}},l)}));return function(l){return s.apply(this,arguments)}}()),Ee(ot(r),"uploadFiles",function(s){var l=He(s),c=l.map(function(u){return u.uid=No(),r.processFile(u,l)});Promise.all(c).then(function(u){var f=r.props.onBatchStart;f==null||f(u.map(function(m){var p=m.origin,v=m.parsedFile;return{file:p,parsedFile:v}})),u.filter(function(m){return m.parsedFile!==null}).forEach(function(m){r.post(m)})})}),Ee(ot(r),"processFile",function(){var s=qt(at().mark(function l(c,u){var f,m,p,v,h,C,g,b,w;return at().wrap(function(y){for(;;)switch(y.prev=y.next){case 0:if(f=r.props.beforeUpload,m=c,!f){y.next=14;break}return y.prev=3,y.next=6,f(c,u);case 6:m=y.sent,y.next=12;break;case 9:y.prev=9,y.t0=y.catch(3),m=!1;case 12:if(m!==!1){y.next=14;break}return y.abrupt("return",{origin:c,parsedFile:null,action:null,data:null});case 14:if(p=r.props.action,typeof p!="function"){y.next=21;break}return y.next=18,p(c);case 18:v=y.sent,y.next=22;break;case 21:v=p;case 22:if(h=r.props.data,typeof h!="function"){y.next=29;break}return y.next=26,h(c);case 26:C=y.sent,y.next=30;break;case 29:C=h;case 30:return g=(et(m)==="object"||typeof m=="string")&&m?m:c,g instanceof File?b=g:b=new File([g],c.name,{type:c.type}),w=b,w.uid=c.uid,y.abrupt("return",{origin:c,data:C,parsedFile:w,action:v});case 35:case"end":return y.stop()}},l,null,[[3,9]])}));return function(l,c){return s.apply(this,arguments)}}()),Ee(ot(r),"saveFileInput",function(s){r.fileInput=s}),r}return Di(n,[{key:"componentDidMount",value:function(){this._isMounted=!0;var o=this.props.pastable;o&&document.addEventListener("paste",this.onFilePaste)}},{key:"componentWillUnmount",value:function(){this._isMounted=!1,this.abort(),document.removeEventListener("paste",this.onFilePaste)}},{key:"componentDidUpdate",value:function(o){var i=this.props.pastable;i&&!o.pastable?document.addEventListener("paste",this.onFilePaste):!i&&o.pastable&&document.removeEventListener("paste",this.onFilePaste)}},{key:"post",value:function(o){var i=this,a=o.data,s=o.origin,l=o.action,c=o.parsedFile;if(this._isMounted){var u=this.props,f=u.onStart,m=u.customRequest,p=u.name,v=u.headers,h=u.withCredentials,C=u.method,g=s.uid,b=m||ay,w={action:l,filename:p,data:a,file:c,headers:v,withCredentials:h,method:C||"post",onProgress:function(y){var x=i.props.onProgress;x==null||x(y,c)},onSuccess:function(y,x){var I=i.props.onSuccess;I==null||I(y,c,x),delete i.reqs[g]},onError:function(y,x){var I=i.props.onError;I==null||I(y,x,c),delete i.reqs[g]}};f(s),this.reqs[g]=b(w)}}},{key:"reset",value:function(){this.setState({uid:No()})}},{key:"abort",value:function(o){var i=this.reqs;if(o){var a=o.uid?o.uid:o;i[a]&&i[a].abort&&i[a].abort(),delete i[a]}else Object.keys(i).forEach(function(s){i[s]&&i[s].abort&&i[s].abort(),delete i[s]})}},{key:"render",value:function(){var o=this.props,i=o.component,a=o.prefixCls,s=o.className,l=o.classNames,c=l===void 0?{}:l,u=o.disabled,f=o.id,m=o.name,p=o.style,v=o.styles,h=v===void 0?{}:v,C=o.multiple,g=o.accept,b=o.capture,w=o.children,E=o.directory,y=o.openFileDialogOnClick,x=o.onMouseEnter,I=o.onMouseLeave,R=o.hasControlInside,k=st(o,uy),T=B(Ee(Ee(Ee({},a,!0),"".concat(a,"-disabled"),u),s,s)),O=E?{directory:"directory",webkitdirectory:"webkitdirectory"}:{},N=u?{}:{onClick:y?this.onClick:function(){},onKeyDown:y?this.onKeyDown:function(){},onMouseEnter:x,onMouseLeave:I,onDrop:this.onFileDrop,onDragOver:this.onFileDragOver,tabIndex:R?void 0:"0"};return P.createElement(i,ae({},N,{className:T,role:R?void 0:"button",style:p}),P.createElement("input",ae({},rn(k,{aria:!0,data:!0}),{id:f,name:m,disabled:u,type:"file",ref:this.saveFileInput,onClick:function(M){return M.stopPropagation()},key:this.state.uid,style:te({display:"none"},h.input),className:c.input,accept:g},O,{multiple:C,onChange:this.onChange},b!=null?{capture:b}:{})),w)}}]),n}(d.Component);function ko(){}var ui=function(e){ac(n,e);var t=sc(n);function n(){var r;zi(this,n);for(var o=arguments.length,i=new Array(o),a=0;a<o;a++)i[a]=arguments[a];return r=t.call.apply(t,[this].concat(i)),Ee(ot(r),"uploader",void 0),Ee(ot(r),"saveUploader",function(s){r.uploader=s}),r}return Di(n,[{key:"abort",value:function(o){this.uploader.abort(o)}},{key:"render",value:function(){return P.createElement(dy,ae({},this.props,{ref:this.saveUploader}))}}]),n}(d.Component);Ee(ui,"defaultProps",{component:"span",prefixCls:"rc-upload",data:{},headers:{},name:"file",multipart:!1,onStart:ko,onError:ko,onSuccess:ko,multiple:!1,beforeUpload:null,customRequest:null,withCredentials:!1,openFileDialogOnClick:!0,hasControlInside:!1});const fy=e=>{const{componentCls:t,iconCls:n}=e;return{[`${t}-wrapper`]:{[`${t}-drag`]:{position:"relative",width:"100%",height:"100%",textAlign:"center",background:e.colorFillAlter,border:`${H(e.lineWidth)} dashed ${e.colorBorder}`,borderRadius:e.borderRadiusLG,cursor:"pointer",transition:`border-color ${e.motionDurationSlow}`,[t]:{padding:e.padding},[`${t}-btn`]:{display:"table",width:"100%",height:"100%",outline:"none",borderRadius:e.borderRadiusLG,"&:focus-visible":{outline:`${H(e.lineWidthFocus)} solid ${e.colorPrimaryBorder}`}},[`${t}-drag-container`]:{display:"table-cell",verticalAlign:"middle"},[`
          &:not(${t}-disabled):hover,
          &-hover:not(${t}-disabled)
        `]:{borderColor:e.colorPrimaryHover},[`p${t}-drag-icon`]:{marginBottom:e.margin,[n]:{color:e.colorPrimary,fontSize:e.uploadThumbnailSize}},[`p${t}-text`]:{margin:`0 0 ${H(e.marginXXS)}`,color:e.colorTextHeading,fontSize:e.fontSizeLG},[`p${t}-hint`]:{color:e.colorTextDescription,fontSize:e.fontSize},[`&${t}-disabled`]:{[`p${t}-drag-icon ${n},
            p${t}-text,
            p${t}-hint
          `]:{color:e.colorTextDisabled}}}}}},py=e=>{const{componentCls:t,iconCls:n,fontSize:r,lineHeight:o,calc:i}=e,a=`${t}-list-item`,s=`${a}-actions`,l=`${a}-action`;return{[`${t}-wrapper`]:{[`${t}-list`]:Object.assign(Object.assign({},sn()),{lineHeight:e.lineHeight,[a]:{position:"relative",height:i(e.lineHeight).mul(r).equal(),marginTop:e.marginXS,fontSize:r,display:"flex",alignItems:"center",transition:`background-color ${e.motionDurationSlow}`,borderRadius:e.borderRadiusSM,"&:hover":{backgroundColor:e.controlItemBgHover},[`${a}-name`]:Object.assign(Object.assign({},In),{padding:`0 ${H(e.paddingXS)}`,lineHeight:o,flex:"auto",transition:`all ${e.motionDurationSlow}`}),[s]:{whiteSpace:"nowrap",[l]:{opacity:0},[n]:{color:e.actionsColor,transition:`all ${e.motionDurationSlow}`},[`
              ${l}:focus-visible,
              &.picture ${l}
            `]:{opacity:1}},[`${t}-icon ${n}`]:{color:e.colorIcon,fontSize:r},[`${a}-progress`]:{position:"absolute",bottom:e.calc(e.uploadProgressOffset).mul(-1).equal(),width:"100%",paddingInlineStart:i(r).add(e.paddingXS).equal(),fontSize:r,lineHeight:0,pointerEvents:"none","> div":{margin:0}}},[`${a}:hover ${l}`]:{opacity:1},[`${a}-error`]:{color:e.colorError,[`${a}-name, ${t}-icon ${n}`]:{color:e.colorError},[s]:{[`${n}, ${n}:hover`]:{color:e.colorError},[l]:{opacity:1}}},[`${t}-list-item-container`]:{transition:`opacity ${e.motionDurationSlow}, height ${e.motionDurationSlow}`,"&::before":{display:"table",width:0,height:0,content:'""'}}})}}},my=e=>{const{componentCls:t}=e,n=new It("uploadAnimateInlineIn",{from:{width:0,height:0,padding:0,opacity:0,margin:e.calc(e.marginXS).div(-2).equal()}}),r=new It("uploadAnimateInlineOut",{to:{width:0,height:0,padding:0,opacity:0,margin:e.calc(e.marginXS).div(-2).equal()}}),o=`${t}-animate-inline`;return[{[`${t}-wrapper`]:{[`${o}-appear, ${o}-enter, ${o}-leave`]:{animationDuration:e.motionDurationSlow,animationTimingFunction:e.motionEaseInOutCirc,animationFillMode:"forwards"},[`${o}-appear, ${o}-enter`]:{animationName:n},[`${o}-leave`]:{animationName:r}}},{[`${t}-wrapper`]:Bi(e)},n,r]},hy=e=>{const{componentCls:t,iconCls:n,uploadThumbnailSize:r,uploadProgressOffset:o,calc:i}=e,a=`${t}-list`,s=`${a}-item`;return{[`${t}-wrapper`]:{[`
        ${a}${a}-picture,
        ${a}${a}-picture-card,
        ${a}${a}-picture-circle
      `]:{[s]:{position:"relative",height:i(r).add(i(e.lineWidth).mul(2)).add(i(e.paddingXS).mul(2)).equal(),padding:e.paddingXS,border:`${H(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusLG,"&:hover":{background:"transparent"},[`${s}-thumbnail`]:Object.assign(Object.assign({},In),{width:r,height:r,lineHeight:H(i(r).add(e.paddingSM).equal()),textAlign:"center",flex:"none",[n]:{fontSize:e.fontSizeHeading2,color:e.colorPrimary},img:{display:"block",width:"100%",height:"100%",overflow:"hidden"}}),[`${s}-progress`]:{bottom:o,width:`calc(100% - ${H(i(e.paddingSM).mul(2).equal())})`,marginTop:0,paddingInlineStart:i(r).add(e.paddingXS).equal()}},[`${s}-error`]:{borderColor:e.colorError,[`${s}-thumbnail ${n}`]:{[`svg path[fill='${ja[0]}']`]:{fill:e.colorErrorBg},[`svg path[fill='${ja.primary}']`]:{fill:e.colorError}}},[`${s}-uploading`]:{borderStyle:"dashed",[`${s}-name`]:{marginBottom:o}}},[`${a}${a}-picture-circle ${s}`]:{[`&, &::before, ${s}-thumbnail`]:{borderRadius:"50%"}}}}},gy=e=>{const{componentCls:t,iconCls:n,fontSizeLG:r,colorTextLightSolid:o,calc:i}=e,a=`${t}-list`,s=`${a}-item`,l=e.uploadPicCardSize;return{[`
      ${t}-wrapper${t}-picture-card-wrapper,
      ${t}-wrapper${t}-picture-circle-wrapper
    `]:Object.assign(Object.assign({},sn()),{display:"block",[`${t}${t}-select`]:{width:l,height:l,textAlign:"center",verticalAlign:"top",backgroundColor:e.colorFillAlter,border:`${H(e.lineWidth)} dashed ${e.colorBorder}`,borderRadius:e.borderRadiusLG,cursor:"pointer",transition:`border-color ${e.motionDurationSlow}`,[`> ${t}`]:{display:"flex",alignItems:"center",justifyContent:"center",height:"100%",textAlign:"center"},[`&:not(${t}-disabled):hover`]:{borderColor:e.colorPrimary}},[`${a}${a}-picture-card, ${a}${a}-picture-circle`]:{display:"flex",flexWrap:"wrap","@supports not (gap: 1px)":{"& > *":{marginBlockEnd:e.marginXS,marginInlineEnd:e.marginXS}},"@supports (gap: 1px)":{gap:e.marginXS},[`${a}-item-container`]:{display:"inline-block",width:l,height:l,verticalAlign:"top"},"&::after":{display:"none"},"&::before":{display:"none"},[s]:{height:"100%",margin:0,"&::before":{position:"absolute",zIndex:1,width:`calc(100% - ${H(i(e.paddingXS).mul(2).equal())})`,height:`calc(100% - ${H(i(e.paddingXS).mul(2).equal())})`,backgroundColor:e.colorBgMask,opacity:0,transition:`all ${e.motionDurationSlow}`,content:'" "'}},[`${s}:hover`]:{[`&::before, ${s}-actions`]:{opacity:1}},[`${s}-actions`]:{position:"absolute",insetInlineStart:0,zIndex:10,width:"100%",whiteSpace:"nowrap",textAlign:"center",opacity:0,transition:`all ${e.motionDurationSlow}`,[`
            ${n}-eye,
            ${n}-download,
            ${n}-delete
          `]:{zIndex:10,width:r,margin:`0 ${H(e.marginXXS)}`,fontSize:r,cursor:"pointer",transition:`all ${e.motionDurationSlow}`,color:o,"&:hover":{color:o},svg:{verticalAlign:"baseline"}}},[`${s}-thumbnail, ${s}-thumbnail img`]:{position:"static",display:"block",width:"100%",height:"100%",objectFit:"contain"},[`${s}-name`]:{display:"none",textAlign:"center"},[`${s}-file + ${s}-name`]:{position:"absolute",bottom:e.margin,display:"block",width:`calc(100% - ${H(i(e.paddingXS).mul(2).equal())})`},[`${s}-uploading`]:{[`&${s}`]:{backgroundColor:e.colorFillAlter},[`&::before, ${n}-eye, ${n}-download, ${n}-delete`]:{display:"none"}},[`${s}-progress`]:{bottom:e.marginXL,width:`calc(100% - ${H(i(e.paddingXS).mul(2).equal())})`,paddingInlineStart:0}}}),[`${t}-wrapper${t}-picture-circle-wrapper`]:{[`${t}${t}-select`]:{borderRadius:"50%"}}}},vy=e=>{const{componentCls:t}=e;return{[`${t}-rtl`]:{direction:"rtl"}}},yy=e=>{const{componentCls:t,colorTextDisabled:n}=e;return{[`${t}-wrapper`]:Object.assign(Object.assign({},Ht(e)),{[t]:{outline:0,"input[type='file']":{cursor:"pointer"}},[`${t}-select`]:{display:"inline-block"},[`${t}-hidden`]:{display:"none"},[`${t}-disabled`]:{color:n,cursor:"not-allowed"}})}},by=e=>({actionsColor:e.colorIcon}),xy=xt("Upload",e=>{const{fontSizeHeading3:t,fontHeight:n,lineWidth:r,controlHeightLG:o,calc:i}=e,a=tt(e,{uploadThumbnailSize:i(t).mul(2).equal(),uploadProgressOffset:i(i(n).div(2)).add(r).equal(),uploadPicCardSize:i(o).mul(2.55).equal()});return[yy(a),fy(a),hy(a),gy(a),py(a),my(a),vy(a),Of(a)]},by);function $r(e){return Object.assign(Object.assign({},e),{lastModified:e.lastModified,lastModifiedDate:e.lastModifiedDate,name:e.name,size:e.size,type:e.type,uid:e.uid,percent:0,originFileObj:e})}function Er(e,t){const n=He(t),r=n.findIndex(({uid:o})=>o===e.uid);return r===-1?n.push(e):n[r]=e,n}function Mo(e,t){const n=e.uid!==void 0?"uid":"name";return t.filter(r=>r[n]===e[n])[0]}function Cy(e,t){const n=e.uid!==void 0?"uid":"name",r=t.filter(o=>o[n]!==e[n]);return r.length===t.length?null:r}const Sy=(e="")=>{const t=e.split("/"),r=t[t.length-1].split(/#|\?/)[0];return(/\.[^./\\]*$/.exec(r)||[""])[0]},hu=e=>e.indexOf("image/")===0,wy=e=>{if(e.type&&!e.thumbUrl)return hu(e.type);const t=e.thumbUrl||e.url||"",n=Sy(t);return/^data:image\//.test(t)||/(webp|svg|png|gif|jpg|jpeg|jfif|bmp|dpg|ico|heic|heif)$/i.test(n)?!0:!(/^data:/.test(t)||n)},Ut=200;function $y(e){return new Promise(t=>{if(!e.type||!hu(e.type)){t("");return}const n=document.createElement("canvas");n.width=Ut,n.height=Ut,n.style.cssText=`position: fixed; left: 0; top: 0; width: ${Ut}px; height: ${Ut}px; z-index: 9999; display: none;`,document.body.appendChild(n);const r=n.getContext("2d"),o=new Image;if(o.onload=()=>{const{width:i,height:a}=o;let s=Ut,l=Ut,c=0,u=0;i>a?(l=a*(Ut/i),u=-(l-s)/2):(s=i*(Ut/a),c=-(s-l)/2),r.drawImage(o,c,u,s,l);const f=n.toDataURL();document.body.removeChild(n),window.URL.revokeObjectURL(o.src),t(f)},o.crossOrigin="anonymous",e.type.startsWith("image/svg+xml")){const i=new FileReader;i.onload=()=>{i.result&&typeof i.result=="string"&&(o.src=i.result)},i.readAsDataURL(e)}else if(e.type.startsWith("image/gif")){const i=new FileReader;i.onload=()=>{i.result&&t(i.result)},i.readAsDataURL(e)}else o.src=window.URL.createObjectURL(e)})}const Ey=d.forwardRef(({prefixCls:e,className:t,style:n,locale:r,listType:o,file:i,items:a,progress:s,iconRender:l,actionIconRender:c,itemRender:u,isImgUrl:f,showPreviewIcon:m,showRemoveIcon:p,showDownloadIcon:v,previewIcon:h,removeIcon:C,downloadIcon:g,extra:b,onPreview:w,onDownload:E,onClose:y},x)=>{var I,R;const{status:k}=i,[T,O]=d.useState(k);d.useEffect(()=>{k!=="removed"&&O(k)},[k]);const[N,j]=d.useState(!1);d.useEffect(()=>{const ee=setTimeout(()=>{j(!0)},300);return()=>{clearTimeout(ee)}},[]);const M=l(i);let L=d.createElement("div",{className:`${e}-icon`},M);if(o==="picture"||o==="picture-card"||o==="picture-circle")if(T==="uploading"||!i.thumbUrl&&!i.url){const ee=B(`${e}-list-item-thumbnail`,{[`${e}-list-item-file`]:T!=="uploading"});L=d.createElement("div",{className:ee},M)}else{const ee=f!=null&&f(i)?d.createElement("img",{src:i.thumbUrl||i.url,alt:i.name,className:`${e}-list-item-image`,crossOrigin:i.crossOrigin}):M,de=B(`${e}-list-item-thumbnail`,{[`${e}-list-item-file`]:f&&!f(i)});L=d.createElement("a",{className:de,onClick:oe=>w(i,oe),href:i.url||i.thumbUrl,target:"_blank",rel:"noopener noreferrer"},ee)}const z=B(`${e}-list-item`,`${e}-list-item-${T}`),D=typeof i.linkProps=="string"?JSON.parse(i.linkProps):i.linkProps,W=(typeof p=="function"?p(i):p)?c((typeof C=="function"?C(i):C)||d.createElement(lc,null),()=>y(i),e,r.removeFile,!0):null,F=(typeof v=="function"?v(i):v)&&T==="done"?c((typeof g=="function"?g(i):g)||d.createElement(bp,null),()=>E(i),e,r.downloadFile):null,$=o!=="picture-card"&&o!=="picture-circle"&&d.createElement("span",{key:"download-delete",className:B(`${e}-list-item-actions`,{picture:o==="picture"})},F,W),G=typeof b=="function"?b(i):b,q=G&&d.createElement("span",{className:`${e}-list-item-extra`},G),S=B(`${e}-list-item-name`),Y=i.url?d.createElement("a",Object.assign({key:"view",target:"_blank",rel:"noopener noreferrer",className:S,title:i.name},D,{href:i.url,onClick:ee=>w(i,ee)}),i.name,q):d.createElement("span",{key:"view",className:S,onClick:ee=>w(i,ee),title:i.name},i.name,q),ne=(typeof m=="function"?m(i):m)&&(i.url||i.thumbUrl)?d.createElement("a",{href:i.url||i.thumbUrl,target:"_blank",rel:"noopener noreferrer",onClick:ee=>w(i,ee),title:r.previewFile},typeof h=="function"?h(i):h||d.createElement(ec,null)):null,K=(o==="picture-card"||o==="picture-circle")&&T!=="uploading"&&d.createElement("span",{className:`${e}-list-item-actions`},ne,T==="done"&&F,W),{getPrefixCls:ue}=d.useContext(Ve),ce=ue(),me=d.createElement("div",{className:z},L,Y,$,K,N&&d.createElement(on,{motionName:`${ce}-fade`,visible:T==="uploading",motionDeadline:2e3},({className:ee})=>{const de="percent"in i?d.createElement(fu,Object.assign({type:"line",percent:i.percent,"aria-label":i["aria-label"],"aria-labelledby":i["aria-labelledby"]},s)):null;return d.createElement("div",{className:B(`${e}-list-item-progress`,ee)},de)})),fe=i.response&&typeof i.response=="string"?i.response:((I=i.error)===null||I===void 0?void 0:I.statusText)||((R=i.error)===null||R===void 0?void 0:R.message)||r.uploadError,se=T==="error"?d.createElement(ln,{title:fe,getPopupContainer:ee=>ee.parentNode},me):me;return d.createElement("div",{className:B(`${e}-list-item-container`,t),style:n,ref:x},u?u(se,i,a,{download:E.bind(null,i),preview:w.bind(null,i),remove:y.bind(null,i)}):se)}),Iy=(e,t)=>{const{listType:n="text",previewFile:r=$y,onPreview:o,onDownload:i,onRemove:a,locale:s,iconRender:l,isImageUrl:c=wy,prefixCls:u,items:f=[],showPreviewIcon:m=!0,showRemoveIcon:p=!0,showDownloadIcon:v=!1,removeIcon:h,previewIcon:C,downloadIcon:g,extra:b,progress:w={size:[-1,2],showInfo:!1},appendAction:E,appendActionVisible:y=!0,itemRender:x,disabled:I}=e,R=Rf(),[k,T]=d.useState(!1),O=["picture-card","picture-circle"].includes(n);d.useEffect(()=>{n.startsWith("picture")&&(f||[]).forEach(S=>{!(S.originFileObj instanceof File||S.originFileObj instanceof Blob)||S.thumbUrl!==void 0||(S.thumbUrl="",r==null||r(S.originFileObj).then(Y=>{S.thumbUrl=Y||"",R()}))})},[n,f,r]),d.useEffect(()=>{T(!0)},[]);const N=(S,Y)=>{if(o)return Y==null||Y.preventDefault(),o(S)},j=S=>{typeof i=="function"?i(S):S.url&&window.open(S.url)},M=S=>{a==null||a(S)},L=S=>{if(l)return l(S,n);const Y=S.status==="uploading";if(n.startsWith("picture")){const ne=n==="picture"?d.createElement(oi,null):s.uploading,K=c!=null&&c(S)?d.createElement(lm,null):d.createElement(Xp,null);return Y?ne:K}return Y?d.createElement(oi,null):d.createElement(pc,null)},z=(S,Y,ne,K,ue)=>{const ce={type:"text",size:"small",title:K,onClick:me=>{var fe,se;Y(),d.isValidElement(S)&&((se=(fe=S.props).onClick)===null||se===void 0||se.call(fe,me))},className:`${ne}-list-item-action`,disabled:ue?I:!1};return d.isValidElement(S)?d.createElement(qe,Object.assign({},ce,{icon:Zn(S,Object.assign(Object.assign({},S.props),{onClick:()=>{}}))})):d.createElement(qe,Object.assign({},ce),d.createElement("span",null,S))};d.useImperativeHandle(t,()=>({handlePreview:N,handleDownload:j}));const{getPrefixCls:D}=d.useContext(Ve),W=D("upload",u),F=D(),$=B(`${W}-list`,`${W}-list-${n}`),G=d.useMemo(()=>Pn(Nf(F),["onAppearEnd","onEnterEnd","onLeaveEnd"]),[F]),q=Object.assign(Object.assign({},O?{}:G),{motionDeadline:2e3,motionName:`${W}-${O?"animate-inline":"animate"}`,keys:He(f.map(S=>({key:S.uid,file:S}))),motionAppear:k});return d.createElement("div",{className:$},d.createElement(cc,Object.assign({},q,{component:!1}),({key:S,file:Y,className:ne,style:K})=>d.createElement(Ey,{key:S,locale:s,prefixCls:W,className:ne,style:K,file:Y,items:f,progress:w,listType:n,isImgUrl:c,showPreviewIcon:m,showRemoveIcon:p,showDownloadIcon:v,removeIcon:h,previewIcon:C,downloadIcon:g,extra:b,iconRender:L,actionIconRender:z,itemRender:x,onPreview:N,onDownload:j,onClose:M})),E&&d.createElement(on,Object.assign({},q,{visible:y,forceRender:!0}),({className:S,style:Y})=>Zn(E,ne=>({className:B(ne.className,S),style:Object.assign(Object.assign(Object.assign({},Y),{pointerEvents:S?"none":void 0}),ne.style)}))))},Py=d.forwardRef(Iy);var Ty=function(e,t,n,r){function o(i){return i instanceof n?i:new n(function(a){a(i)})}return new(n||(n=Promise))(function(i,a){function s(u){try{c(r.next(u))}catch(f){a(f)}}function l(u){try{c(r.throw(u))}catch(f){a(f)}}function c(u){u.done?i(u.value):o(u.value).then(s,l)}c((r=r.apply(e,[])).next())})};const Vn=`__LIST_IGNORE_${Date.now()}__`,Oy=(e,t)=>{const{fileList:n,defaultFileList:r,onRemove:o,showUploadList:i=!0,listType:a="text",onPreview:s,onDownload:l,onChange:c,onDrop:u,previewFile:f,disabled:m,locale:p,iconRender:v,isImageUrl:h,progress:C,prefixCls:g,className:b,type:w="select",children:E,style:y,itemRender:x,maxCount:I,data:R={},multiple:k=!1,hasControlInside:T=!0,action:O="",accept:N="",supportServerRender:j=!0,rootClassName:M}=e,L=d.useContext(kf),z=m??L,[D,W]=bt(r||[],{value:n,postState:Z=>Z??[]}),[F,$]=d.useState("drop"),G=d.useRef(null),q=d.useRef(null);d.useMemo(()=>{const Z=Date.now();(n||[]).forEach((pe,X)=>{!pe.uid&&!Object.isFrozen(pe)&&(pe.uid=`__AUTO__${Z}_${X}__`)})},[n]);const S=(Z,pe,X)=>{let J=He(pe),ge=!1;I===1?J=J.slice(-1):I&&(ge=J.length>I,J=J.slice(0,I)),ti.flushSync(()=>{W(J)});const Te={file:Z,fileList:J};X&&(Te.event=X),(!ge||Z.status==="removed"||J.some(We=>We.uid===Z.uid))&&ti.flushSync(()=>{c==null||c(Te)})},Y=(Z,pe)=>Ty(void 0,void 0,void 0,function*(){const{beforeUpload:X,transformFile:J}=e;let ge=Z;if(X){const Te=yield X(Z,pe);if(Te===!1)return!1;if(delete Z[Vn],Te===Vn)return Object.defineProperty(Z,Vn,{value:!0,configurable:!0}),!1;typeof Te=="object"&&Te&&(ge=Te)}return J&&(ge=yield J(ge)),ge}),ne=Z=>{const pe=Z.filter(ge=>!ge.file[Vn]);if(!pe.length)return;const X=pe.map(ge=>$r(ge.file));let J=He(D);X.forEach(ge=>{J=Er(ge,J)}),X.forEach((ge,Te)=>{let We=ge;if(pe[Te].parsedFile)ge.status="uploading";else{const{originFileObj:nt}=ge;let mt;try{mt=new File([nt],nt.name,{type:nt.type})}catch{mt=new Blob([nt],{type:nt.type}),mt.name=nt.name,mt.lastModifiedDate=new Date,mt.lastModified=new Date().getTime()}mt.uid=ge.uid,We=mt}S(We,J)})},K=(Z,pe,X)=>{try{typeof Z=="string"&&(Z=JSON.parse(Z))}catch{}if(!Mo(pe,D))return;const J=$r(pe);J.status="done",J.percent=100,J.response=Z,J.xhr=X;const ge=Er(J,D);S(J,ge)},ue=(Z,pe)=>{if(!Mo(pe,D))return;const X=$r(pe);X.status="uploading",X.percent=Z.percent;const J=Er(X,D);S(X,J,Z)},ce=(Z,pe,X)=>{if(!Mo(X,D))return;const J=$r(X);J.error=Z,J.response=pe,J.status="error";const ge=Er(J,D);S(J,ge)},me=Z=>{let pe;Promise.resolve(typeof o=="function"?o(Z):o).then(X=>{var J;if(X===!1)return;const ge=Cy(Z,D);ge&&(pe=Object.assign(Object.assign({},Z),{status:"removed"}),D==null||D.forEach(Te=>{const We=pe.uid!==void 0?"uid":"name";Te[We]===pe[We]&&!Object.isFrozen(Te)&&(Te.status="removed")}),(J=G.current)===null||J===void 0||J.abort(pe),S(pe,ge))})},fe=Z=>{$(Z.type),Z.type==="drop"&&(u==null||u(Z))};d.useImperativeHandle(t,()=>({onBatchStart:ne,onSuccess:K,onProgress:ue,onError:ce,fileList:D,upload:G.current,nativeElement:q.current}));const{getPrefixCls:se,direction:ee,upload:de}=d.useContext(Ve),oe=se("upload",g),ie=Object.assign(Object.assign({onBatchStart:ne,onError:ce,onProgress:ue,onSuccess:K},e),{data:R,multiple:k,action:O,accept:N,supportServerRender:j,prefixCls:oe,disabled:z,beforeUpload:Y,onChange:void 0,hasControlInside:T});delete ie.className,delete ie.style,(!E||z)&&delete ie.id;const be=`${oe}-wrapper`,[Ie,ye,Oe]=xy(oe,be),[xe]=$n("Upload",Kl.Upload),{showRemoveIcon:$e,showPreviewIcon:Ae,showDownloadIcon:Re,removeIcon:Ue,previewIcon:Ke,downloadIcon:_,extra:U}=typeof i=="boolean"?{}:i,le=typeof $e>"u"?!z:$e,he=(Z,pe)=>i?d.createElement(Py,{prefixCls:oe,listType:a,items:D,previewFile:f,onPreview:s,onDownload:l,onRemove:me,showRemoveIcon:le,showPreviewIcon:Ae,showDownloadIcon:Re,removeIcon:Ue,previewIcon:Ke,downloadIcon:_,iconRender:v,extra:U,locale:Object.assign(Object.assign({},xe),p),isImageUrl:h,progress:C,appendAction:Z,appendActionVisible:pe,itemRender:x,disabled:z}):Z,Ce=B(be,b,M,ye,Oe,de==null?void 0:de.className,{[`${oe}-rtl`]:ee==="rtl",[`${oe}-picture-card-wrapper`]:a==="picture-card",[`${oe}-picture-circle-wrapper`]:a==="picture-circle"}),ze=Object.assign(Object.assign({},de==null?void 0:de.style),y);if(w==="drag"){const Z=B(ye,oe,`${oe}-drag`,{[`${oe}-drag-uploading`]:D.some(pe=>pe.status==="uploading"),[`${oe}-drag-hover`]:F==="dragover",[`${oe}-disabled`]:z,[`${oe}-rtl`]:ee==="rtl"});return Ie(d.createElement("span",{className:Ce,ref:q},d.createElement("div",{className:Z,style:ze,onDrop:fe,onDragOver:fe,onDragLeave:fe},d.createElement(ui,Object.assign({},ie,{ref:G,className:`${oe}-btn`}),d.createElement("div",{className:`${oe}-drag-container`},E))),he()))}const V=B(oe,`${oe}-select`,{[`${oe}-disabled`]:z,[`${oe}-hidden`]:!E}),re=d.createElement("div",{className:V,style:ze},d.createElement(ui,Object.assign({},ie,{ref:G})));return Ie(a==="picture-card"||a==="picture-circle"?d.createElement("span",{className:Ce,ref:q},he(re,!!E)):d.createElement("span",{className:Ce,ref:q},re,he()))},gu=d.forwardRef(Oy);var Ry=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const Ny=d.forwardRef((e,t)=>{var{style:n,height:r,hasControlInside:o=!1}=e,i=Ry(e,["style","height","hasControlInside"]);return d.createElement(gu,Object.assign({ref:t,hasControlInside:o},i,{type:"drag",style:Object.assign(Object.assign({},n),{height:r})}))}),io=gu;io.Dragger=Ny;io.LIST_IGNORE=Vn;const ky=P.createContext({}),My={classNames:{},styles:{},className:"",style:{}},lr=e=>{const t=P.useContext(ky);return P.useMemo(()=>({...My,...t[e]}),[t[e]])};function Ft(){const{getPrefixCls:e,direction:t,csp:n,iconPrefixCls:r,theme:o}=P.useContext(En.ConfigContext);return{theme:o,getPrefixCls:e,direction:t,csp:n,iconPrefixCls:r}}const jy=tc(nn.defaultAlgorithm),Ly={screenXS:!0,screenXSMin:!0,screenXSMax:!0,screenSM:!0,screenSMMin:!0,screenSMMax:!0,screenMD:!0,screenMDMin:!0,screenMDMax:!0,screenLG:!0,screenLGMin:!0,screenLGMax:!0,screenXL:!0,screenXLMin:!0,screenXLMax:!0,screenXXL:!0,screenXXLMin:!0},vu=(e,t,n)=>{const r=n.getDerivativeToken(e),{override:o,...i}=t;let a={...r,override:o};return a=nc(a),i&&Object.entries(i).forEach(([s,l])=>{const{theme:c,...u}=l;let f=u;c&&(f=vu({...a,...u},{override:u},c)),a[s]=f}),a};function _y(){const{token:e,hashed:t,theme:n=jy,override:r,cssVar:o}=P.useContext(nn._internalContext),[i,a,s]=Mf(n,[nn.defaultSeed,e],{salt:`${Rm}-${t||""}`,override:r,getComputedToken:vu,cssVar:o&&{prefix:o.prefix,key:o.key,unitless:Lf,ignore:jf,preserve:Ly}});return[n,s,t?a:"",i,o]}const{genStyleHooks:cr}=_f({usePrefix:()=>{const{getPrefixCls:e,iconPrefixCls:t}=Ft();return{iconPrefixCls:t,rootPrefixCls:e()}},useToken:()=>{const[e,t,n,r,o]=_y();return{theme:e,realToken:t,hashId:n,token:r,cssVar:o}},useCSP:()=>{const{csp:e}=Ft();return e??{}},layer:{name:"antdx",dependencies:["antd"]}}),ur=P.createContext(null);function ws(e){const{getDropContainer:t,className:n,prefixCls:r,children:o}=e,{disabled:i}=P.useContext(ur),[a,s]=P.useState(),[l,c]=P.useState(null);if(P.useEffect(()=>{const m=t==null?void 0:t();a!==m&&s(m)},[t]),P.useEffect(()=>{if(a){const m=()=>{c(!0)},p=C=>{C.preventDefault()},v=C=>{C.relatedTarget||c(!1)},h=C=>{c(!1),C.preventDefault()};return document.addEventListener("dragenter",m),document.addEventListener("dragover",p),document.addEventListener("dragleave",v),document.addEventListener("drop",h),()=>{document.removeEventListener("dragenter",m),document.removeEventListener("dragover",p),document.removeEventListener("dragleave",v),document.removeEventListener("drop",h)}}},[!!a]),!(t&&a&&!i))return null;const f=`${r}-drop-area`;return ti.createPortal(P.createElement("div",{className:B(f,n,{[`${f}-on-body`]:a.tagName==="BODY"}),style:{display:l?"block":"none"}},o),a)}function Ay(e,t){const{children:n,upload:r,rootClassName:o}=e,i=P.useRef(null);return P.useImperativeHandle(t,()=>i.current),P.createElement(io,ae({},r,{showUploadList:!1,rootClassName:o,ref:i}),n)}const yu=P.forwardRef(Ay),zy=e=>{const{componentCls:t,antCls:n,calc:r}=e,o=`${t}-list-card`,i=r(e.fontSize).mul(e.lineHeight).mul(2).add(e.paddingSM).add(e.paddingSM).equal();return{[o]:{borderRadius:e.borderRadius,position:"relative",background:e.colorFillContent,borderWidth:e.lineWidth,borderStyle:"solid",borderColor:"transparent",flex:"none",[`${o}-name,${o}-desc`]:{display:"flex",flexWrap:"nowrap",maxWidth:"100%"},[`${o}-ellipsis-prefix`]:{flex:"0 1 auto",minWidth:0,overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},[`${o}-ellipsis-suffix`]:{flex:"none"},"&-type-overview":{padding:r(e.paddingSM).sub(e.lineWidth).equal(),paddingInlineStart:r(e.padding).add(e.lineWidth).equal(),display:"flex",flexWrap:"nowrap",gap:e.paddingXS,alignItems:"flex-start",width:236,[`${o}-icon`]:{fontSize:r(e.fontSizeLG).mul(2).equal(),lineHeight:1,paddingTop:r(e.paddingXXS).mul(1.5).equal(),flex:"none"},[`${o}-content`]:{flex:"auto",minWidth:0,display:"flex",flexDirection:"column",alignItems:"stretch"},[`${o}-desc`]:{color:e.colorTextTertiary}},"&-type-preview":{width:i,height:i,lineHeight:1,display:"flex",alignItems:"center",[`&:not(${o}-status-error)`]:{border:0},[`${n}-image`]:{width:"100%",height:"100%",borderRadius:"inherit",img:{height:"100%",objectFit:"cover",borderRadius:"inherit"}},[`${o}-img-mask`]:{position:"absolute",inset:0,display:"flex",justifyContent:"center",alignItems:"center",background:`rgba(0, 0, 0, ${e.opacityLoading})`,borderRadius:"inherit"},[`&${o}-status-error`]:{[`img, ${o}-img-mask`]:{borderRadius:r(e.borderRadius).sub(e.lineWidth).equal()},[`${o}-desc`]:{paddingInline:e.paddingXXS}},[`${o}-progress`]:{}},[`${o}-remove`]:{position:"absolute",top:0,insetInlineEnd:0,border:0,padding:e.paddingXXS,background:"transparent",lineHeight:1,transform:"translate(50%, -50%)",fontSize:e.fontSize,cursor:"pointer",opacity:e.opacityLoading,display:"none","&:dir(rtl)":{transform:"translate(-50%, -50%)"},"&:hover":{opacity:1},"&:active":{opacity:e.opacityLoading}},[`&:hover ${o}-remove`]:{display:"block"},"&-status-error":{borderColor:e.colorError,[`${o}-desc`]:{color:e.colorError}},"&-motion":{transition:["opacity","width","margin","padding"].map(a=>`${a} ${e.motionDurationSlow}`).join(","),"&-appear-start":{width:0,transition:"none"},"&-leave-active":{opacity:0,width:0,paddingInline:0,borderInlineWidth:0,marginInlineEnd:r(e.paddingSM).mul(-1).equal()}}}}},di={"&, *":{boxSizing:"border-box"}},Dy=e=>{const{componentCls:t,calc:n,antCls:r}=e,o=`${t}-drop-area`,i=`${t}-placeholder`;return{[o]:{position:"absolute",inset:0,zIndex:e.zIndexPopupBase,...di,"&-on-body":{position:"fixed",inset:0},"&-hide-placement":{[`${i}-inner`]:{display:"none"}},[i]:{padding:0}},"&":{[i]:{height:"100%",borderRadius:e.borderRadius,borderWidth:e.lineWidthBold,borderStyle:"dashed",borderColor:"transparent",padding:e.padding,position:"relative",backdropFilter:"blur(10px)",background:e.colorBgPlaceholderHover,...di,[`${r}-upload-wrapper ${r}-upload${r}-upload-btn`]:{padding:0},[`&${i}-drag-in`]:{borderColor:e.colorPrimaryHover},[`&${i}-disabled`]:{opacity:.25,pointerEvents:"none"},[`${i}-inner`]:{gap:n(e.paddingXXS).div(2).equal()},[`${i}-icon`]:{fontSize:e.fontSizeHeading2,lineHeight:1},[`${i}-title${i}-title`]:{margin:0,fontSize:e.fontSize,lineHeight:e.lineHeight},[`${i}-description`]:{}}}}},By=e=>{const{componentCls:t,calc:n}=e,r=`${t}-list`,o=n(e.fontSize).mul(e.lineHeight).mul(2).add(e.paddingSM).add(e.paddingSM).equal();return{[t]:{position:"relative",width:"100%",...di,[r]:{display:"flex",flexWrap:"wrap",gap:e.paddingSM,fontSize:e.fontSize,lineHeight:e.lineHeight,color:e.colorText,paddingBlock:e.paddingSM,paddingInline:e.padding,width:"100%",background:e.colorBgContainer,scrollbarWidth:"none","-ms-overflow-style":"none","&::-webkit-scrollbar":{display:"none"},"&-overflow-scrollX, &-overflow-scrollY":{"&:before, &:after":{content:'""',position:"absolute",opacity:0,transition:`opacity ${e.motionDurationSlow}`,zIndex:1}},"&-overflow-ping-start:before":{opacity:1},"&-overflow-ping-end:after":{opacity:1},"&-overflow-scrollX":{overflowX:"auto",overflowY:"hidden",flexWrap:"nowrap","&:before, &:after":{insetBlock:0,width:8},"&:before":{insetInlineStart:0,background:"linear-gradient(to right, rgba(0,0,0,0.06), rgba(0,0,0,0));"},"&:after":{insetInlineEnd:0,background:"linear-gradient(to left, rgba(0,0,0,0.06), rgba(0,0,0,0));"},"&:dir(rtl)":{"&:before":{background:"linear-gradient(to left, rgba(0,0,0,0.06), rgba(0,0,0,0));"},"&:after":{background:"linear-gradient(to right, rgba(0,0,0,0.06), rgba(0,0,0,0));"}}},"&-overflow-scrollY":{overflowX:"hidden",overflowY:"auto",maxHeight:n(o).mul(3).equal(),"&:before, &:after":{insetInline:0,height:8},"&:before":{insetBlockStart:0,background:"linear-gradient(to bottom, rgba(0,0,0,0.06), rgba(0,0,0,0));"},"&:after":{insetBlockEnd:0,background:"linear-gradient(to top, rgba(0,0,0,0.06), rgba(0,0,0,0));"}},"&-upload-btn":{width:o,height:o,fontSize:e.fontSizeHeading2,color:"#999"},"&-prev-btn, &-next-btn":{position:"absolute",top:"50%",transform:"translateY(-50%)",boxShadow:e.boxShadowTertiary,opacity:0,pointerEvents:"none"},"&-prev-btn":{left:{_skip_check_:!0,value:e.padding}},"&-next-btn":{right:{_skip_check_:!0,value:e.padding}},"&:dir(ltr)":{[`&${r}-overflow-ping-start ${r}-prev-btn`]:{opacity:1,pointerEvents:"auto"},[`&${r}-overflow-ping-end ${r}-next-btn`]:{opacity:1,pointerEvents:"auto"}},"&:dir(rtl)":{[`&${r}-overflow-ping-end ${r}-prev-btn`]:{opacity:1,pointerEvents:"auto"},[`&${r}-overflow-ping-start ${r}-next-btn`]:{opacity:1,pointerEvents:"auto"}}}}}},Fy=e=>{const{colorBgContainer:t}=e;return{colorBgPlaceholderHover:new Rt(t).setA(.85).toRgbString()}},bu=cr("Attachments",e=>{const t=tt(e,{});return[Dy(t),By(t),zy(t)]},Fy),Hy=e=>e.indexOf("image/")===0,Ir=200;function Wy(e){return new Promise(t=>{if(!e||!e.type||!Hy(e.type)){t("");return}const n=new Image;if(n.onload=()=>{const{width:r,height:o}=n,i=r/o,a=i>1?Ir:Ir*i,s=i>1?Ir/i:Ir,l=document.createElement("canvas");l.width=a,l.height=s,l.style.cssText=`position: fixed; left: 0; top: 0; width: ${a}px; height: ${s}px; z-index: 9999; display: none;`,document.body.appendChild(l),l.getContext("2d").drawImage(n,0,0,a,s);const u=l.toDataURL();document.body.removeChild(l),window.URL.revokeObjectURL(n.src),t(u)},n.crossOrigin="anonymous",e.type.startsWith("image/svg+xml")){const r=new FileReader;r.onload=()=>{r.result&&typeof r.result=="string"&&(n.src=r.result)},r.readAsDataURL(e)}else if(e.type.startsWith("image/gif")){const r=new FileReader;r.onload=()=>{r.result&&t(r.result)},r.readAsDataURL(e)}else n.src=window.URL.createObjectURL(e)})}function Vy(){return P.createElement("svg",{width:"1em",height:"1em",viewBox:"0 0 16 16",version:"1.1",xmlns:"http://www.w3.org/2000/svg"},P.createElement("title",null,"audio"),P.createElement("g",{stroke:"none",strokeWidth:"1",fill:"none",fillRule:"evenodd"},P.createElement("path",{d:"M14.1178571,4.0125 C14.225,4.11964286 14.2857143,4.26428571 14.2857143,4.41607143 L14.2857143,15.4285714 C14.2857143,15.7446429 14.0303571,16 13.7142857,16 L2.28571429,16 C1.96964286,16 1.71428571,15.7446429 1.71428571,15.4285714 L1.71428571,0.571428571 C1.71428571,0.255357143 1.96964286,0 2.28571429,0 L9.86964286,0 C10.0214286,0 10.1678571,0.0607142857 10.275,0.167857143 L14.1178571,4.0125 Z M10.7315824,7.11216117 C10.7428131,7.15148751 10.7485063,7.19218979 10.7485063,7.23309113 L10.7485063,8.07742614 C10.7484199,8.27364959 10.6183424,8.44607275 10.4296853,8.50003683 L8.32984514,9.09986306 L8.32984514,11.7071803 C8.32986605,12.5367078 7.67249692,13.217028 6.84345686,13.2454634 L6.79068592,13.2463395 C6.12766108,13.2463395 5.53916361,12.8217001 5.33010655,12.1924966 C5.1210495,11.563293 5.33842118,10.8709227 5.86959669,10.4741173 C6.40077221,10.0773119 7.12636292,10.0652587 7.67042486,10.4442027 L7.67020842,7.74937024 L7.68449368,7.74937024 C7.72405122,7.59919041 7.83988806,7.48101083 7.98924584,7.4384546 L10.1880418,6.81004755 C10.42156,6.74340323 10.6648954,6.87865515 10.7315824,7.11216117 Z M9.60714286,1.31785714 L12.9678571,4.67857143 L9.60714286,4.67857143 L9.60714286,1.31785714 Z",fill:"currentColor"})))}function Uy(e){const{percent:t}=e,{token:n}=nn.useToken();return P.createElement(fu,{type:"circle",percent:t,size:n.fontSizeHeading2*2,strokeColor:"#FFF",trailColor:"rgba(255, 255, 255, 0.3)",format:r=>P.createElement("span",{style:{color:"#FFF"}},(r||0).toFixed(0),"%")})}function Xy(){return P.createElement("svg",{width:"1em",height:"1em",viewBox:"0 0 16 16",version:"1.1",xmlns:"http://www.w3.org/2000/svg"},P.createElement("title",null,"video"),P.createElement("g",{stroke:"none",strokeWidth:"1",fill:"none",fillRule:"evenodd"},P.createElement("path",{d:"M14.1178571,4.0125 C14.225,4.11964286 14.2857143,4.26428571 14.2857143,4.41607143 L14.2857143,15.4285714 C14.2857143,15.7446429 14.0303571,16 13.7142857,16 L2.28571429,16 C1.96964286,16 1.71428571,15.7446429 1.71428571,15.4285714 L1.71428571,0.571428571 C1.71428571,0.255357143 1.96964286,0 2.28571429,0 L9.86964286,0 C10.0214286,0 10.1678571,0.0607142857 10.275,0.167857143 L14.1178571,4.0125 Z M12.9678571,4.67857143 L9.60714286,1.31785714 L9.60714286,4.67857143 L12.9678571,4.67857143 Z M10.5379461,10.3101106 L6.68957555,13.0059749 C6.59910784,13.0693494 6.47439406,13.0473861 6.41101953,12.9569184 C6.3874624,12.9232903 6.37482581,12.8832269 6.37482581,12.8421686 L6.37482581,7.45043999 C6.37482581,7.33998304 6.46436886,7.25043999 6.57482581,7.25043999 C6.61588409,7.25043999 6.65594753,7.26307658 6.68957555,7.28663371 L10.5379461,9.98249803 C10.6284138,10.0458726 10.6503772,10.1705863 10.5870027,10.2610541 C10.5736331,10.2801392 10.5570312,10.2967411 10.5379461,10.3101106 Z",fill:"currentColor"})))}const jo=" ",fi="#8c8c8c",xu=["png","jpg","jpeg","gif","bmp","webp","svg"],Gy=[{icon:P.createElement(Tp,null),color:"#22b35e",ext:["xlsx","xls"]},{icon:P.createElement(Np,null),color:fi,ext:xu},{icon:P.createElement(jp,null),color:fi,ext:["md","mdx"]},{icon:P.createElement(Ap,null),color:"#ff4d4f",ext:["pdf"]},{icon:P.createElement(Bp,null),color:"#ff6e31",ext:["ppt","pptx"]},{icon:P.createElement(Yp,null),color:"#1677ff",ext:["doc","docx"]},{icon:P.createElement(Jp,null),color:"#fab714",ext:["zip","rar","7z","tar","gz"]},{icon:P.createElement(Xy,null),color:"#ff4d4f",ext:["mp4","avi","mov","wmv","flv","mkv"]},{icon:P.createElement(Vy,null),color:"#8c8c8c",ext:["mp3","wav","flac","ape","aac","ogg"]}];function $s(e,t){return t.some(n=>e.toLowerCase()===`.${n}`)}function qy(e){let t=e;const n=["B","KB","MB","GB","TB","PB","EB"];let r=0;for(;t>=1024&&r<n.length-1;)t/=1024,r++;return`${t.toFixed(0)} ${n[r]}`}function Yy(e,t){const{prefixCls:n,item:r,onRemove:o,className:i,style:a,imageProps:s}=e,l=P.useContext(ur),{disabled:c}=l||{},{name:u,size:f,percent:m,status:p="done",description:v}=r,{getPrefixCls:h}=Ft(),C=h("attachment",n),g=`${C}-list-card`,[b,w,E]=bu(C),[y,x]=P.useMemo(()=>{const z=u||"",D=z.match(/^(.*)\.[^.]+$/);return D?[D[1],z.slice(D[1].length)]:[z,""]},[u]),I=P.useMemo(()=>$s(x,xu),[x]),R=P.useMemo(()=>v||(p==="uploading"?`${m||0}%`:p==="error"?r.response||jo:f?qy(f):jo),[p,m]),[k,T]=P.useMemo(()=>{for(const{ext:z,icon:D,color:W}of Gy)if($s(x,z))return[D,W];return[P.createElement(Wp,{key:"defaultIcon"}),fi]},[x]),[O,N]=P.useState();P.useEffect(()=>{if(r.originFileObj){let z=!0;return Wy(r.originFileObj).then(D=>{z&&N(D)}),()=>{z=!1}}N(void 0)},[r.originFileObj]);let j=null;const M=r.thumbUrl||r.url||O,L=I&&(r.originFileObj||M);return L?j=P.createElement(P.Fragment,null,M&&P.createElement(Xi,ae({alt:"preview",src:M},s)),p!=="done"&&P.createElement("div",{className:`${g}-img-mask`},p==="uploading"&&m!==void 0&&P.createElement(Uy,{percent:m,prefixCls:g}),p==="error"&&P.createElement("div",{className:`${g}-desc`},P.createElement("div",{className:`${g}-ellipsis-prefix`},R)))):j=P.createElement(P.Fragment,null,P.createElement("div",{className:`${g}-icon`,style:{color:T}},k),P.createElement("div",{className:`${g}-content`},P.createElement("div",{className:`${g}-name`},P.createElement("div",{className:`${g}-ellipsis-prefix`},y??jo),P.createElement("div",{className:`${g}-ellipsis-suffix`},x)),P.createElement("div",{className:`${g}-desc`},P.createElement("div",{className:`${g}-ellipsis-prefix`},R)))),b(P.createElement("div",{className:B(g,{[`${g}-status-${p}`]:p,[`${g}-type-preview`]:L,[`${g}-type-overview`]:!L},i,w,E),style:a,ref:t},j,!c&&o&&P.createElement("button",{type:"button",className:`${g}-remove`,onClick:()=>{o(r)}},P.createElement(eo,null))))}const Cu=P.forwardRef(Yy),Es=1;function Ky(e){const{prefixCls:t,items:n,onRemove:r,overflow:o,upload:i,listClassName:a,listStyle:s,itemClassName:l,itemStyle:c,imageProps:u}=e,f=`${t}-list`,m=P.useRef(null),[p,v]=P.useState(!1),{disabled:h}=P.useContext(ur);P.useEffect(()=>(v(!0),()=>{v(!1)}),[]);const[C,g]=P.useState(!1),[b,w]=P.useState(!1),E=()=>{const R=m.current;R&&(o==="scrollX"?(g(Math.abs(R.scrollLeft)>=Es),w(R.scrollWidth-R.clientWidth-Math.abs(R.scrollLeft)>=Es)):o==="scrollY"&&(g(R.scrollTop!==0),w(R.scrollHeight-R.clientHeight!==R.scrollTop)))};P.useEffect(()=>{E()},[o,n.length]);const y=R=>{const k=m.current;k&&k.scrollTo({left:k.scrollLeft+R*k.clientWidth,behavior:"smooth"})},x=()=>{y(-1)},I=()=>{y(1)};return P.createElement("div",{className:B(f,{[`${f}-overflow-${e.overflow}`]:o,[`${f}-overflow-ping-start`]:C,[`${f}-overflow-ping-end`]:b},a),ref:m,onScroll:E,style:s},P.createElement(cc,{keys:n.map(R=>({key:R.uid,item:R})),motionName:`${f}-card-motion`,component:!1,motionAppear:p,motionLeave:!0,motionEnter:!0},({key:R,item:k,className:T,style:O})=>P.createElement(Cu,{key:R,prefixCls:t,item:k,onRemove:r,className:B(T,l),imageProps:u,style:{...O,...c}})),!h&&P.createElement(yu,{upload:i},P.createElement(qe,{className:`${f}-upload-btn`,type:"dashed"},P.createElement(Fr,{className:`${f}-upload-btn-icon`}))),o==="scrollX"&&P.createElement(P.Fragment,null,P.createElement(qe,{size:"small",shape:"circle",className:`${f}-prev-btn`,icon:P.createElement(Hr,null),onClick:x}),P.createElement(qe,{size:"small",shape:"circle",className:`${f}-next-btn`,icon:P.createElement(Jn,null),onClick:I})))}function Zy(e,t){const{prefixCls:n,placeholder:r={},upload:o,className:i,style:a}=e,s=`${n}-placeholder`,l=r||{},{disabled:c}=P.useContext(ur),[u,f]=P.useState(!1),m=()=>{f(!0)},p=C=>{C.currentTarget.contains(C.relatedTarget)||f(!1)},v=()=>{f(!1)},h=P.isValidElement(r)?r:P.createElement(en,{align:"center",justify:"center",vertical:!0,className:`${s}-inner`},P.createElement(Ze.Text,{className:`${s}-icon`},l.icon),P.createElement(Ze.Title,{className:`${s}-title`,level:5},l.title),P.createElement(Ze.Text,{className:`${s}-description`,type:"secondary"},l.description));return P.createElement("div",{className:B(s,{[`${s}-drag-in`]:u,[`${s}-disabled`]:c},i),onDragEnter:m,onDragLeave:p,onDrop:v,"aria-hidden":c,style:a},P.createElement(io.Dragger,ae({showUploadList:!1},o,{ref:t,style:{padding:0,border:0,background:"transparent"}}),h))}const Jy=P.forwardRef(Zy);function Qy(e,t){const{prefixCls:n,rootClassName:r,rootStyle:o,className:i,style:a,items:s,children:l,getDropContainer:c,placeholder:u,onChange:f,onRemove:m,overflow:p,imageProps:v,disabled:h,classNames:C={},styles:g={},...b}=e,{getPrefixCls:w,direction:E}=Ft(),y=w("attachment",n),x=lr("attachments"),{classNames:I,styles:R}=x,k=P.useRef(null),T=P.useRef(null);P.useImperativeHandle(t,()=>({nativeElement:k.current,upload:q=>{var Y,ne;const S=(ne=(Y=T.current)==null?void 0:Y.nativeElement)==null?void 0:ne.querySelector('input[type="file"]');if(S){const K=new DataTransfer;K.items.add(q),S.files=K.files,S.dispatchEvent(new Event("change",{bubbles:!0}))}}}));const[O,N,j]=bu(y),M=B(N,j),[L,z]=bt([],{value:s}),D=tn(q=>{z(q.fileList),f==null||f(q)}),W={...b,fileList:L,onChange:D},F=q=>Promise.resolve(typeof m=="function"?m(q):m).then(S=>{if(S===!1)return;const Y=L.filter(ne=>ne.uid!==q.uid);D({file:{...q,status:"removed"},fileList:Y})});let $;const G=(q,S,Y)=>{const ne=typeof u=="function"?u(q):u;return P.createElement(Jy,{placeholder:ne,upload:W,prefixCls:y,className:B(I.placeholder,C.placeholder),style:{...R.placeholder,...g.placeholder,...S==null?void 0:S.style},ref:Y})};if(l)$=P.createElement(P.Fragment,null,P.createElement(yu,{upload:W,rootClassName:r,ref:T},l),P.createElement(ws,{getDropContainer:c,prefixCls:y,className:B(M,r)},G("drop")));else{const q=L.length>0;$=P.createElement("div",{className:B(y,M,{[`${y}-rtl`]:E==="rtl"},i,r),style:{...o,...a},dir:E||"ltr",ref:k},P.createElement(Ky,{prefixCls:y,items:L,onRemove:F,overflow:p,upload:W,listClassName:B(I.list,C.list),listStyle:{...R.list,...g.list,...!q&&{display:"none"}},itemClassName:B(I.item,C.item),itemStyle:{...R.item,...g.item},imageProps:v}),G("inline",q?{style:{display:"none"}}:{},T),P.createElement(ws,{getDropContainer:c||(()=>k.current),prefixCls:y,className:M},G("drop")))}return O(P.createElement(ur.Provider,{value:{disabled:h}},$))}const Su=P.forwardRef(Qy);Su.FileCard=Cu;function e1(e,t){return d.useImperativeHandle(e,()=>{const n=t(),{nativeElement:r}=n;return new Proxy(r,{get(o,i){return n[i]?n[i]:Reflect.get(o,i)}})})}const wu=d.createContext({}),Is=()=>({height:0}),Ps=e=>({height:e.scrollHeight});function t1(e){const{title:t,onOpenChange:n,open:r,children:o,className:i,style:a,classNames:s={},styles:l={},closable:c,forceRender:u}=e,{prefixCls:f}=d.useContext(wu),m=`${f}-header`;return d.createElement(on,{motionEnter:!0,motionLeave:!0,motionName:`${m}-motion`,leavedClassName:`${m}-motion-hidden`,onEnterStart:Is,onEnterActive:Ps,onLeaveStart:Ps,onLeaveActive:Is,visible:r,forceRender:u},({className:p,style:v})=>d.createElement("div",{className:B(m,p,i),style:{...v,...a}},(c!==!1||t)&&d.createElement("div",{className:B(`${m}-header`,s.header),style:{...l.header}},d.createElement("div",{className:`${m}-title`},t),c!==!1&&d.createElement("div",{className:`${m}-close`},d.createElement(qe,{type:"text",icon:d.createElement(an,null),size:"small",onClick:()=>{n==null||n(!r)}}))),o&&d.createElement("div",{className:B(`${m}-content`,s.content),style:{...l.content}},o)))}const ao=d.createContext(null);function n1(e,t){const{className:n,action:r,onClick:o,...i}=e,a=d.useContext(ao),{prefixCls:s,disabled:l}=a,c=i.disabled??l??a[`${r}Disabled`];return d.createElement(qe,ae({type:"text"},i,{ref:t,onClick:u=>{var f;c||((f=a[r])==null||f.call(a),o==null||o(u))},className:B(s,n,{[`${s}-disabled`]:c})}))}const so=d.forwardRef(n1);function r1(e,t){return d.createElement(so,ae({icon:d.createElement(pp,null)},e,{action:"onClear",ref:t}))}const o1=d.forwardRef(r1),i1=d.memo(e=>{const{className:t}=e;return P.createElement("svg",{color:"currentColor",viewBox:"0 0 1000 1000",xmlns:"http://www.w3.org/2000/svg",className:t},P.createElement("title",null,"Stop Loading"),P.createElement("rect",{fill:"currentColor",height:"250",rx:"24",ry:"24",width:"250",x:"375",y:"375"}),P.createElement("circle",{cx:"500",cy:"500",fill:"none",r:"450",stroke:"currentColor",strokeWidth:"100",opacity:"0.45"}),P.createElement("circle",{cx:"500",cy:"500",fill:"none",r:"450",stroke:"currentColor",strokeWidth:"100",strokeDasharray:"600 9999999"},P.createElement("animateTransform",{attributeName:"transform",dur:"1s",from:"0 500 500",repeatCount:"indefinite",to:"360 500 500",type:"rotate"})))});function a1(e,t){const{prefixCls:n}=d.useContext(ao),{className:r}=e;return d.createElement(so,ae({icon:null,color:"primary",variant:"text",shape:"circle"},e,{className:B(r,`${n}-loading-button`),action:"onCancel",ref:t}),d.createElement(i1,{className:`${n}-loading-icon`}))}const $u=d.forwardRef(a1);function s1(e,t){return d.createElement(so,ae({icon:d.createElement(dc,null),type:"primary",shape:"circle"},e,{action:"onSend",ref:t}))}const Eu=d.forwardRef(s1),_n=1e3,An=4,_r=140,Ts=_r/2,Pr=250,Os=500,Tr=.8;function l1({className:e}){return P.createElement("svg",{color:"currentColor",viewBox:`0 0 ${_n} ${_n}`,xmlns:"http://www.w3.org/2000/svg",className:e},P.createElement("title",null,"Speech Recording"),Array.from({length:An}).map((t,n)=>{const r=(_n-_r*An)/(An-1),o=n*(r+_r),i=_n/2-Pr/2,a=_n/2-Os/2;return P.createElement("rect",{fill:"currentColor",rx:Ts,ry:Ts,height:Pr,width:_r,x:o,y:i,key:n},P.createElement("animate",{attributeName:"height",values:`${Pr}; ${Os}; ${Pr}`,keyTimes:"0; 0.5; 1",dur:`${Tr}s`,begin:`${Tr/An*n}s`,repeatCount:"indefinite"}),P.createElement("animate",{attributeName:"y",values:`${i}; ${a}; ${i}`,keyTimes:"0; 0.5; 1",dur:`${Tr}s`,begin:`${Tr/An*n}s`,repeatCount:"indefinite"}))}))}function c1(e,t){const{speechRecording:n,onSpeechDisabled:r,prefixCls:o}=d.useContext(ao);let i=null;return n?i=d.createElement(l1,{className:`${o}-recording-icon`}):r?i=d.createElement(op,null):i=d.createElement(sp,null),d.createElement(so,ae({icon:i,color:"primary",variant:"text"},e,{action:"onSpeech",ref:t}))}const Iu=d.forwardRef(c1),u1=e=>{const{componentCls:t,calc:n}=e,r=`${t}-header`;return{[t]:{[r]:{borderBottomWidth:e.lineWidth,borderBottomStyle:"solid",borderBottomColor:e.colorBorder,"&-header":{background:e.colorFillAlter,fontSize:e.fontSize,lineHeight:e.lineHeight,paddingBlock:n(e.paddingSM).sub(e.lineWidthBold).equal(),paddingInlineStart:e.padding,paddingInlineEnd:e.paddingXS,display:"flex",borderRadius:{_skip_check_:!0,value:n(e.borderRadius).mul(2).equal()},borderEndStartRadius:0,borderEndEndRadius:0,[`${r}-title`]:{flex:"auto"}},"&-content":{padding:e.padding},"&-motion":{transition:["height","border"].map(o=>`${o} ${e.motionDurationSlow}`).join(","),overflow:"hidden","&-enter-start, &-leave-active":{borderBottomColor:"transparent"},"&-hidden":{display:"none"}}}}}},d1=e=>{const{componentCls:t,padding:n,paddingSM:r,paddingXS:o,paddingXXS:i,lineWidth:a,lineWidthBold:s,calc:l}=e;return{[t]:{position:"relative",width:"100%",boxSizing:"border-box",boxShadow:`${e.boxShadowTertiary}`,transition:`background ${e.motionDurationSlow}`,borderRadius:{_skip_check_:!0,value:l(e.borderRadius).mul(2).equal()},borderColor:e.colorBorder,borderWidth:0,borderStyle:"solid","&:after":{content:'""',position:"absolute",inset:0,pointerEvents:"none",transition:`border-color ${e.motionDurationSlow}`,borderRadius:{_skip_check_:!0,value:"inherit"},borderStyle:"inherit",borderColor:"inherit",borderWidth:a},"&:focus-within":{boxShadow:`${e.boxShadowSecondary}`,borderColor:e.colorPrimary,"&:after":{borderWidth:s}},"&-disabled":{background:e.colorBgContainerDisabled},[`&${t}-rtl`]:{direction:"rtl"},[`${t}-content`]:{display:"flex",gap:o,width:"100%",paddingBlock:r,paddingInlineStart:n,paddingInlineEnd:r,boxSizing:"border-box",alignItems:"flex-end"},[`${t}-prefix`]:{flex:"none"},[`${t}-input`]:{padding:0,borderRadius:0,flex:"auto",alignSelf:"center",minHeight:"auto"},[`${t}-actions-list`]:{flex:"none",display:"flex","&-presets":{gap:e.paddingXS}},[`${t}-actions-btn`]:{"&-disabled":{opacity:.45},"&-loading-button":{padding:0,border:0},"&-loading-icon":{height:e.controlHeight,width:e.controlHeight,verticalAlign:"top"},"&-recording-icon":{height:"1.2em",width:"1.2em",verticalAlign:"top"}},[`${t}-footer`]:{paddingInlineStart:n,paddingInlineEnd:r,paddingBlockEnd:r,paddingBlockStart:i,boxSizing:"border-box"}}}},f1=()=>({}),p1=cr("Sender",e=>{const{paddingXS:t,calc:n}=e,r=tt(e,{SenderContentMaxWidth:`calc(100% - ${H(n(t).add(32).equal())})`});return[d1(r),u1(r)]},f1);let Kr;!Kr&&typeof window<"u"&&(Kr=window.SpeechRecognition||window.webkitSpeechRecognition);function m1(e,t){const n=tn(e),[r,o,i]=P.useMemo(()=>typeof t=="object"?[t.recording,t.onRecordingChange,typeof t.recording=="boolean"]:[void 0,void 0,!1],[t]),[a,s]=P.useState(null);P.useEffect(()=>{if(typeof navigator<"u"&&"permissions"in navigator){let h=null;return navigator.permissions.query({name:"microphone"}).then(C=>{s(C.state),C.onchange=function(){s(this.state)},h=C}),()=>{h&&(h.onchange=null)}}},[]);const l=Kr&&a!=="denied",c=P.useRef(null),[u,f]=bt(!1,{value:r}),m=P.useRef(!1),p=()=>{if(l&&!c.current){const h=new Kr;h.onstart=()=>{f(!0)},h.onend=()=>{f(!1)},h.onresult=C=>{var g,b,w;if(!m.current){const E=(w=(b=(g=C.results)==null?void 0:g[0])==null?void 0:b[0])==null?void 0:w.transcript;n(E)}m.current=!1},c.current=h}},v=tn(h=>{h&&!u||(m.current=h,i?o==null||o(!u):(p(),c.current&&(u?(c.current.stop(),o==null||o(!1)):(c.current.start(),o==null||o(!0)))))});return[l,v,u]}function h1(e,t,n){return Af(e,t)||n}const Rs={SendButton:Eu,ClearButton:o1,LoadingButton:$u,SpeechButton:Iu},g1=P.forwardRef((e,t)=>{const{prefixCls:n,styles:r={},classNames:o={},className:i,rootClassName:a,style:s,defaultValue:l,value:c,readOnly:u,submitType:f="enter",onSubmit:m,loading:p,components:v,onCancel:h,onChange:C,actions:g,onKeyPress:b,onKeyDown:w,disabled:E,allowSpeech:y,prefix:x,footer:I,header:R,onPaste:k,onPasteFile:T,autoSize:O={maxRows:8},...N}=e,{direction:j,getPrefixCls:M}=Ft(),L=M("sender",n),z=P.useRef(null),D=P.useRef(null);e1(t,()=>{var _,U;return{nativeElement:z.current,focus:(_=D.current)==null?void 0:_.focus,blur:(U=D.current)==null?void 0:U.blur}});const W=lr("sender"),F=`${L}-input`,[$,G,q]=p1(L),S=B(L,W.className,i,a,G,q,{[`${L}-rtl`]:j==="rtl",[`${L}-disabled`]:E}),Y=`${L}-actions-btn`,ne=`${L}-actions-list`,[K,ue]=bt(l||"",{value:c}),ce=(_,U)=>{ue(_),C&&C(_,U)},[me,fe,se]=m1(_=>{ce(`${K} ${_}`)},y),ee=h1(v,["input"],uc.TextArea),oe={...rn(N,{attr:!0,aria:!0,data:!0}),ref:D},ie=()=>{K&&m&&!p&&m(K)},be=()=>{ce("")},Ie=P.useRef(!1),ye=()=>{Ie.current=!0},Oe=()=>{Ie.current=!1},xe=_=>{const U=_.key==="Enter"&&!Ie.current;switch(f){case"enter":U&&!_.shiftKey&&(_.preventDefault(),ie());break;case"shiftEnter":U&&_.shiftKey&&(_.preventDefault(),ie());break}b==null||b(_)},$e=_=>{var le;const U=(le=_.clipboardData)==null?void 0:le.files;U!=null&&U.length&&T&&(T(U[0],U),_.preventDefault()),k==null||k(_)},Ae=_=>{var U,le;_.target!==((U=z.current)==null?void 0:U.querySelector(`.${F}`))&&_.preventDefault(),(le=D.current)==null||le.focus()};let Re=P.createElement(en,{className:`${ne}-presets`},y&&P.createElement(Iu,null),p?P.createElement($u,null):P.createElement(Eu,null));typeof g=="function"?Re=g(Re,{components:Rs}):(g||g===!1)&&(Re=g);const Ue={prefixCls:Y,onSend:ie,onSendDisabled:!K,onClear:be,onClearDisabled:!K,onCancel:h,onCancelDisabled:!p,onSpeech:()=>fe(!1),onSpeechDisabled:!me,speechRecording:se,disabled:E},Ke=typeof I=="function"?I({components:Rs}):I||null;return $(P.createElement("div",{ref:z,className:S,style:{...W.style,...s}},R&&P.createElement(wu.Provider,{value:{prefixCls:L}},R),P.createElement(ao.Provider,{value:Ue},P.createElement("div",{className:`${L}-content`,onMouseDown:Ae},x&&P.createElement("div",{className:B(`${L}-prefix`,W.classNames.prefix,o.prefix),style:{...W.styles.prefix,...r.prefix}},x),P.createElement(ee,ae({},oe,{disabled:E,style:{...W.styles.input,...r.input},className:B(F,W.classNames.input,o.input),autoSize:O,value:K,onChange:_=>{ce(_.target.value,_),fe(!0)},onPressEnter:xe,onCompositionStart:ye,onCompositionEnd:Oe,onKeyDown:w,onPaste:$e,variant:"borderless",readOnly:u})),Re&&P.createElement("div",{className:B(ne,W.classNames.actions,o.actions),style:{...W.styles.actions,...r.actions}},Re)),Ke&&P.createElement("div",{className:B(`${L}-footer`,W.classNames.footer,o.footer),style:{...W.styles.footer,...r.footer}},Ke))))}),pi=g1;pi.Header=t1;function Or(e){return typeof e=="string"}const v1=(e,t,n,r)=>{const o=d.useRef(""),[i,a]=d.useState(1),s=t&&Or(e);return Qt(()=>{!s&&Or(e)?a(e.length):Or(e)&&Or(o.current)&&e.indexOf(o.current)!==0&&a(1),o.current=e},[e]),d.useEffect(()=>{if(s&&i<e.length){const c=setTimeout(()=>{a(u=>u+n)},r);return()=>{clearTimeout(c)}}},[i,t,e]),[s?e.slice(0,i):e,s&&i<e.length]};function y1(e){return d.useMemo(()=>{if(!e)return[!1,0,0,null];let t={step:1,interval:50,suffix:null};return typeof e=="object"&&(t={...t,...e}),[!0,t.step,t.interval,t.suffix]},[e])}const b1=({prefixCls:e})=>P.createElement("span",{className:`${e}-dot`},P.createElement("i",{className:`${e}-dot-item`,key:"item-1"}),P.createElement("i",{className:`${e}-dot-item`,key:"item-2"}),P.createElement("i",{className:`${e}-dot-item`,key:"item-3"})),x1=e=>{const{componentCls:t,paddingSM:n,padding:r}=e;return{[t]:{[`${t}-content`]:{"&-filled,&-outlined,&-shadow":{padding:`${H(n)} ${H(r)}`,borderRadius:e.borderRadiusLG},"&-filled":{backgroundColor:e.colorFillContent},"&-outlined":{border:`1px solid ${e.colorBorderSecondary}`},"&-shadow":{boxShadow:e.boxShadowTertiary}}}}},C1=e=>{const{componentCls:t,fontSize:n,lineHeight:r,paddingSM:o,padding:i,calc:a}=e,s=a(n).mul(r).div(2).add(o).equal(),l=`${t}-content`;return{[t]:{[l]:{"&-round":{borderRadius:{_skip_check_:!0,value:s},paddingInline:a(i).mul(1.25).equal()}},[`&-start ${l}-corner`]:{borderStartStartRadius:e.borderRadiusXS},[`&-end ${l}-corner`]:{borderStartEndRadius:e.borderRadiusXS}}}},S1=e=>{const{componentCls:t,padding:n}=e;return{[`${t}-list`]:{display:"flex",flexDirection:"column",gap:n,overflowY:"auto","&::-webkit-scrollbar":{width:8,backgroundColor:"transparent"},"&::-webkit-scrollbar-thumb":{backgroundColor:e.colorTextTertiary,borderRadius:e.borderRadiusSM},"&":{scrollbarWidth:"thin",scrollbarColor:`${e.colorTextTertiary} transparent`}}}},w1=new It("loadingMove",{"0%":{transform:"translateY(0)"},"10%":{transform:"translateY(4px)"},"20%":{transform:"translateY(0)"},"30%":{transform:"translateY(-4px)"},"40%":{transform:"translateY(0)"}}),$1=new It("cursorBlink",{"0%":{opacity:1},"50%":{opacity:0},"100%":{opacity:1}}),E1=e=>{const{componentCls:t,fontSize:n,lineHeight:r,paddingSM:o,colorText:i,calc:a}=e;return{[t]:{display:"flex",columnGap:o,[`&${t}-end`]:{justifyContent:"end",flexDirection:"row-reverse",[`& ${t}-content-wrapper`]:{alignItems:"flex-end"}},[`&${t}-rtl`]:{direction:"rtl"},[`&${t}-typing ${t}-content:last-child::after`]:{content:'"|"',fontWeight:900,userSelect:"none",opacity:1,marginInlineStart:"0.1em",animationName:$1,animationDuration:"0.8s",animationIterationCount:"infinite",animationTimingFunction:"linear"},[`& ${t}-avatar`]:{display:"inline-flex",justifyContent:"center",alignSelf:"flex-start"},[`& ${t}-header, & ${t}-footer`]:{fontSize:n,lineHeight:r,color:e.colorText},[`& ${t}-header`]:{marginBottom:e.paddingXXS},[`& ${t}-footer`]:{marginTop:o},[`& ${t}-content-wrapper`]:{flex:"auto",display:"flex",flexDirection:"column",alignItems:"flex-start",minWidth:0,maxWidth:"100%"},[`& ${t}-content`]:{position:"relative",boxSizing:"border-box",minWidth:0,maxWidth:"100%",color:i,fontSize:e.fontSize,lineHeight:e.lineHeight,minHeight:a(o).mul(2).add(a(r).mul(n)).equal(),wordBreak:"break-word",[`& ${t}-dot`]:{position:"relative",height:"100%",display:"flex",alignItems:"center",columnGap:e.marginXS,padding:`0 ${H(e.paddingXXS)}`,"&-item":{backgroundColor:e.colorPrimary,borderRadius:"100%",width:4,height:4,animationName:w1,animationDuration:"2s",animationIterationCount:"infinite",animationTimingFunction:"linear","&:nth-child(1)":{animationDelay:"0s"},"&:nth-child(2)":{animationDelay:"0.2s"},"&:nth-child(3)":{animationDelay:"0.4s"}}}}}}},I1=()=>({}),Pu=cr("Bubble",e=>{const t=tt(e,{});return[E1(t),S1(t),x1(t),C1(t)]},I1),Tu=P.createContext({}),P1=(e,t)=>{const{prefixCls:n,className:r,rootClassName:o,style:i,classNames:a={},styles:s={},avatar:l,placement:c="start",loading:u=!1,loadingRender:f,typing:m,content:p="",messageRender:v,variant:h="filled",shape:C,onTypingComplete:g,header:b,footer:w,_key:E,...y}=e,{onUpdate:x}=P.useContext(Tu),I=P.useRef(null);P.useImperativeHandle(t,()=>({nativeElement:I.current}));const{direction:R,getPrefixCls:k}=Ft(),T=k("bubble",n),O=lr("bubble"),[N,j,M,L]=y1(m),[z,D]=v1(p,N,j,M);P.useEffect(()=>{x==null||x()},[z]);const W=P.useRef(!1);P.useEffect(()=>{!D&&!u?W.current||(W.current=!0,g==null||g()):W.current=!1},[D,u]);const[F,$,G]=Pu(T),q=B(T,o,O.className,r,$,G,`${T}-${c}`,{[`${T}-rtl`]:R==="rtl",[`${T}-typing`]:D&&!u&&!v&&!L}),S=P.useMemo(()=>P.isValidElement(l)?l:P.createElement(Vr,l),[l]),Y=P.useMemo(()=>v?v(z):z,[z,v]),ne=ce=>typeof ce=="function"?ce(z,{key:E}):ce;let K;u?K=f?f():P.createElement(b1,{prefixCls:T}):K=P.createElement(P.Fragment,null,Y,D&&L);let ue=P.createElement("div",{style:{...O.styles.content,...s.content},className:B(`${T}-content`,`${T}-content-${h}`,C&&`${T}-content-${C}`,O.classNames.content,a.content)},K);return(b||w)&&(ue=P.createElement("div",{className:`${T}-content-wrapper`},b&&P.createElement("div",{className:B(`${T}-header`,O.classNames.header,a.header),style:{...O.styles.header,...s.header}},ne(b)),ue,w&&P.createElement("div",{className:B(`${T}-footer`,O.classNames.footer,a.footer),style:{...O.styles.footer,...s.footer}},ne(w)))),F(P.createElement("div",ae({style:{...O.style,...i},className:q},y,{ref:I}),l&&P.createElement("div",{style:{...O.styles.avatar,...s.avatar},className:B(`${T}-avatar`,O.classNames.avatar,a.avatar)},S),ue))},Yi=P.forwardRef(P1);function T1(e,t){const n=d.useCallback((r,o)=>typeof t=="function"?t(r,o):t?t[r.role]||{}:{},[t]);return d.useMemo(()=>(e||[]).map((r,o)=>{const i=r.key??`preset_${o}`;return{...n(r,o),...r,key:i}}),[e,n])}const O1=({_key:e,...t},n)=>d.createElement(Yi,ae({},t,{_key:e,ref:r=>{var o;r?n.current[e]=r:(o=n.current)==null||delete o[e]}})),R1=d.memo(d.forwardRef(O1)),N1=1,k1=(e,t)=>{const{prefixCls:n,rootClassName:r,className:o,items:i,autoScroll:a=!0,roles:s,...l}=e,c=rn(l,{attr:!0,aria:!0}),u=d.useRef(null),f=d.useRef({}),{getPrefixCls:m}=Ft(),p=m("bubble",n),v=`${p}-list`,[h,C,g]=Pu(p),[b,w]=d.useState(!1);d.useEffect(()=>(w(!0),()=>{w(!1)}),[]);const E=T1(i,s),[y,x]=d.useState(!0),[I,R]=d.useState(0),k=N=>{const j=N.target;x(j.scrollHeight-Math.abs(j.scrollTop)-j.clientHeight<=N1)};d.useEffect(()=>{a&&u.current&&y&&u.current.scrollTo({top:u.current.scrollHeight})},[I]),d.useEffect(()=>{var N;if(a){const j=(N=E[E.length-2])==null?void 0:N.key,M=f.current[j];if(M){const{nativeElement:L}=M,{top:z,bottom:D}=L.getBoundingClientRect(),{top:W,bottom:F}=u.current.getBoundingClientRect();z<F&&D>W&&(R(G=>G+1),x(!0))}}},[E.length]),d.useImperativeHandle(t,()=>({nativeElement:u.current,scrollTo:({key:N,offset:j,behavior:M="smooth",block:L})=>{if(typeof j=="number")u.current.scrollTo({top:j,behavior:M});else if(N!==void 0){const z=f.current[N];if(z){const D=E.findIndex(W=>W.key===N);x(D===E.length-1),z.nativeElement.scrollIntoView({behavior:M,block:L})}}}}));const T=tn(()=>{a&&R(N=>N+1)}),O=d.useMemo(()=>({onUpdate:T}),[]);return h(d.createElement(Tu.Provider,{value:O},d.createElement("div",ae({},c,{className:B(v,r,o,C,g,{[`${v}-reach-end`]:y}),ref:u,onScroll:k}),E.map(({key:N,...j})=>d.createElement(R1,ae({},j,{key:N,_key:N,ref:f,typing:b?j.typing:!1}))))))},M1=d.forwardRef(k1);Yi.List=M1;const Ou=P.createContext(null),Ns=({children:e})=>{const{prefixCls:t}=P.useContext(Ou);return P.createElement("div",{className:B(`${t}-group-title`)},e&&P.createElement(Ze.Text,null,e))},j1=e=>{e.stopPropagation()},L1=e=>{const{prefixCls:t,info:n,className:r,direction:o,onClick:i,active:a,menu:s,...l}=e,c=rn(l,{aria:!0,data:!0,attr:!0}),{disabled:u}=n,f=B(r,`${t}-item`,{[`${t}-item-active`]:a&&!u},{[`${t}-item-disabled`]:u}),m=()=>{!u&&i&&i(n)},{trigger:p,...v}=s||{},h=v==null?void 0:v.getPopupContainer,C=g=>{const b=P.createElement(Ql,{onClick:j1,className:`${t}-menu-icon`});return p?typeof p=="function"?p(g,{originNode:b}):p:b};return P.createElement("li",ae({},c,{className:f,onClick:m,title:`${n.label}`}),n.icon&&P.createElement("div",{className:`${t}-icon`},n.icon),P.createElement(Ze.Text,{className:`${t}-label`},n.label),!u&&s&&P.createElement(zf,{menu:v,placement:o==="rtl"?"bottomLeft":"bottomRight",trigger:["click"],disabled:u,getPopupContainer:h},C(n)))},Lo="__ungrouped",_1=(e,t=[])=>{const[n,r,o]=P.useMemo(()=>{if(!e)return[!1,void 0,void 0];let i={sort:void 0,title:void 0};return typeof e=="object"&&(i={...i,...e}),[!0,i.sort,i.title]},[e]);return P.useMemo(()=>{if(!n)return[[{name:Lo,data:t,title:void 0}],n];const i=t.reduce((l,c)=>{const u=c.group||Lo;return l[u]||(l[u]=[]),l[u].push(c),l},{});return[(r?Object.keys(i).sort(r):Object.keys(i)).map(l=>({name:l===Lo?void 0:l,title:o,data:i[l]})),n]},[t,e])},A1=e=>{const{componentCls:t}=e;return{[t]:{display:"flex",flexDirection:"column",gap:e.paddingXXS,overflowY:"auto",padding:e.paddingSM,margin:0,listStyle:"none","ul, ol":{margin:0,padding:0,listStyle:"none"},[`&${t}-rtl`]:{direction:"rtl"},[`& ${t}-list`]:{display:"flex",gap:e.paddingXXS,flexDirection:"column",[`& ${t}-item`]:{paddingInlineStart:e.paddingXL},[`& ${t}-label`]:{color:e.colorTextDescription,overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}},[`& ${t}-item`]:{display:"flex",height:e.controlHeightLG,minHeight:e.controlHeightLG,gap:e.paddingXS,padding:`0 ${H(e.paddingXS)}`,alignItems:"center",borderRadius:e.borderRadiusLG,cursor:"pointer",transition:`all ${e.motionDurationMid} ${e.motionEaseInOut}`,"&:hover":{backgroundColor:e.colorBgTextHover},"&-active":{backgroundColor:e.colorBgTextHover,[`& ${t}-label, ${t}-menu-icon`]:{color:e.colorText}},"&-disabled":{cursor:"not-allowed",[`& ${t}-label`]:{color:e.colorTextDisabled}},"&:hover, &-active":{[`& ${t}-menu-icon`]:{opacity:.6}},[`${t}-menu-icon:hover`]:{opacity:1}},[`& ${t}-label`]:{flex:1,color:e.colorText,overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},[`& ${t}-menu-icon`]:{opacity:0,fontSize:e.fontSizeXL},[`& ${t}-group-title`]:{display:"flex",alignItems:"center",height:e.controlHeightLG,minHeight:e.controlHeightLG,padding:`0 ${H(e.paddingXS)}`}}}},z1=()=>({}),D1=cr("Conversations",e=>{const t=tt(e,{});return A1(t)},z1),B1=e=>{const{prefixCls:t,rootClassName:n,items:r,activeKey:o,defaultActiveKey:i,onActiveChange:a,menu:s,styles:l={},classNames:c={},groupable:u,className:f,style:m,...p}=e,v=rn(p,{attr:!0,aria:!0,data:!0}),[h,C]=bt(i,{value:o}),[g,b]=_1(u,r),{getPrefixCls:w,direction:E}=Ft(),y=w("conversations",t),x=lr("conversations"),[I,R,k]=D1(y),T=B(y,x.className,f,n,R,k,{[`${y}-rtl`]:E==="rtl"}),O=N=>{C(N.key),a&&a(N.key)};return I(P.createElement("ul",ae({},v,{style:{...x.style,...m},className:T}),g.map((N,j)=>{var L;const M=N.data.map((z,D)=>P.createElement(L1,{key:z.key||`key-${D}`,info:z,prefixCls:y,direction:E,className:B(c.item,x.classNames.item),style:{...x.styles.item,...l.item},menu:typeof s=="function"?s(z):s,active:h===z.key,onClick:O}));return b?P.createElement("li",{key:N.name||`key-${j}`},P.createElement(Ou.Provider,{value:{prefixCls:y}},((L=N.title)==null?void 0:L.call(N,N.name,{components:{GroupTitle:Ns}}))||P.createElement(Ns,{key:N.name},N.name)),P.createElement("ul",{className:`${y}-list`},M)):M})))},F1=e=>{const{componentCls:t}=e;return{[t]:{"&, & *":{boxSizing:"border-box"},maxWidth:"100%",[`&${t}-rtl`]:{direction:"rtl"},[`& ${t}-title`]:{marginBlockStart:0,fontWeight:"normal",color:e.colorTextTertiary},[`& ${t}-list`]:{display:"flex",gap:e.paddingSM,overflowX:"auto",scrollbarWidth:"none","-ms-overflow-style":"none","&::-webkit-scrollbar":{display:"none"},listStyle:"none",paddingInlineStart:0,marginBlock:0,alignItems:"stretch","&-wrap":{flexWrap:"wrap"},"&-vertical":{flexDirection:"column",alignItems:"flex-start"}},[`${t}-item`]:{flex:"none",display:"flex",gap:e.paddingXS,height:"auto",paddingBlock:e.paddingSM,paddingInline:e.padding,alignItems:"flex-start",justifyContent:"flex-start",background:e.colorBgContainer,borderRadius:e.borderRadiusLG,transition:["border","background"].map(n=>`${n} ${e.motionDurationSlow}`).join(","),border:`${H(e.lineWidth)} ${e.lineType} ${e.colorBorderSecondary}`,[`&:not(${t}-item-has-nest)`]:{"&:hover":{cursor:"pointer",background:e.colorFillTertiary},"&:active":{background:e.colorFill}},[`${t}-content`]:{flex:"auto",minWidth:0,display:"flex",gap:e.paddingXXS,flexDirection:"column",alignItems:"flex-start"},[`${t}-icon, ${t}-label, ${t}-desc`]:{margin:0,padding:0,fontSize:e.fontSize,lineHeight:e.lineHeight,textAlign:"start",whiteSpace:"normal"},[`${t}-label`]:{color:e.colorTextHeading,fontWeight:500},[`${t}-label + ${t}-desc`]:{color:e.colorTextTertiary},[`&${t}-item-disabled`]:{pointerEvents:"none",background:e.colorBgContainerDisabled,[`${t}-label, ${t}-desc`]:{color:e.colorTextTertiary}}}}}},H1=e=>{const{componentCls:t}=e;return{[t]:{[`${t}-item-has-nest`]:{[`> ${t}-content`]:{[`> ${t}-label`]:{fontSize:e.fontSizeLG,lineHeight:e.lineHeightLG}}},[`&${t}-nested`]:{marginTop:e.paddingXS,alignSelf:"stretch",[`${t}-list`]:{alignItems:"stretch"},[`${t}-item`]:{border:0,background:e.colorFillQuaternary}}}}},W1=()=>({}),V1=cr("Prompts",e=>{const t=tt(e,{});return[F1(t),H1(t)]},W1),Ru=e=>{const{prefixCls:t,title:n,className:r,items:o,onItemClick:i,vertical:a,wrap:s,rootClassName:l,styles:c={},classNames:u={},style:f,...m}=e,{getPrefixCls:p,direction:v}=Ft(),h=p("prompts",t),C=lr("prompts"),[g,b,w]=V1(h),E=B(h,C.className,r,l,b,w,{[`${h}-rtl`]:v==="rtl"}),y=B(`${h}-list`,C.classNames.list,u.list,{[`${h}-list-wrap`]:s},{[`${h}-list-vertical`]:a});return g(P.createElement("div",ae({},m,{className:E,style:{...f,...C.style}}),n&&P.createElement(Ze.Title,{level:5,className:B(`${h}-title`,C.classNames.title,u.title),style:{...C.styles.title,...c.title}},n),P.createElement("div",{className:y,style:{...C.styles.list,...c.list}},o==null?void 0:o.map((x,I)=>{const R=x.children&&x.children.length>0;return P.createElement("div",{key:x.key||`key_${I}`,style:{...C.styles.item,...c.item},className:B(`${h}-item`,C.classNames.item,u.item,{[`${h}-item-disabled`]:x.disabled,[`${h}-item-has-nest`]:R}),onClick:()=>{!R&&i&&i({data:x})}},x.icon&&P.createElement("div",{className:`${h}-icon`},x.icon),P.createElement("div",{className:B(`${h}-content`,C.classNames.itemContent,u.itemContent),style:{...C.styles.itemContent,...c.itemContent}},x.label&&P.createElement("h6",{className:`${h}-label`},x.label),x.description&&P.createElement("p",{className:`${h}-desc`},x.description),R&&P.createElement(Ru,{className:`${h}-nested`,items:x.children,vertical:!0,onItemClick:i,classNames:{list:u.subList,item:u.subItem},styles:{list:c.subList,item:c.subItem}})))}))))};function U1(e){const[,t]=P.useState(0),n=P.useRef(typeof e=="function"?e():e),r=P.useCallback(i=>{n.current=typeof i=="function"?i(n.current):i,t(a=>a+1)},[]),o=P.useCallback(()=>n.current,[]);return[n.current,r,o]}function X1(e){return Array.isArray(e)?e:[e]}function G1(e){const{defaultMessages:t,agent:n,requestFallback:r,requestPlaceholder:o,parser:i,transformMessage:a,transformStream:s,resolveAbortController:l}=e,c=P.useRef(0),[u,f,m]=U1(()=>(t||[]).map((w,E)=>({id:`default_${E}`,status:"local",...w}))),p=(w,E)=>{const y={id:`msg_${c.current}`,message:w,status:E};return c.current+=1,y},v=P.useMemo(()=>{const w=[];return u.forEach(E=>{const y=i?i(E.message):E.message,x=X1(y);x.forEach((I,R)=>{let k=E.id;x.length>1&&(k=`${k}_${R}`),w.push({id:k,message:I,status:E.status})})}),w},[u]),h=w=>w.filter(E=>E.status!=="loading"&&E.status!=="error").map(E=>E.message),C=()=>h(m()),g=w=>{const{chunk:E,chunks:y,originMessage:x}=w;if(typeof a=="function")return a(w);if(E)return E;if(Array.isArray(y)){const I=(y==null?void 0:y.length)>0?y==null?void 0:y[(y==null?void 0:y.length)-1]:void 0;return x||I}return y};return{onRequest:tn(w=>{if(!n)throw new Error("The agent parameter is required when using the onRequest method in an agent generated by useXAgent.");let E=null,y,x={};if(w&&typeof w=="object"&&"message"in w){const{message:k,...T}=w;y=k,x=T}else y=w;f(k=>{let T=[...k,p(y,"local")];if(o){let O;typeof o=="function"?O=o(y,{messages:h(T)}):O=o;const N=p(O,"loading");E=N.id,T=[...T,N]}return T});let I=null;const R=(k,T,O)=>{let N=m().find(j=>j.id===I);if(N)f(j=>j.map(M=>{if(M.id===I){const L=g({originMessage:M.message,chunk:T,chunks:O,status:k});return{...M,message:L,status:k}}return M}));else{const j=g({chunk:T,status:k,chunks:O});N=p(j,k),f(M=>[...M.filter(z=>z.id!==E),N]),I=N.id}return N};n.request({message:y,messages:C(),...x},{onUpdate:k=>{R("loading",k,[])},onSuccess:k=>{R("success",void 0,k)},onError:async k=>{if(r){let T;typeof r=="function"?T=await r(y,{error:k,messages:C()}):T=r,f(O=>[...O.filter(N=>N.id!==E&&N.id!==I),p(T,"error")])}else f(T=>T.filter(O=>O.id!==E&&O.id!==I))},onStream:k=>{l==null||l(k)}},s)}),messages:u,parsedMessages:v,setMessages:f}}const q1=`

`,Y1=`
`,ks=":",mi=e=>(e??"").trim()!=="";function K1(){let e="";return new TransformStream({transform(t,n){e+=t;const r=e.split(q1);r.slice(0,-1).forEach(o=>{mi(o)&&n.enqueue(o)}),e=r[r.length-1]},flush(t){mi(e)&&t.enqueue(e)}})}function Z1(){return new TransformStream({transform(e,t){const r=e.split(Y1).reduce((o,i)=>{const a=i.indexOf(ks);if(a===-1)throw new Error(`The key-value separator "${ks}" is not found in the sse line chunk!`);const s=i.slice(0,a);if(!mi(s))return o;const l=i.slice(a+1);return{...o,[s]:l}},{});Object.keys(r).length!==0&&t.enqueue(r)}})}function Ms(e){const{readableStream:t,transformStream:n}=e;if(!(t instanceof ReadableStream))throw new Error("The options.readableStream must be an instance of ReadableStream.");const r=new TextDecoderStream,o=n?t.pipeThrough(r).pipeThrough(n):t.pipeThrough(r).pipeThrough(K1()).pipeThrough(Z1());return o[Symbol.asyncIterator]=async function*(){const i=this.getReader();for(;;){const{done:a,value:s}=await i.read();if(a)break;s&&(yield s)}},o}const J1=async(e,t={})=>{const{fetch:n=globalThis.fetch,middlewares:r={},...o}=t;if(typeof n!="function")throw new Error("The options.fetch must be a typeof fetch function!");let i=[e,o];typeof r.onRequest=="function"&&(i=await r.onRequest(...i));let a=await n(...i);if(typeof r.onResponse=="function"){const s=await r.onResponse(a);if(!(s instanceof Response))throw new Error("The options.onResponse must return a Response instance!");a=s}if(!a.ok)throw new Error(`Fetch failed with status ${a.status}`);if(!a.body)throw new Error("The response body is empty.");return a};class Ki{constructor(t){wt(this,"baseURL");wt(this,"model");wt(this,"defaultHeaders");wt(this,"customOptions");wt(this,"create",async(t,n,r)=>{var a,s;const o=new AbortController,i={method:"POST",body:JSON.stringify({model:this.model,...t}),headers:this.defaultHeaders,signal:o.signal};(a=n==null?void 0:n.onStream)==null||a.call(n,o);try{const l=await J1(this.baseURL,{fetch:this.customOptions.fetch,...i});if(r){await this.customResponseHandler(l,n,r);return}const c=l.headers.get("content-type")||"";switch(c.split(";")[0].trim()){case"text/event-stream":await this.sseResponseHandler(l,n);break;case"application/json":await this.jsonResponseHandler(l,n);break;default:throw new Error(`The response content-type: ${c} is not support!`)}}catch(l){const c=l instanceof Error?l:new Error("Unknown error!");throw(s=n==null?void 0:n.onError)==null||s.call(n,c),c}});wt(this,"customResponseHandler",async(t,n,r)=>{var i,a;const o=[];for await(const s of Ms({readableStream:t.body,transformStream:r}))o.push(s),(i=n==null?void 0:n.onUpdate)==null||i.call(n,s);(a=n==null?void 0:n.onSuccess)==null||a.call(n,o)});wt(this,"sseResponseHandler",async(t,n)=>{var i,a;const r=[],o=Ms({readableStream:t.body});for await(const s of o)r.push(s),(i=n==null?void 0:n.onUpdate)==null||i.call(n,s);(a=n==null?void 0:n.onSuccess)==null||a.call(n,r)});wt(this,"jsonResponseHandler",async(t,n)=>{var o,i;const r=await t.json();(o=n==null?void 0:n.onUpdate)==null||o.call(n,r),(i=n==null?void 0:n.onSuccess)==null||i.call(n,[r])});const{baseURL:n,model:r,dangerouslyApiKey:o,...i}=t;this.baseURL=t.baseURL,this.model=t.model,this.defaultHeaders={"Content-Type":"application/json",...t.dangerouslyApiKey&&{Authorization:t.dangerouslyApiKey}},this.customOptions=i}static init(t){if(!t.baseURL||typeof t.baseURL!="string")throw new Error("The baseURL is not valid!");return new Ki(t)}}const Q1=Ki.init;let js=0;class eb{constructor(t){wt(this,"config");wt(this,"requestingMap",{});wt(this,"request",(t,n,r)=>{const{request:o}=this.config,{onUpdate:i,onSuccess:a,onError:s,onStream:l}=n,c=js;js+=1,this.requestingMap[c]=!0,o==null||o(t,{onStream:u=>{this.requestingMap[c]&&(l==null||l(u))},onUpdate:u=>{this.requestingMap[c]&&i(u)},onSuccess:u=>{this.requestingMap[c]&&(a(u),this.finishRequest(c))},onError:u=>{this.requestingMap[c]&&(s(u),this.finishRequest(c))}},r)});this.config=t}finishRequest(t){delete this.requestingMap[t]}isRequesting(){return Object.keys(this.requestingMap).length>0}}function tb(e){const{request:t,...n}=e;return P.useMemo(()=>[new eb({request:t||Q1({baseURL:n.baseURL,model:n.model,dangerouslyApiKey:n.dangerouslyApiKey}).create,...n})],[e==null?void 0:e.baseURL,e==null?void 0:e.dangerouslyApiKey,e==null?void 0:e.model])}function nb(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]}function rb(e){var t=document.createElement("style");return t.setAttribute("data-emotion",e.key),e.nonce!==void 0&&t.setAttribute("nonce",e.nonce),t.appendChild(document.createTextNode("")),t.setAttribute("data-s",""),t}var ob=function(){function e(n){var r=this;this._insertTag=function(o){var i;r.tags.length===0?r.insertionPoint?i=r.insertionPoint.nextSibling:r.prepend?i=r.container.firstChild:i=r.before:i=r.tags[r.tags.length-1].nextSibling,r.container.insertBefore(o,i),r.tags.push(o)},this.isSpeedy=n.speedy===void 0?!0:n.speedy,this.tags=[],this.ctr=0,this.nonce=n.nonce,this.key=n.key,this.container=n.container,this.prepend=n.prepend,this.insertionPoint=n.insertionPoint,this.before=null}var t=e.prototype;return t.hydrate=function(r){r.forEach(this._insertTag)},t.insert=function(r){this.ctr%(this.isSpeedy?65e3:1)===0&&this._insertTag(rb(this));var o=this.tags[this.tags.length-1];if(this.isSpeedy){var i=nb(o);try{i.insertRule(r,i.cssRules.length)}catch{}}else o.appendChild(document.createTextNode(r));this.ctr++},t.flush=function(){this.tags.forEach(function(r){var o;return(o=r.parentNode)==null?void 0:o.removeChild(r)}),this.tags=[],this.ctr=0},e}(),rt="-ms-",Zr="-moz-",Ne="-webkit-",Nu="comm",Zi="rule",Ji="decl",ib="@import",ku="@keyframes",ab="@layer",sb=Math.abs,lo=String.fromCharCode,lb=Object.assign;function cb(e,t){return Qe(e,0)^45?(((t<<2^Qe(e,0))<<2^Qe(e,1))<<2^Qe(e,2))<<2^Qe(e,3):0}function Mu(e){return e.trim()}function ub(e,t){return(e=t.exec(e))?e[0]:e}function ke(e,t,n){return e.replace(t,n)}function hi(e,t){return e.indexOf(t)}function Qe(e,t){return e.charCodeAt(t)|0}function er(e,t,n){return e.slice(t,n)}function Mt(e){return e.length}function Qi(e){return e.length}function Rr(e,t){return t.push(e),e}function db(e,t){return e.map(t).join("")}var co=1,wn=1,ju=0,ft=0,Ye=0,Tn="";function uo(e,t,n,r,o,i,a){return{value:e,root:t,parent:n,type:r,props:o,children:i,line:co,column:wn,length:a,return:""}}function zn(e,t){return lb(uo("",null,null,"",null,null,0),e,{length:-e.length},t)}function fb(){return Ye}function pb(){return Ye=ft>0?Qe(Tn,--ft):0,wn--,Ye===10&&(wn=1,co--),Ye}function yt(){return Ye=ft<ju?Qe(Tn,ft++):0,wn++,Ye===10&&(wn=1,co++),Ye}function _t(){return Qe(Tn,ft)}function Ar(){return ft}function dr(e,t){return er(Tn,e,t)}function tr(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function Lu(e){return co=wn=1,ju=Mt(Tn=e),ft=0,[]}function _u(e){return Tn="",e}function zr(e){return Mu(dr(ft-1,gi(e===91?e+2:e===40?e+1:e)))}function mb(e){for(;(Ye=_t())&&Ye<33;)yt();return tr(e)>2||tr(Ye)>3?"":" "}function hb(e,t){for(;--t&&yt()&&!(Ye<48||Ye>102||Ye>57&&Ye<65||Ye>70&&Ye<97););return dr(e,Ar()+(t<6&&_t()==32&&yt()==32))}function gi(e){for(;yt();)switch(Ye){case e:return ft;case 34:case 39:e!==34&&e!==39&&gi(Ye);break;case 40:e===41&&gi(e);break;case 92:yt();break}return ft}function gb(e,t){for(;yt()&&e+Ye!==57;)if(e+Ye===84&&_t()===47)break;return"/*"+dr(t,ft-1)+"*"+lo(e===47?e:yt())}function vb(e){for(;!tr(_t());)yt();return dr(e,ft)}function yb(e){return _u(Dr("",null,null,null,[""],e=Lu(e),0,[0],e))}function Dr(e,t,n,r,o,i,a,s,l){for(var c=0,u=0,f=a,m=0,p=0,v=0,h=1,C=1,g=1,b=0,w="",E=o,y=i,x=r,I=w;C;)switch(v=b,b=yt()){case 40:if(v!=108&&Qe(I,f-1)==58){hi(I+=ke(zr(b),"&","&\f"),"&\f")!=-1&&(g=-1);break}case 34:case 39:case 91:I+=zr(b);break;case 9:case 10:case 13:case 32:I+=mb(v);break;case 92:I+=hb(Ar()-1,7);continue;case 47:switch(_t()){case 42:case 47:Rr(bb(gb(yt(),Ar()),t,n),l);break;default:I+="/"}break;case 123*h:s[c++]=Mt(I)*g;case 125*h:case 59:case 0:switch(b){case 0:case 125:C=0;case 59+u:g==-1&&(I=ke(I,/\f/g,"")),p>0&&Mt(I)-f&&Rr(p>32?_s(I+";",r,n,f-1):_s(ke(I," ","")+";",r,n,f-2),l);break;case 59:I+=";";default:if(Rr(x=Ls(I,t,n,c,u,o,s,w,E=[],y=[],f),i),b===123)if(u===0)Dr(I,t,x,x,E,i,f,s,y);else switch(m===99&&Qe(I,3)===110?100:m){case 100:case 108:case 109:case 115:Dr(e,x,x,r&&Rr(Ls(e,x,x,0,0,o,s,w,o,E=[],f),y),o,y,f,s,r?E:y);break;default:Dr(I,x,x,x,[""],y,0,s,y)}}c=u=p=0,h=g=1,w=I="",f=a;break;case 58:f=1+Mt(I),p=v;default:if(h<1){if(b==123)--h;else if(b==125&&h++==0&&pb()==125)continue}switch(I+=lo(b),b*h){case 38:g=u>0?1:(I+="\f",-1);break;case 44:s[c++]=(Mt(I)-1)*g,g=1;break;case 64:_t()===45&&(I+=zr(yt())),m=_t(),u=f=Mt(w=I+=vb(Ar())),b++;break;case 45:v===45&&Mt(I)==2&&(h=0)}}return i}function Ls(e,t,n,r,o,i,a,s,l,c,u){for(var f=o-1,m=o===0?i:[""],p=Qi(m),v=0,h=0,C=0;v<r;++v)for(var g=0,b=er(e,f+1,f=sb(h=a[v])),w=e;g<p;++g)(w=Mu(h>0?m[g]+" "+b:ke(b,/&\f/g,m[g])))&&(l[C++]=w);return uo(e,t,n,o===0?Zi:s,l,c,u)}function bb(e,t,n){return uo(e,t,n,Nu,lo(fb()),er(e,2,-2),0)}function _s(e,t,n,r){return uo(e,t,n,Ji,er(e,0,r),er(e,r+1,-1),r)}function xn(e,t){for(var n="",r=Qi(e),o=0;o<r;o++)n+=t(e[o],o,e,t)||"";return n}function xb(e,t,n,r){switch(e.type){case ab:if(e.children.length)break;case ib:case Ji:return e.return=e.return||e.value;case Nu:return"";case ku:return e.return=e.value+"{"+xn(e.children,r)+"}";case Zi:e.value=e.props.join(",")}return Mt(n=xn(e.children,r))?e.return=e.value+"{"+n+"}":""}function Cb(e){var t=Qi(e);return function(n,r,o,i){for(var a="",s=0;s<t;s++)a+=e[s](n,r,o,i)||"";return a}}function Sb(e){return function(t){t.root||(t=t.return)&&e(t)}}var As=function(t){var n=new WeakMap;return function(r){if(n.has(r))return n.get(r);var o=t(r);return n.set(r,o),o}};function wb(e){var t=Object.create(null);return function(n){return t[n]===void 0&&(t[n]=e(n)),t[n]}}var $b=function(t,n,r){for(var o=0,i=0;o=i,i=_t(),o===38&&i===12&&(n[r]=1),!tr(i);)yt();return dr(t,ft)},Eb=function(t,n){var r=-1,o=44;do switch(tr(o)){case 0:o===38&&_t()===12&&(n[r]=1),t[r]+=$b(ft-1,n,r);break;case 2:t[r]+=zr(o);break;case 4:if(o===44){t[++r]=_t()===58?"&\f":"",n[r]=t[r].length;break}default:t[r]+=lo(o)}while(o=yt());return t},Ib=function(t,n){return _u(Eb(Lu(t),n))},zs=new WeakMap,Pb=function(t){if(!(t.type!=="rule"||!t.parent||t.length<1)){for(var n=t.value,r=t.parent,o=t.column===r.column&&t.line===r.line;r.type!=="rule";)if(r=r.parent,!r)return;if(!(t.props.length===1&&n.charCodeAt(0)!==58&&!zs.get(r))&&!o){zs.set(t,!0);for(var i=[],a=Ib(n,i),s=r.props,l=0,c=0;l<a.length;l++)for(var u=0;u<s.length;u++,c++)t.props[c]=i[l]?a[l].replace(/&\f/g,s[u]):s[u]+" "+a[l]}}},Tb=function(t){if(t.type==="decl"){var n=t.value;n.charCodeAt(0)===108&&n.charCodeAt(2)===98&&(t.return="",t.value="")}};function Au(e,t){switch(cb(e,t)){case 5103:return Ne+"print-"+e+e;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return Ne+e+e;case 5349:case 4246:case 4810:case 6968:case 2756:return Ne+e+Zr+e+rt+e+e;case 6828:case 4268:return Ne+e+rt+e+e;case 6165:return Ne+e+rt+"flex-"+e+e;case 5187:return Ne+e+ke(e,/(\w+).+(:[^]+)/,Ne+"box-$1$2"+rt+"flex-$1$2")+e;case 5443:return Ne+e+rt+"flex-item-"+ke(e,/flex-|-self/,"")+e;case 4675:return Ne+e+rt+"flex-line-pack"+ke(e,/align-content|flex-|-self/,"")+e;case 5548:return Ne+e+rt+ke(e,"shrink","negative")+e;case 5292:return Ne+e+rt+ke(e,"basis","preferred-size")+e;case 6060:return Ne+"box-"+ke(e,"-grow","")+Ne+e+rt+ke(e,"grow","positive")+e;case 4554:return Ne+ke(e,/([^-])(transform)/g,"$1"+Ne+"$2")+e;case 6187:return ke(ke(ke(e,/(zoom-|grab)/,Ne+"$1"),/(image-set)/,Ne+"$1"),e,"")+e;case 5495:case 3959:return ke(e,/(image-set\([^]*)/,Ne+"$1$`$1");case 4968:return ke(ke(e,/(.+:)(flex-)?(.*)/,Ne+"box-pack:$3"+rt+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+Ne+e+e;case 4095:case 3583:case 4068:case 2532:return ke(e,/(.+)-inline(.+)/,Ne+"$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(Mt(e)-1-t>6)switch(Qe(e,t+1)){case 109:if(Qe(e,t+4)!==45)break;case 102:return ke(e,/(.+:)(.+)-([^]+)/,"$1"+Ne+"$2-$3$1"+Zr+(Qe(e,t+3)==108?"$3":"$2-$3"))+e;case 115:return~hi(e,"stretch")?Au(ke(e,"stretch","fill-available"),t)+e:e}break;case 4949:if(Qe(e,t+1)!==115)break;case 6444:switch(Qe(e,Mt(e)-3-(~hi(e,"!important")&&10))){case 107:return ke(e,":",":"+Ne)+e;case 101:return ke(e,/(.+:)([^;!]+)(;|!.+)?/,"$1"+Ne+(Qe(e,14)===45?"inline-":"")+"box$3$1"+Ne+"$2$3$1"+rt+"$2box$3")+e}break;case 5936:switch(Qe(e,t+11)){case 114:return Ne+e+rt+ke(e,/[svh]\w+-[tblr]{2}/,"tb")+e;case 108:return Ne+e+rt+ke(e,/[svh]\w+-[tblr]{2}/,"tb-rl")+e;case 45:return Ne+e+rt+ke(e,/[svh]\w+-[tblr]{2}/,"lr")+e}return Ne+e+rt+e+e}return e}var Ob=function(t,n,r,o){if(t.length>-1&&!t.return)switch(t.type){case Ji:t.return=Au(t.value,t.length);break;case ku:return xn([zn(t,{value:ke(t.value,"@","@"+Ne)})],o);case Zi:if(t.length)return db(t.props,function(i){switch(ub(i,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return xn([zn(t,{props:[ke(i,/:(read-\w+)/,":"+Zr+"$1")]})],o);case"::placeholder":return xn([zn(t,{props:[ke(i,/:(plac\w+)/,":"+Ne+"input-$1")]}),zn(t,{props:[ke(i,/:(plac\w+)/,":"+Zr+"$1")]}),zn(t,{props:[ke(i,/:(plac\w+)/,rt+"input-$1")]})],o)}return""})}},Rb=[Ob],zu=function(t){var n=t.key;if(n==="css"){var r=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(r,function(h){var C=h.getAttribute("data-emotion");C.indexOf(" ")!==-1&&(document.head.appendChild(h),h.setAttribute("data-s",""))})}var o=t.stylisPlugins||Rb,i={},a,s=[];a=t.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+n+' "]'),function(h){for(var C=h.getAttribute("data-emotion").split(" "),g=1;g<C.length;g++)i[C[g]]=!0;s.push(h)});var l,c=[Pb,Tb];{var u,f=[xb,Sb(function(h){u.insert(h)})],m=Cb(c.concat(o,f)),p=function(C){return xn(yb(C),m)};l=function(C,g,b,w){u=b,p(C?C+"{"+g.styles+"}":g.styles),w&&(v.inserted[g.name]=!0)}}var v={key:n,sheet:new ob({key:n,container:a,nonce:t.nonce,speedy:t.speedy,prepend:t.prepend,insertionPoint:t.insertionPoint}),nonce:t.nonce,inserted:i,registered:{},insert:l};return v.sheet.hydrate(s),v};function Nb(e){for(var t=0,n,r=0,o=e.length;o>=4;++r,o-=4)n=e.charCodeAt(r)&255|(e.charCodeAt(++r)&255)<<8|(e.charCodeAt(++r)&255)<<16|(e.charCodeAt(++r)&255)<<24,n=(n&65535)*1540483477+((n>>>16)*59797<<16),n^=n>>>24,t=(n&65535)*1540483477+((n>>>16)*59797<<16)^(t&65535)*1540483477+((t>>>16)*59797<<16);switch(o){case 3:t^=(e.charCodeAt(r+2)&255)<<16;case 2:t^=(e.charCodeAt(r+1)&255)<<8;case 1:t^=e.charCodeAt(r)&255,t=(t&65535)*1540483477+((t>>>16)*59797<<16)}return t^=t>>>13,t=(t&65535)*1540483477+((t>>>16)*59797<<16),((t^t>>>15)>>>0).toString(36)}var kb={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},Mb=/[A-Z]|^ms/g,jb=/_EMO_([^_]+?)_([^]*?)_EMO_/g,Du=function(t){return t.charCodeAt(1)===45},Ds=function(t){return t!=null&&typeof t!="boolean"},_o=wb(function(e){return Du(e)?e:e.replace(Mb,"-$&").toLowerCase()}),Bs=function(t,n){switch(t){case"animation":case"animationName":if(typeof n=="string")return n.replace(jb,function(r,o,i){return jt={name:o,styles:i,next:jt},o})}return kb[t]!==1&&!Du(t)&&typeof n=="number"&&n!==0?n+"px":n};function nr(e,t,n){if(n==null)return"";var r=n;if(r.__emotion_styles!==void 0)return r;switch(typeof n){case"boolean":return"";case"object":{var o=n;if(o.anim===1)return jt={name:o.name,styles:o.styles,next:jt},o.name;var i=n;if(i.styles!==void 0){var a=i.next;if(a!==void 0)for(;a!==void 0;)jt={name:a.name,styles:a.styles,next:jt},a=a.next;var s=i.styles+";";return s}return Lb(e,t,n)}case"function":{if(e!==void 0){var l=jt,c=n(e);return jt=l,nr(e,t,c)}break}}var u=n;if(t==null)return u;var f=t[u];return f!==void 0?f:u}function Lb(e,t,n){var r="";if(Array.isArray(n))for(var o=0;o<n.length;o++)r+=nr(e,t,n[o])+";";else for(var i in n){var a=n[i];if(typeof a!="object"){var s=a;t!=null&&t[s]!==void 0?r+=i+"{"+t[s]+"}":Ds(s)&&(r+=_o(i)+":"+Bs(i,s)+";")}else if(Array.isArray(a)&&typeof a[0]=="string"&&(t==null||t[a[0]]===void 0))for(var l=0;l<a.length;l++)Ds(a[l])&&(r+=_o(i)+":"+Bs(i,a[l])+";");else{var c=nr(e,t,a);switch(i){case"animation":case"animationName":{r+=_o(i)+":"+c+";";break}default:r+=i+"{"+c+"}"}}}return r}var Fs=/label:\s*([^\s;{]+)\s*(;|$)/g,jt;function Kt(e,t,n){if(e.length===1&&typeof e[0]=="object"&&e[0]!==null&&e[0].styles!==void 0)return e[0];var r=!0,o="";jt=void 0;var i=e[0];if(i==null||i.raw===void 0)r=!1,o+=nr(n,t,i);else{var a=i;o+=a[0]}for(var s=1;s<e.length;s++)if(o+=nr(n,t,e[s]),r){var l=i;o+=l[s]}Fs.lastIndex=0;for(var c="",u;(u=Fs.exec(o))!==null;)c+="-"+u[1];var f=Nb(o)+c;return{name:f,styles:o,next:jt}}var _b=!0;function fo(e,t,n){var r="";return n.split(" ").forEach(function(o){e[o]!==void 0?t.push(e[o]+";"):o&&(r+=o+" ")}),r}var ea=function(t,n,r){var o=t.key+"-"+n.name;(r===!1||_b===!1)&&t.registered[o]===void 0&&(t.registered[o]=n.styles)},ta=function(t,n,r){ea(t,n,r);var o=t.key+"-"+n.name;if(t.inserted[n.name]===void 0){var i=n;do t.insert(n===i?"."+o:"",i,t.sheet,!0),i=i.next;while(i!==void 0)}};function Hs(e,t){if(e.inserted[t.name]===void 0)return e.insert("",t,e.sheet,!0)}function Ws(e,t,n){var r=[],o=fo(e,r,n);return r.length<2?n:o+t(r)}var na=function(t){var n=zu(t);n.sheet.speedy=function(s){this.isSpeedy=s},n.compat=!0;var r=function(){for(var l=arguments.length,c=new Array(l),u=0;u<l;u++)c[u]=arguments[u];var f=Kt(c,n.registered,void 0);return ta(n,f,!1),n.key+"-"+f.name},o=function(){for(var l=arguments.length,c=new Array(l),u=0;u<l;u++)c[u]=arguments[u];var f=Kt(c,n.registered),m="animation-"+f.name;return Hs(n,{name:f.name,styles:"@keyframes "+m+"{"+f.styles+"}"}),m},i=function(){for(var l=arguments.length,c=new Array(l),u=0;u<l;u++)c[u]=arguments[u];var f=Kt(c,n.registered);Hs(n,f)},a=function(){for(var l=arguments.length,c=new Array(l),u=0;u<l;u++)c[u]=arguments[u];return Ws(n.registered,r,Ab(c))};return{css:r,cx:a,injectGlobal:i,keyframes:o,hydrate:function(l){l.forEach(function(c){n.inserted[c]=!0})},flush:function(){n.registered={},n.inserted={},n.sheet.flush()},sheet:n.sheet,cache:n,getRegisteredStyles:fo.bind(null,n.registered),merge:Ws.bind(null,n.registered,r)}},Ab=function e(t){for(var n="",r=0;r<t.length;r++){var o=t[r];if(o!=null){var i=void 0;switch(typeof o){case"boolean":break;case"object":{if(Array.isArray(o))i=e(o);else{i="";for(var a in o)o[a]&&a&&(i&&(i+=" "),i+=a)}break}default:i=o}i&&(n&&(n+=" "),n+=i)}}return n},Dt=na({key:"css"});Dt.flush;Dt.hydrate;Dt.cx;Dt.merge;Dt.getRegisteredStyles;Dt.injectGlobal;Dt.keyframes;Dt.css;Dt.sheet;var zb=Dt.cache,Db=function(){function e(){zi(this,e),Ee(this,"_cacheList",[zb])}return Di(e,[{key:"add",value:function(n){var r=this.getCache(n.key);return r||(this._cacheList.push(n),n)}},{key:"delete",value:function(n){this._cacheList=this._cacheList.filter(function(r){return r.key!==n.key})}},{key:"hasCache",value:function(n){return this._cacheList.some(function(r){return r.key===n.key})}},{key:"getCache",value:function(n){return this._cacheList.find(function(r){return r.key===n})}},{key:"getCacheList",value:function(){return this._cacheList}}]),e}(),Vs=typeof document<"u",Bu=function(t,n){return"".concat(t,"-").concat(n)},Bb=function(t,n,r,o){var i=o.hashPriority;ea(t,n,r);var a=".".concat(Bu(t.key,n.name)),s=i==="low"?":where(".concat(a,")"):a;if(t.inserted[n.name]===void 0){var l="",c=n;do{var u=t.insert(n===c?s:"",c,t.sheet,!0);!Vs&&u!==void 0&&(l+=u),c=c.next}while(c!==void 0);if(!Vs&&l.length!==0)return l}},ra=function(t){return et(t)==="object"&&"styles"in t&&"name"in t&&"toString"in t},Fb=function e(t){for(var n="",r=0;r<t.length;r++){var o=t[r];if(o!==null){var i=void 0;switch(et(o)){case"boolean":break;case"object":{if(Array.isArray(o))i=e(o);else{i="";for(var a in o)o[a]&&a&&(i&&(i+=" "),i+=a)}break}default:i=o}i&&(n&&(n+=" "),n+=i)}}return n},Hb=function(t,n,r){var o=[],i=fo(t,o,r);return o.length<2?r:i+n(o)},Wb=function(t,n){return function(){for(var r=arguments.length,o=new Array(r),i=0;i<r;i++)o[i]=arguments[i];var a=Kt(o,t.registered,void 0);return Bb(t,a,!1,n),Bu(t.key,a.name)}},Vb=function(t,n){return function(){for(var r=arguments.length,o=new Array(r),i=0;i<r;i++)o[i]=arguments[i];var a=o.map(function(s){return ra(s)?n(s):s});return Hb(t.registered,n,Fb(a))}},Fu=function(t,n){var r=Wb(t,{hashPriority:n.hashPriority||"high",label:n.label}),o=Vb(t,r);return{css:r,cx:o}},fr=function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return Kt(n)},Ub="acss",Xb=function(t){return d.createContext(t)},Ao={exports:{}},Le={};/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Us;function Gb(){if(Us)return Le;Us=1;var e=typeof Symbol=="function"&&Symbol.for,t=e?Symbol.for("react.element"):60103,n=e?Symbol.for("react.portal"):60106,r=e?Symbol.for("react.fragment"):60107,o=e?Symbol.for("react.strict_mode"):60108,i=e?Symbol.for("react.profiler"):60114,a=e?Symbol.for("react.provider"):60109,s=e?Symbol.for("react.context"):60110,l=e?Symbol.for("react.async_mode"):60111,c=e?Symbol.for("react.concurrent_mode"):60111,u=e?Symbol.for("react.forward_ref"):60112,f=e?Symbol.for("react.suspense"):60113,m=e?Symbol.for("react.suspense_list"):60120,p=e?Symbol.for("react.memo"):60115,v=e?Symbol.for("react.lazy"):60116,h=e?Symbol.for("react.block"):60121,C=e?Symbol.for("react.fundamental"):60117,g=e?Symbol.for("react.responder"):60118,b=e?Symbol.for("react.scope"):60119;function w(y){if(typeof y=="object"&&y!==null){var x=y.$$typeof;switch(x){case t:switch(y=y.type,y){case l:case c:case r:case i:case o:case f:return y;default:switch(y=y&&y.$$typeof,y){case s:case u:case v:case p:case a:return y;default:return x}}case n:return x}}}function E(y){return w(y)===c}return Le.AsyncMode=l,Le.ConcurrentMode=c,Le.ContextConsumer=s,Le.ContextProvider=a,Le.Element=t,Le.ForwardRef=u,Le.Fragment=r,Le.Lazy=v,Le.Memo=p,Le.Portal=n,Le.Profiler=i,Le.StrictMode=o,Le.Suspense=f,Le.isAsyncMode=function(y){return E(y)||w(y)===l},Le.isConcurrentMode=E,Le.isContextConsumer=function(y){return w(y)===s},Le.isContextProvider=function(y){return w(y)===a},Le.isElement=function(y){return typeof y=="object"&&y!==null&&y.$$typeof===t},Le.isForwardRef=function(y){return w(y)===u},Le.isFragment=function(y){return w(y)===r},Le.isLazy=function(y){return w(y)===v},Le.isMemo=function(y){return w(y)===p},Le.isPortal=function(y){return w(y)===n},Le.isProfiler=function(y){return w(y)===i},Le.isStrictMode=function(y){return w(y)===o},Le.isSuspense=function(y){return w(y)===f},Le.isValidElementType=function(y){return typeof y=="string"||typeof y=="function"||y===r||y===c||y===i||y===o||y===f||y===m||typeof y=="object"&&y!==null&&(y.$$typeof===v||y.$$typeof===p||y.$$typeof===a||y.$$typeof===s||y.$$typeof===u||y.$$typeof===C||y.$$typeof===g||y.$$typeof===b||y.$$typeof===h)},Le.typeOf=w,Le}var Xs;function qb(){return Xs||(Xs=1,Ao.exports=Gb()),Ao.exports}var zo,Gs;function Yb(){if(Gs)return zo;Gs=1;var e=qb(),t={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},n={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},r={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},o={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},i={};i[e.ForwardRef]=r,i[e.Memo]=o;function a(v){return e.isMemo(v)?o:i[v.$$typeof]||t}var s=Object.defineProperty,l=Object.getOwnPropertyNames,c=Object.getOwnPropertySymbols,u=Object.getOwnPropertyDescriptor,f=Object.getPrototypeOf,m=Object.prototype;function p(v,h,C){if(typeof h!="string"){if(m){var g=f(h);g&&g!==m&&p(v,g,C)}var b=l(h);c&&(b=b.concat(c(h)));for(var w=a(v),E=a(h),y=0;y<b.length;++y){var x=b[y];if(!n[x]&&!(C&&C[x])&&!(E&&E[x])&&!(w&&w[x])){var I=u(h,x);try{s(v,x,I)}catch{}}}}return v}return zo=p,zo}Yb();var Kb=function(t){return t()},Hu=Pa.useInsertionEffect?Pa.useInsertionEffect:!1,Zb=Hu||Kb,qs=Hu||d.useLayoutEffect,Wu=d.createContext(typeof HTMLElement<"u"?zu({key:"css"}):null);Wu.Provider;var Vu=function(t){return d.forwardRef(function(n,r){var o=d.useContext(Wu);return t(n,o,r)})},rr=d.createContext({}),Jb=function(t,n){if(typeof n=="function"){var r=n(t);return r}return ae({},t,n)},Qb=As(function(e){return As(function(t){return Jb(e,t)})}),ex=function(t){var n=d.useContext(rr);return t.theme!==n&&(n=Qb(n)(t.theme)),d.createElement(rr.Provider,{value:n},t.children)},oa={}.hasOwnProperty,vi="__EMOTION_TYPE_PLEASE_DO_NOT_USE__",tx=function(t,n){var r={};for(var o in n)oa.call(n,o)&&(r[o]=n[o]);return r[vi]=t,r},nx=function(t){var n=t.cache,r=t.serialized,o=t.isStringTag;return ea(n,r,o),Zb(function(){return ta(n,r,o)}),null},rx=Vu(function(e,t,n){var r=e.css;typeof r=="string"&&t.registered[r]!==void 0&&(r=t.registered[r]);var o=e[vi],i=[r],a="";typeof e.className=="string"?a=fo(t.registered,i,e.className):e.className!=null&&(a=e.className+" ");var s=Kt(i,void 0,d.useContext(rr));a+=t.key+"-"+s.name;var l={};for(var c in e)oa.call(e,c)&&c!=="css"&&c!==vi&&(l[c]=e[c]);return l.className=a,n&&(l.ref=n),d.createElement(d.Fragment,null,d.createElement(nx,{cache:t,serialized:s,isStringTag:typeof o=="string"}),d.createElement(o,l))}),ox=rx,Ys=function(t,n){var r=arguments;if(n==null||!oa.call(n,"css"))return d.createElement.apply(void 0,r);var o=r.length,i=new Array(o);i[0]=ox,i[1]=tx(t,n);for(var a=2;a<o;a++)i[a]=r[a];return d.createElement.apply(null,i)};(function(e){var t;t||(t=e.JSX||(e.JSX={}))})(Ys||(Ys={}));var ix=Vu(function(e,t){var n=e.styles,r=Kt([n],void 0,d.useContext(rr)),o=d.useRef();return qs(function(){var i=t.key+"-global",a=new t.sheet.constructor({key:i,nonce:t.sheet.nonce,container:t.sheet.container,speedy:t.sheet.isSpeedy}),s=!1,l=document.querySelector('style[data-emotion="'+i+" "+r.name+'"]');return t.sheet.tags.length&&(a.before=t.sheet.tags[0]),l!==null&&(s=!0,l.setAttribute("data-emotion",i),a.hydrate([l])),o.current=[a,s],function(){a.flush()}},[t]),qs(function(){var i=o.current,a=i[0],s=i[1];if(s){i[1]=!1;return}if(r.next!==void 0&&ta(t,r.next,!0),a.tags.length){var l=a.tags[a.tags.length-1].nextElementSibling;a.before=l,a.flush()}t.insert("",r,a,!1)},[t,r.name]),null}),ax=function(t){return function(){for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return d.memo(function(i){var a=t();return A.jsx(ix,{styles:Kt(r,void 0,te(te({},i),{},{theme:a}))})})}},sx=function(t){return function(n){var r=t(n);return function(o){var i=r(o),a=i.styles;return a}}},lx=["children","prefix","speedy","getStyleManager","container","nonce","insertionPoint","stylisPlugins","linters"],cx=function(t){return d.memo(function(n){var r=n.children,o=n.prefix,i=n.speedy,a=n.getStyleManager,s=n.container,l=n.nonce,c=n.insertionPoint,u=n.stylisPlugins,f=n.linters,m=st(n,lx),p=d.useContext(t),v=o??p.sheet.key,h=s??p.sheet.container,C=i??p.sheet.isSpeedy,g=d.useMemo(function(){var w=!1,E=na({speedy:C??w,key:v,container:h,nonce:l,insertionPoint:c,stylisPlugins:u});if(typeof global<"u"){var y=global.__ANTD_STYLE_CACHE_MANAGER_FOR_SSR__;y&&(E.cache=y.add(E.cache))}return E},[v,C,h,l,c,u]);d.useEffect(function(){a==null||a(g)},[g]);var b=A.jsx(t.Provider,{value:g,children:r});return Object.keys(m).length||h?A.jsx(Df,te(te({linters:f,container:h},m),{},{children:b})):b})},ux=function(t){var n=t.css,r=t.token;return{buttonDefaultHover:n({backgroundColor:r.colorBgContainer,border:"1px solid ".concat(r.colorBorder),cursor:"pointer",":hover":{color:r.colorPrimaryHover,borderColor:r.colorPrimaryHover},":active":{color:r.colorPrimaryActive,borderColor:r.colorPrimaryActive}})}},dx=function(t){return Object.fromEntries(Object.entries(t).map(function(n){var r=ve(n,2),o=r[0],i=r[1];return[o,i.styles]}))},ia=function(){var t=nn.useToken(),n=t.token;return n},bn=function(t){return typeof window<"u"?matchMedia&&matchMedia("(prefers-color-scheme: ".concat(t,")")):{matches:!1}},Do,Uu=d.createContext({appearance:"light",setAppearance:function(){},isDarkMode:!1,themeMode:"light",setThemeMode:function(){},browserPrefers:(Do=bn("dark"))!==null&&Do!==void 0&&Do.matches?"dark":"light"}),po=function(){return d.useContext(Uu)},fx=function(){var t=ia(),n=po(),r=n.appearance,o=n.isDarkMode;return d.useMemo(function(){return dx(ux({token:t,css:fr}))},[t,r,o])},Xu=function(){var t=ia(),n=fx();return d.useMemo(function(){return te(te({},t),{},{stylish:n})},[t,n])},px=function(t){return te(te({},t),{},{mobile:t.xs,tablet:t.md,laptop:t.lg,desktop:t.xxl})},mx=function(){var t=ia(),n={xs:"@media (max-width: ".concat(t.screenXSMax,"px)"),sm:"@media (max-width: ".concat(t.screenSMMax,"px)"),md:"@media (max-width: ".concat(t.screenMDMax,"px)"),lg:"@media (max-width: ".concat(t.screenLGMax,"px)"),xl:"@media (max-width: ".concat(t.screenXLMax,"px)"),xxl:"@media (min-width: ".concat(t.screenXXLMin,"px)")};return d.useMemo(function(){return px(n)},[t])},hx=function(t,n){return Object.entries(t).map(function(r){var o=ve(r,2),i=o[0],a=o[1],s=a;return ra(a)||(s=fr(a)),n[i]?"".concat(n[i]," {").concat(s.styles,"}"):""}).join("")},gx=["stylish","appearance","isDarkMode","prefixCls","iconPrefixCls"],vx=["prefixCls","iconPrefixCls"],yx=function(t){var n=t.hashPriority,r=t.useTheme,o=t.EmotionContext;return function(i,a){var s=a==null?void 0:a.__BABEL_FILE_NAME__,l=!!s;return function(c){var u=r(),f=d.useContext(o),m=f.cache,p=Fu(m,{hashPriority:(a==null?void 0:a.hashPriority)||n,label:a==null?void 0:a.label}),v=p.cx,h=p.css,C=mx(),g=d.useMemo(function(){var b;if(i instanceof Function){var w=u.stylish,E=u.appearance,y=u.isDarkMode,x=u.prefixCls,I=u.iconPrefixCls,R=st(u,gx),k=function(O){return hx(O,C)};Object.assign(k,C),b=i({token:R,stylish:w,appearance:E,isDarkMode:y,prefixCls:x,iconPrefixCls:I,cx:v,css:fr,responsive:k},c)}else b=i;return et(b)==="object"&&(ra(b)?b=h(b):b=Object.fromEntries(Object.entries(b).map(function(T){var O=ve(T,2),N=O[0],j=O[1],M=l?"".concat(s,"-").concat(N):void 0;return et(j)==="object"?l?[N,h(j,"label:".concat(M))]:[N,h(j)]:[N,j]}))),b},[c,u]);return d.useMemo(function(){var b=u.prefixCls,w=u.iconPrefixCls,E=st(u,vx);return{styles:g,cx:v,theme:E,prefixCls:b,iconPrefixCls:w}},[g,u])}}},Ks=function(t){if(t.ThemeProvider)return t.ThemeProvider;var n=t.ThemeContext;return function(r){return A.jsx(n.Provider,{value:r.theme,children:r.children})}},bx=ex,Gu=rr,qu=d.memo(function(e){var t=e.children,n=e.theme,r=e.prefixCls,o=e.getStaticInstance,i=e.staticInstanceConfig,a=po(),s=a.appearance,l=a.isDarkMode,c=ct.useMessage(i==null?void 0:i.message),u=ve(c,2),f=u[0],m=u[1],p=uu.useNotification(i==null?void 0:i.notification),v=ve(p,2),h=v[0],C=v[1],g=Nt.useModal(),b=ve(g,2),w=b[0],E=b[1];d.useEffect(function(){o==null||o({message:f,modal:w,notification:h})},[]);var y=d.useMemo(function(){var x=l?nn.darkAlgorithm:nn.defaultAlgorithm,I=n;if(typeof n=="function"&&(I=n(s)),!I)return{algorithm:x};var R=I.algorithm?I.algorithm instanceof Array?I.algorithm:[I.algorithm]:[];return te(te({},I),{},{algorithm:I.algorithm?[x].concat(He(R)):x})},[n,l]);return A.jsxs(En,{prefixCls:r,theme:y,children:[m,C,E,t]})});qu.displayName="AntdProvider";function xx(e,t){var n=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(n!=null){var r,o,i,a,s=[],l=!0,c=!1;try{if(i=(n=n.call(e)).next,t!==0)for(;!(l=(r=i.call(n)).done)&&(s.push(r.value),s.length!==t);l=!0);}catch(u){c=!0,o=u}finally{try{if(!l&&n.return!=null&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw o}}return s}}function Cx(e,t){return Sx(e)||xx(e,t)||wx(e,t)||$x()}function Sx(e){if(Array.isArray(e))return e}function wx(e,t){if(e){if(typeof e=="string")return Zs(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(e);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Zs(e,t)}}function Zs(e,t){(t==null||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function $x(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Js(e,t){var n=t||{},r=n.defaultValue,o=n.value,i=n.onChange,a=n.postState,s=P.useState(function(){return o!==void 0?o:r!==void 0?typeof r=="function"?r():r:e}),l=Cx(s,2),c=l[0],u=l[1],f=o!==void 0?o:c;a&&(f=a(f));function m(p){u(p),f!==p&&i&&i(p,f)}return[f,m]}var Bo=function(t){typeof d.startTransition=="function"?d.startTransition(t):t()},Xt,Ex=function(t){var n=t.themeMode,r=t.setAppearance,o=t.setBrowserPrefers,i=function(){Bo(function(){bn("dark").matches?r("dark"):r("light")})},a=function(){Bo(function(){bn("dark").matches?o("dark"):o("light")})};return d.useLayoutEffect(function(){if(n!=="auto"){Bo(function(){r(n)});return}return setTimeout(i,1),Xt||(Xt=bn("dark")),Xt.addEventListener("change",i),function(){Xt.removeEventListener("change",i)}},[n]),d.useLayoutEffect(function(){return Xt||(Xt=bn("dark")),Xt.addEventListener("change",a),function(){Xt.removeEventListener("change",a)}},[]),null},Yu=d.memo(function(e){var t,n=e.children,r=e.appearance,o=e.defaultAppearance,i=e.onAppearanceChange,a=e.themeMode,s=e.defaultThemeMode,l=e.onThemeModeChange,c=e.useTheme,u=c(),f=u.appearance,m=u.themeMode,p=Js("light",{value:a,defaultValue:s??m,onChange:function(T){return l==null?void 0:l(T)}}),v=ve(p,2),h=v[0],C=v[1],g=Js("light",{value:r,defaultValue:o??f,onChange:function(T){return i==null?void 0:i(T)}}),b=ve(g,2),w=b[0],E=b[1],y=d.useState((t=bn("dark"))!==null&&t!==void 0&&t.matches?"dark":"light"),x=ve(y,2),I=x[0],R=x[1];return A.jsxs(Uu.Provider,{value:{themeMode:h,setThemeMode:C,appearance:w,setAppearance:E,isDarkMode:w==="dark",browserPrefers:I},children:[typeof window<"u"&&A.jsx(Ex,{themeMode:h,setAppearance:E,setBrowserPrefers:R}),n]})});Yu.displayName="ThemeSwitcher";var Ix=["stylish"],Px=function(t){var n=t.children,r=t.customToken,o=t.defaultCustomToken,i=t.customStylish,a=t.prefixCls,s=t.StyledThemeProvider,l=po(),c=l.appearance,u=l.isDarkMode,f=Xu(),m=f.stylish,p=st(f,Ix),v=d.useMemo(function(){return o?o instanceof Function?o({token:p,appearance:c,isDarkMode:u}):o:{}},[o,p,c]),h=d.useMemo(function(){return r instanceof Function?te(te({},v),r({token:p,appearance:c,isDarkMode:u})):te(te({},v),r)},[v,r,p,c]),C=d.useMemo(function(){return i?i({token:te(te({},p),h),stylish:m,appearance:c,isDarkMode:u,css:fr}):{}},[i,p,h,m,c]),g=d.useMemo(function(){return te(te({},C),m)},[C,m]),b=te(te(te(te({},p),h),{},{stylish:g},l),{},{prefixCls:a});return A.jsx(s,{theme:b,children:n})},Tx=function(t){var n=t.styledConfig?Ks(t.styledConfig):void 0,r=t.StyleEngineContext;return d.memo(function(o){var i=o.children,a=o.customToken,s=o.customStylish,l=o.theme,c=o.getStaticInstance,u=o.prefixCls,f=o.staticInstanceConfig,m=o.appearance,p=o.defaultAppearance,v=o.onAppearanceChange,h=o.themeMode,C=o.defaultThemeMode,g=o.onThemeModeChange,b=o.styled,w=d.useContext(r),E=w.prefixCls,y=w.StyledThemeContext,x=w.CustomThemeContext,I=d.useContext(x),R=b?Ks(b):n||bx,k=u||E;return A.jsx(r.Provider,{value:{prefixCls:k,StyledThemeContext:(b==null?void 0:b.ThemeContext)||y||Gu,CustomThemeContext:x},children:A.jsx(Yu,{themeMode:h,defaultThemeMode:C,onThemeModeChange:g,defaultAppearance:p,appearance:m,onAppearanceChange:v,useTheme:t.useTheme,children:A.jsx(qu,{prefixCls:k,staticInstanceConfig:f,theme:l,getStaticInstance:c,children:A.jsx(Px,{prefixCls:k,customToken:a,defaultCustomToken:I,customStylish:s,StyledThemeProvider:R,children:i})})})})})},Ox=function(t){return function(){var n=t.StyleEngineContext,r=d.useContext(n),o=r.StyledThemeContext,i=r.CustomThemeContext,a=r.prefixCls,s=Xu(),l=po(),c=d.useContext(i),u=d.useContext(o??Gu)||{},f=d.useContext(En.ConfigContext),m=f.iconPrefixCls,p=f.getPrefixCls,v=p(),h=a&&a!=="ant"?a:v,C=d.useMemo(function(){return te(te(te(te({},s),l),c),{},{prefixCls:h,iconPrefixCls:m})},[s,l,c,h,m]);return!u||Object.keys(u).length===0?C:te(te({},u),{},{prefixCls:h,iconPrefixCls:m})}},Ku=new Db;typeof global<"u"&&(global.__ANTD_STYLE_CACHE_MANAGER_FOR_SSR__=Ku);var Rx=function(t){var n,r,o,i=te(te({},t),{},{key:(n=t.key)!==null&&n!==void 0?n:"zcss",speedy:(r=t.speedy)!==null&&r!==void 0?r:!1}),a=na({key:i.key,speedy:i.speedy,container:i.container}),s=Xb(a),l=cx(s);a.cache=Ku.add(a.cache);var c=d.createContext(i.customToken?i.customToken:{}),u=(o=i.styled)===null||o===void 0?void 0:o.ThemeContext,f=d.createContext({CustomThemeContext:c,StyledThemeContext:u,prefixCls:i==null?void 0:i.prefixCls,iconPrefixCls:i==null?void 0:i.iconPrefixCls}),m=Ox({StyleEngineContext:f}),p=yx({hashPriority:i.hashPriority,useTheme:m,EmotionContext:s}),v=ax(m),h=sx(p),C=Tx({styledConfig:i.styled,StyleEngineContext:f,useTheme:m});C.displayName="AntdStyleThemeProvider";var g=Fu(a.cache,{hashPriority:i.hashPriority}),b=g.cx,w=a.injectGlobal,E=a.keyframes;return{createStyles:p,createGlobalStyle:v,createStylish:h,css:fr,cx:b,keyframes:E,injectGlobal:w,styleManager:a,useTheme:m,StyleProvider:l,ThemeProvider:C}},Nx=Rx({key:Ub,speedy:!1}),kx=Nx.createStyles;const Zu="/assets/aworld_logo-DnLSZeN8.png",Mx=()=>{const[e,t]=d.useState(""),n=()=>new URLSearchParams(window.location.search).get("agentid")||"",r=i=>{const a=new URL(window.location.href);i?a.searchParams.set("agentid",i):a.searchParams.delete("agentid"),window.history.replaceState({},"",a.toString())},o=i=>{t(i),r(i)};return d.useEffect(()=>{const i=n();i&&t(i)},[]),{agentId:e,setAgentIdAndUpdateURL:o,updateURLAgentId:r}},Je=[];for(let e=0;e<256;++e)Je.push((e+256).toString(16).slice(1));function jx(e,t=0){return(Je[e[t+0]]+Je[e[t+1]]+Je[e[t+2]]+Je[e[t+3]]+"-"+Je[e[t+4]]+Je[e[t+5]]+"-"+Je[e[t+6]]+Je[e[t+7]]+"-"+Je[e[t+8]]+Je[e[t+9]]+"-"+Je[e[t+10]]+Je[e[t+11]]+Je[e[t+12]]+Je[e[t+13]]+Je[e[t+14]]+Je[e[t+15]]).toLowerCase()}let Fo;const Lx=new Uint8Array(16);function _x(){if(!Fo){if(typeof crypto>"u"||!crypto.getRandomValues)throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");Fo=crypto.getRandomValues.bind(crypto)}return Fo(Lx)}const Ax=typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto),Qs={randomUUID:Ax};function zx(e,t,n){var o;if(Qs.randomUUID&&!e)return Qs.randomUUID();e=e||{};const r=e.random??((o=e.rng)==null?void 0:o.call(e))??_x();if(r.length<16)throw new Error("Random bytes length must be >= 16");return r[6]=r[6]&15|64,r[8]=r[8]&63|128,jx(r)}const Dx=()=>{const[e,t]=d.useState(""),n=()=>new URLSearchParams(window.location.search).get("session_id")||"",r=i=>{const a=new URL(window.location.href);a.searchParams.set("session_id",i),window.history.replaceState({},"",a.toString())},o=()=>{const i=zx();return t(i),r(i),console.log("generateNewSessionId",i),i};return d.useEffect(()=>{const i=n();i?t(i):o()},[]),{sessionId:e,setSessionId:t,generateNewSessionId:o,updateURLSessionId:r}},Bx=e=>{const{items:t,onItemClick:n,className:r}=e;return A.jsx(Ru,{items:t,styles:{item:{flex:1,backgroundImage:"linear-gradient(123deg, #e5f4ff 0%, #efe7ff 100%)",borderRadius:12,border:"none"},subItem:{background:"#ffffffa6"}},onItemClick:o=>{n(o.data.description)},className:r||"chatPrompt"})},{Title:Fx}=Ze,Hx=({onSubmit:e,models:t,selectedModel:n,onModelChange:r,modelsLoading:o})=>{const[i,a]=d.useState(""),s=l=>{l.key==="Enter"&&!l.shiftKey&&(l.preventDefault(),i.trim()&&e(i))};return A.jsx("div",{className:"welcome-container",children:A.jsxs("div",{className:"content",children:[A.jsx(qg,{justify:"center",children:A.jsx(Ug,{children:A.jsxs("div",{className:"logo-title-container",children:[A.jsx("img",{src:Zu,alt:"AWorld Logo",width:"46",height:"46"}),A.jsx(Fx,{level:1,style:{margin:0},children:A.jsxs("a",{href:"https://github.com/inclusionAI/AWorld",target:"_blank",rel:"noopener noreferrer",className:"aworld-link",children:["Hello"," ","AWorld"]})})]})})}),A.jsxs("div",{className:"input-area",children:[A.jsx(uc.TextArea,{value:i,onChange:l=>a(l.target.value),onKeyDown:s,placeholder:"Ask or input / use skills",autoSize:{minRows:3,maxRows:5},className:"text-input"}),A.jsx(qe,{type:"primary",shape:"circle",onClick:()=>{i.trim()&&e(i)},icon:A.jsx(dc,{}),className:"submit-button",disabled:i.trim()===""})]}),A.jsx("div",{className:"controls-area",children:A.jsx(Bf,{value:n,onChange:r,options:t,loading:o,placeholder:"Select a model",className:"model-select",showSearch:!0,filterOption:(l,c)=>((c==null?void 0:c.label)??"").toLowerCase().includes(l.toLowerCase()),optionRender:l=>A.jsx("div",{className:"select-item",children:A.jsxs(en,{justify:"space-between",children:[A.jsxs("div",{children:[A.jsx("strong",{children:l.label}),A.jsx("small",{children:l.value})]}),A.jsx(Jn,{className:"icon-right"})]})})})})]})})};function Wx(e,t){const n={};return(e[e.length-1]===""?[...e,""]:e).join((n.padRight?" ":"")+","+(n.padLeft===!1?"":" ")).trim()}const Vx=/^[$_\p{ID_Start}][$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,Ux=/^[$_\p{ID_Start}][-$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,Xx={};function el(e,t){return(Xx.jsx?Ux:Vx).test(e)}const Gx=/[ \t\n\f\r]/g;function qx(e){return typeof e=="object"?e.type==="text"?tl(e.value):!1:tl(e)}function tl(e){return e.replace(Gx,"")===""}class pr{constructor(t,n,r){this.normal=n,this.property=t,r&&(this.space=r)}}pr.prototype.normal={};pr.prototype.property={};pr.prototype.space=void 0;function Ju(e,t){const n={},r={};for(const o of e)Object.assign(n,o.property),Object.assign(r,o.normal);return new pr(n,r,t)}function yi(e){return e.toLowerCase()}class pt{constructor(t,n){this.attribute=n,this.property=t}}pt.prototype.attribute="";pt.prototype.booleanish=!1;pt.prototype.boolean=!1;pt.prototype.commaOrSpaceSeparated=!1;pt.prototype.commaSeparated=!1;pt.prototype.defined=!1;pt.prototype.mustUseProperty=!1;pt.prototype.number=!1;pt.prototype.overloadedBoolean=!1;pt.prototype.property="";pt.prototype.spaceSeparated=!1;pt.prototype.space=void 0;let Yx=0;const Pe=cn(),Ge=cn(),bi=cn(),Q=cn(),De=cn(),Cn=cn(),gt=cn();function cn(){return 2**++Yx}const xi=Object.freeze(Object.defineProperty({__proto__:null,boolean:Pe,booleanish:Ge,commaOrSpaceSeparated:gt,commaSeparated:Cn,number:Q,overloadedBoolean:bi,spaceSeparated:De},Symbol.toStringTag,{value:"Module"})),Ho=Object.keys(xi);class aa extends pt{constructor(t,n,r,o){let i=-1;if(super(t,n),nl(this,"space",o),typeof r=="number")for(;++i<Ho.length;){const a=Ho[i];nl(this,Ho[i],(r&xi[a])===xi[a])}}}aa.prototype.defined=!0;function nl(e,t,n){n&&(e[t]=n)}function On(e){const t={},n={};for(const[r,o]of Object.entries(e.properties)){const i=new aa(r,e.transform(e.attributes||{},r),o,e.space);e.mustUseProperty&&e.mustUseProperty.includes(r)&&(i.mustUseProperty=!0),t[r]=i,n[yi(r)]=r,n[yi(i.attribute)]=r}return new pr(t,n,e.space)}const Qu=On({properties:{ariaActiveDescendant:null,ariaAtomic:Ge,ariaAutoComplete:null,ariaBusy:Ge,ariaChecked:Ge,ariaColCount:Q,ariaColIndex:Q,ariaColSpan:Q,ariaControls:De,ariaCurrent:null,ariaDescribedBy:De,ariaDetails:null,ariaDisabled:Ge,ariaDropEffect:De,ariaErrorMessage:null,ariaExpanded:Ge,ariaFlowTo:De,ariaGrabbed:Ge,ariaHasPopup:null,ariaHidden:Ge,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:De,ariaLevel:Q,ariaLive:null,ariaModal:Ge,ariaMultiLine:Ge,ariaMultiSelectable:Ge,ariaOrientation:null,ariaOwns:De,ariaPlaceholder:null,ariaPosInSet:Q,ariaPressed:Ge,ariaReadOnly:Ge,ariaRelevant:null,ariaRequired:Ge,ariaRoleDescription:De,ariaRowCount:Q,ariaRowIndex:Q,ariaRowSpan:Q,ariaSelected:Ge,ariaSetSize:Q,ariaSort:null,ariaValueMax:Q,ariaValueMin:Q,ariaValueNow:Q,ariaValueText:null,role:null},transform(e,t){return t==="role"?t:"aria-"+t.slice(4).toLowerCase()}});function ed(e,t){return t in e?e[t]:t}function td(e,t){return ed(e,t.toLowerCase())}const Kx=On({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:Cn,acceptCharset:De,accessKey:De,action:null,allow:null,allowFullScreen:Pe,allowPaymentRequest:Pe,allowUserMedia:Pe,alt:null,as:null,async:Pe,autoCapitalize:null,autoComplete:De,autoFocus:Pe,autoPlay:Pe,blocking:De,capture:null,charSet:null,checked:Pe,cite:null,className:De,cols:Q,colSpan:null,content:null,contentEditable:Ge,controls:Pe,controlsList:De,coords:Q|Cn,crossOrigin:null,data:null,dateTime:null,decoding:null,default:Pe,defer:Pe,dir:null,dirName:null,disabled:Pe,download:bi,draggable:Ge,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:Pe,formTarget:null,headers:De,height:Q,hidden:bi,high:Q,href:null,hrefLang:null,htmlFor:De,httpEquiv:De,id:null,imageSizes:null,imageSrcSet:null,inert:Pe,inputMode:null,integrity:null,is:null,isMap:Pe,itemId:null,itemProp:De,itemRef:De,itemScope:Pe,itemType:De,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:Pe,low:Q,manifest:null,max:null,maxLength:Q,media:null,method:null,min:null,minLength:Q,multiple:Pe,muted:Pe,name:null,nonce:null,noModule:Pe,noValidate:Pe,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:Pe,optimum:Q,pattern:null,ping:De,placeholder:null,playsInline:Pe,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:Pe,referrerPolicy:null,rel:De,required:Pe,reversed:Pe,rows:Q,rowSpan:Q,sandbox:De,scope:null,scoped:Pe,seamless:Pe,selected:Pe,shadowRootClonable:Pe,shadowRootDelegatesFocus:Pe,shadowRootMode:null,shape:null,size:Q,sizes:null,slot:null,span:Q,spellCheck:Ge,src:null,srcDoc:null,srcLang:null,srcSet:null,start:Q,step:null,style:null,tabIndex:Q,target:null,title:null,translate:null,type:null,typeMustMatch:Pe,useMap:null,value:Ge,width:Q,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:De,axis:null,background:null,bgColor:null,border:Q,borderColor:null,bottomMargin:Q,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:Pe,declare:Pe,event:null,face:null,frame:null,frameBorder:null,hSpace:Q,leftMargin:Q,link:null,longDesc:null,lowSrc:null,marginHeight:Q,marginWidth:Q,noResize:Pe,noHref:Pe,noShade:Pe,noWrap:Pe,object:null,profile:null,prompt:null,rev:null,rightMargin:Q,rules:null,scheme:null,scrolling:Ge,standby:null,summary:null,text:null,topMargin:Q,valueType:null,version:null,vAlign:null,vLink:null,vSpace:Q,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:Pe,disableRemotePlayback:Pe,prefix:null,property:null,results:Q,security:null,unselectable:null},space:"html",transform:td}),Zx=On({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:gt,accentHeight:Q,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:Q,amplitude:Q,arabicForm:null,ascent:Q,attributeName:null,attributeType:null,azimuth:Q,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:Q,by:null,calcMode:null,capHeight:Q,className:De,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:Q,diffuseConstant:Q,direction:null,display:null,dur:null,divisor:Q,dominantBaseline:null,download:Pe,dx:null,dy:null,edgeMode:null,editable:null,elevation:Q,enableBackground:null,end:null,event:null,exponent:Q,externalResourcesRequired:null,fill:null,fillOpacity:Q,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:Cn,g2:Cn,glyphName:Cn,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:Q,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:Q,horizOriginX:Q,horizOriginY:Q,id:null,ideographic:Q,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:Q,k:Q,k1:Q,k2:Q,k3:Q,k4:Q,kernelMatrix:gt,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:Q,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:Q,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:Q,overlineThickness:Q,paintOrder:null,panose1:null,path:null,pathLength:Q,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:De,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:Q,pointsAtY:Q,pointsAtZ:Q,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:gt,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:gt,rev:gt,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:gt,requiredFeatures:gt,requiredFonts:gt,requiredFormats:gt,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:Q,specularExponent:Q,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:Q,strikethroughThickness:Q,string:null,stroke:null,strokeDashArray:gt,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:Q,strokeOpacity:Q,strokeWidth:null,style:null,surfaceScale:Q,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:gt,tabIndex:Q,tableValues:null,target:null,targetX:Q,targetY:Q,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:gt,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:Q,underlineThickness:Q,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:Q,values:null,vAlphabetic:Q,vMathematical:Q,vectorEffect:null,vHanging:Q,vIdeographic:Q,version:null,vertAdvY:Q,vertOriginX:Q,vertOriginY:Q,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:Q,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:ed}),nd=On({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform(e,t){return"xlink:"+t.slice(5).toLowerCase()}}),rd=On({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:td}),od=On({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform(e,t){return"xml:"+t.slice(3).toLowerCase()}}),Jx={classId:"classID",dataType:"datatype",itemId:"itemID",strokeDashArray:"strokeDasharray",strokeDashOffset:"strokeDashoffset",strokeLineCap:"strokeLinecap",strokeLineJoin:"strokeLinejoin",strokeMiterLimit:"strokeMiterlimit",typeOf:"typeof",xLinkActuate:"xlinkActuate",xLinkArcRole:"xlinkArcrole",xLinkHref:"xlinkHref",xLinkRole:"xlinkRole",xLinkShow:"xlinkShow",xLinkTitle:"xlinkTitle",xLinkType:"xlinkType",xmlnsXLink:"xmlnsXlink"},Qx=/[A-Z]/g,rl=/-[a-z]/g,eC=/^data[-\w.:]+$/i;function tC(e,t){const n=yi(t);let r=t,o=pt;if(n in e.normal)return e.property[e.normal[n]];if(n.length>4&&n.slice(0,4)==="data"&&eC.test(t)){if(t.charAt(4)==="-"){const i=t.slice(5).replace(rl,rC);r="data"+i.charAt(0).toUpperCase()+i.slice(1)}else{const i=t.slice(4);if(!rl.test(i)){let a=i.replace(Qx,nC);a.charAt(0)!=="-"&&(a="-"+a),t="data"+a}}o=aa}return new o(r,t)}function nC(e){return"-"+e.toLowerCase()}function rC(e){return e.charAt(1).toUpperCase()}const oC=Ju([Qu,Kx,nd,rd,od],"html"),sa=Ju([Qu,Zx,nd,rd,od],"svg");function iC(e){return e.join(" ").trim()}var hn={},Wo,ol;function aC(){if(ol)return Wo;ol=1;var e=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,t=/\n/g,n=/^\s*/,r=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,o=/^:\s*/,i=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,a=/^[;\s]*/,s=/^\s+|\s+$/g,l=`
`,c="/",u="*",f="",m="comment",p="declaration";Wo=function(h,C){if(typeof h!="string")throw new TypeError("First argument must be a string");if(!h)return[];C=C||{};var g=1,b=1;function w(j){var M=j.match(t);M&&(g+=M.length);var L=j.lastIndexOf(l);b=~L?j.length-L:b+j.length}function E(){var j={line:g,column:b};return function(M){return M.position=new y(j),R(),M}}function y(j){this.start=j,this.end={line:g,column:b},this.source=C.source}y.prototype.content=h;function x(j){var M=new Error(C.source+":"+g+":"+b+": "+j);if(M.reason=j,M.filename=C.source,M.line=g,M.column=b,M.source=h,!C.silent)throw M}function I(j){var M=j.exec(h);if(M){var L=M[0];return w(L),h=h.slice(L.length),M}}function R(){I(n)}function k(j){var M;for(j=j||[];M=T();)M!==!1&&j.push(M);return j}function T(){var j=E();if(!(c!=h.charAt(0)||u!=h.charAt(1))){for(var M=2;f!=h.charAt(M)&&(u!=h.charAt(M)||c!=h.charAt(M+1));)++M;if(M+=2,f===h.charAt(M-1))return x("End of comment missing");var L=h.slice(2,M-2);return b+=2,w(L),h=h.slice(M),b+=2,j({type:m,comment:L})}}function O(){var j=E(),M=I(r);if(M){if(T(),!I(o))return x("property missing ':'");var L=I(i),z=j({type:p,property:v(M[0].replace(e,f)),value:L?v(L[0].replace(e,f)):f});return I(a),z}}function N(){var j=[];k(j);for(var M;M=O();)M!==!1&&(j.push(M),k(j));return j}return R(),N()};function v(h){return h?h.replace(s,f):f}return Wo}var il;function sC(){if(il)return hn;il=1;var e=hn&&hn.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(hn,"__esModule",{value:!0}),hn.default=n;var t=e(aC());function n(r,o){var i=null;if(!r||typeof r!="string")return i;var a=(0,t.default)(r),s=typeof o=="function";return a.forEach(function(l){if(l.type==="declaration"){var c=l.property,u=l.value;s?o(c,u,l):u&&(i=i||{},i[c]=u)}}),i}return hn}var Dn={},al;function lC(){if(al)return Dn;al=1,Object.defineProperty(Dn,"__esModule",{value:!0}),Dn.camelCase=void 0;var e=/^--[a-zA-Z0-9_-]+$/,t=/-([a-z])/g,n=/^[^-]+$/,r=/^-(webkit|moz|ms|o|khtml)-/,o=/^-(ms)-/,i=function(c){return!c||n.test(c)||e.test(c)},a=function(c,u){return u.toUpperCase()},s=function(c,u){return"".concat(u,"-")},l=function(c,u){return u===void 0&&(u={}),i(c)?c:(c=c.toLowerCase(),u.reactCompat?c=c.replace(o,s):c=c.replace(r,s),c.replace(t,a))};return Dn.camelCase=l,Dn}var Bn,sl;function cC(){if(sl)return Bn;sl=1;var e=Bn&&Bn.__importDefault||function(o){return o&&o.__esModule?o:{default:o}},t=e(sC()),n=lC();function r(o,i){var a={};return!o||typeof o!="string"||(0,t.default)(o,function(s,l){s&&l&&(a[(0,n.camelCase)(s,i)]=l)}),a}return r.default=r,Bn=r,Bn}var uC=cC();const dC=Ti(uC),id=ad("end"),la=ad("start");function ad(e){return t;function t(n){const r=n&&n.position&&n.position[e]||{};if(typeof r.line=="number"&&r.line>0&&typeof r.column=="number"&&r.column>0)return{line:r.line,column:r.column,offset:typeof r.offset=="number"&&r.offset>-1?r.offset:void 0}}}function fC(e){const t=la(e),n=id(e);if(t&&n)return{start:t,end:n}}function Xn(e){return!e||typeof e!="object"?"":"position"in e||"type"in e?ll(e.position):"start"in e||"end"in e?ll(e):"line"in e||"column"in e?Ci(e):""}function Ci(e){return cl(e&&e.line)+":"+cl(e&&e.column)}function ll(e){return Ci(e&&e.start)+"-"+Ci(e&&e.end)}function cl(e){return e&&typeof e=="number"?e:1}class lt extends Error{constructor(t,n,r){super(),typeof n=="string"&&(r=n,n=void 0);let o="",i={},a=!1;if(n&&("line"in n&&"column"in n?i={place:n}:"start"in n&&"end"in n?i={place:n}:"type"in n?i={ancestors:[n],place:n.position}:i={...n}),typeof t=="string"?o=t:!i.cause&&t&&(a=!0,o=t.message,i.cause=t),!i.ruleId&&!i.source&&typeof r=="string"){const l=r.indexOf(":");l===-1?i.ruleId=r:(i.source=r.slice(0,l),i.ruleId=r.slice(l+1))}if(!i.place&&i.ancestors&&i.ancestors){const l=i.ancestors[i.ancestors.length-1];l&&(i.place=l.position)}const s=i.place&&"start"in i.place?i.place.start:i.place;this.ancestors=i.ancestors||void 0,this.cause=i.cause||void 0,this.column=s?s.column:void 0,this.fatal=void 0,this.file,this.message=o,this.line=s?s.line:void 0,this.name=Xn(i.place)||"1:1",this.place=i.place||void 0,this.reason=this.message,this.ruleId=i.ruleId||void 0,this.source=i.source||void 0,this.stack=a&&i.cause&&typeof i.cause.stack=="string"?i.cause.stack:"",this.actual,this.expected,this.note,this.url}}lt.prototype.file="";lt.prototype.name="";lt.prototype.reason="";lt.prototype.message="";lt.prototype.stack="";lt.prototype.column=void 0;lt.prototype.line=void 0;lt.prototype.ancestors=void 0;lt.prototype.cause=void 0;lt.prototype.fatal=void 0;lt.prototype.place=void 0;lt.prototype.ruleId=void 0;lt.prototype.source=void 0;const ca={}.hasOwnProperty,pC=new Map,mC=/[A-Z]/g,hC=new Set(["table","tbody","thead","tfoot","tr"]),gC=new Set(["td","th"]),sd="https://github.com/syntax-tree/hast-util-to-jsx-runtime";function vC(e,t){if(!t||t.Fragment===void 0)throw new TypeError("Expected `Fragment` in options");const n=t.filePath||void 0;let r;if(t.development){if(typeof t.jsxDEV!="function")throw new TypeError("Expected `jsxDEV` in options when `development: true`");r=EC(n,t.jsxDEV)}else{if(typeof t.jsx!="function")throw new TypeError("Expected `jsx` in production options");if(typeof t.jsxs!="function")throw new TypeError("Expected `jsxs` in production options");r=$C(n,t.jsx,t.jsxs)}const o={Fragment:t.Fragment,ancestors:[],components:t.components||{},create:r,elementAttributeNameCase:t.elementAttributeNameCase||"react",evaluater:t.createEvaluater?t.createEvaluater():void 0,filePath:n,ignoreInvalidStyle:t.ignoreInvalidStyle||!1,passKeys:t.passKeys!==!1,passNode:t.passNode||!1,schema:t.space==="svg"?sa:oC,stylePropertyNameCase:t.stylePropertyNameCase||"dom",tableCellAlignToStyle:t.tableCellAlignToStyle!==!1},i=ld(o,e,void 0);return i&&typeof i!="string"?i:o.create(e,o.Fragment,{children:i||void 0},void 0)}function ld(e,t,n){if(t.type==="element")return yC(e,t,n);if(t.type==="mdxFlowExpression"||t.type==="mdxTextExpression")return bC(e,t);if(t.type==="mdxJsxFlowElement"||t.type==="mdxJsxTextElement")return CC(e,t,n);if(t.type==="mdxjsEsm")return xC(e,t);if(t.type==="root")return SC(e,t,n);if(t.type==="text")return wC(e,t)}function yC(e,t,n){const r=e.schema;let o=r;t.tagName.toLowerCase()==="svg"&&r.space==="html"&&(o=sa,e.schema=o),e.ancestors.push(t);const i=ud(e,t.tagName,!1),a=IC(e,t);let s=da(e,t);return hC.has(t.tagName)&&(s=s.filter(function(l){return typeof l=="string"?!qx(l):!0})),cd(e,a,i,t),ua(a,s),e.ancestors.pop(),e.schema=r,e.create(t,i,a,n)}function bC(e,t){if(t.data&&t.data.estree&&e.evaluater){const r=t.data.estree.body[0];return r.type,e.evaluater.evaluateExpression(r.expression)}or(e,t.position)}function xC(e,t){if(t.data&&t.data.estree&&e.evaluater)return e.evaluater.evaluateProgram(t.data.estree);or(e,t.position)}function CC(e,t,n){const r=e.schema;let o=r;t.name==="svg"&&r.space==="html"&&(o=sa,e.schema=o),e.ancestors.push(t);const i=t.name===null?e.Fragment:ud(e,t.name,!0),a=PC(e,t),s=da(e,t);return cd(e,a,i,t),ua(a,s),e.ancestors.pop(),e.schema=r,e.create(t,i,a,n)}function SC(e,t,n){const r={};return ua(r,da(e,t)),e.create(t,e.Fragment,r,n)}function wC(e,t){return t.value}function cd(e,t,n,r){typeof n!="string"&&n!==e.Fragment&&e.passNode&&(t.node=r)}function ua(e,t){if(t.length>0){const n=t.length>1?t:t[0];n&&(e.children=n)}}function $C(e,t,n){return r;function r(o,i,a,s){const c=Array.isArray(a.children)?n:t;return s?c(i,a,s):c(i,a)}}function EC(e,t){return n;function n(r,o,i,a){const s=Array.isArray(i.children),l=la(r);return t(o,i,a,s,{columnNumber:l?l.column-1:void 0,fileName:e,lineNumber:l?l.line:void 0},void 0)}}function IC(e,t){const n={};let r,o;for(o in t.properties)if(o!=="children"&&ca.call(t.properties,o)){const i=TC(e,o,t.properties[o]);if(i){const[a,s]=i;e.tableCellAlignToStyle&&a==="align"&&typeof s=="string"&&gC.has(t.tagName)?r=s:n[a]=s}}if(r){const i=n.style||(n.style={});i[e.stylePropertyNameCase==="css"?"text-align":"textAlign"]=r}return n}function PC(e,t){const n={};for(const r of t.attributes)if(r.type==="mdxJsxExpressionAttribute")if(r.data&&r.data.estree&&e.evaluater){const i=r.data.estree.body[0];i.type;const a=i.expression;a.type;const s=a.properties[0];s.type,Object.assign(n,e.evaluater.evaluateExpression(s.argument))}else or(e,t.position);else{const o=r.name;let i;if(r.value&&typeof r.value=="object")if(r.value.data&&r.value.data.estree&&e.evaluater){const s=r.value.data.estree.body[0];s.type,i=e.evaluater.evaluateExpression(s.expression)}else or(e,t.position);else i=r.value===null?!0:r.value;n[o]=i}return n}function da(e,t){const n=[];let r=-1;const o=e.passKeys?new Map:pC;for(;++r<t.children.length;){const i=t.children[r];let a;if(e.passKeys){const l=i.type==="element"?i.tagName:i.type==="mdxJsxFlowElement"||i.type==="mdxJsxTextElement"?i.name:void 0;if(l){const c=o.get(l)||0;a=l+"-"+c,o.set(l,c+1)}}const s=ld(e,i,a);s!==void 0&&n.push(s)}return n}function TC(e,t,n){const r=tC(e.schema,t);if(!(n==null||typeof n=="number"&&Number.isNaN(n))){if(Array.isArray(n)&&(n=r.commaSeparated?Wx(n):iC(n)),r.property==="style"){let o=typeof n=="object"?n:OC(e,String(n));return e.stylePropertyNameCase==="css"&&(o=RC(o)),["style",o]}return[e.elementAttributeNameCase==="react"&&r.space?Jx[r.property]||r.property:r.attribute,n]}}function OC(e,t){try{return dC(t,{reactCompat:!0})}catch(n){if(e.ignoreInvalidStyle)return{};const r=n,o=new lt("Cannot parse `style` attribute",{ancestors:e.ancestors,cause:r,ruleId:"style",source:"hast-util-to-jsx-runtime"});throw o.file=e.filePath||void 0,o.url=sd+"#cannot-parse-style-attribute",o}}function ud(e,t,n){let r;if(!n)r={type:"Literal",value:t};else if(t.includes(".")){const o=t.split(".");let i=-1,a;for(;++i<o.length;){const s=el(o[i])?{type:"Identifier",name:o[i]}:{type:"Literal",value:o[i]};a=a?{type:"MemberExpression",object:a,property:s,computed:!!(i&&s.type==="Literal"),optional:!1}:s}r=a}else r=el(t)&&!/^[a-z]/.test(t)?{type:"Identifier",name:t}:{type:"Literal",value:t};if(r.type==="Literal"){const o=r.value;return ca.call(e.components,o)?e.components[o]:o}if(e.evaluater)return e.evaluater.evaluateExpression(r);or(e)}function or(e,t){const n=new lt("Cannot handle MDX estrees without `createEvaluater`",{ancestors:e.ancestors,place:t,ruleId:"mdx-estree",source:"hast-util-to-jsx-runtime"});throw n.file=e.filePath||void 0,n.url=sd+"#cannot-handle-mdx-estrees-without-createevaluater",n}function RC(e){const t={};let n;for(n in e)ca.call(e,n)&&(t[NC(n)]=e[n]);return t}function NC(e){let t=e.replace(mC,kC);return t.slice(0,3)==="ms-"&&(t="-"+t),t}function kC(e){return"-"+e.toLowerCase()}const Vo={action:["form"],cite:["blockquote","del","ins","q"],data:["object"],formAction:["button","input"],href:["a","area","base","link"],icon:["menuitem"],itemId:null,manifest:["html"],ping:["a","area"],poster:["video"],src:["audio","embed","iframe","img","input","script","source","track","video"]},MC={};function jC(e,t){const n=MC,r=typeof n.includeImageAlt=="boolean"?n.includeImageAlt:!0,o=typeof n.includeHtml=="boolean"?n.includeHtml:!0;return dd(e,r,o)}function dd(e,t,n){if(LC(e)){if("value"in e)return e.type==="html"&&!n?"":e.value;if(t&&"alt"in e&&e.alt)return e.alt;if("children"in e)return ul(e.children,t,n)}return Array.isArray(e)?ul(e,t,n):""}function ul(e,t,n){const r=[];let o=-1;for(;++o<e.length;)r[o]=dd(e[o],t,n);return r.join("")}function LC(e){return!!(e&&typeof e=="object")}const dl=document.createElement("i");function fa(e){const t="&"+e+";";dl.innerHTML=t;const n=dl.textContent;return n.charCodeAt(n.length-1)===59&&e!=="semi"||n===t?!1:n}function zt(e,t,n,r){const o=e.length;let i=0,a;if(t<0?t=-t>o?0:o+t:t=t>o?o:t,n=n>0?n:0,r.length<1e4)a=Array.from(r),a.unshift(t,n),e.splice(...a);else for(n&&e.splice(t,n);i<r.length;)a=r.slice(i,i+1e4),a.unshift(t,0),e.splice(...a),i+=1e4,t+=1e4}function Et(e,t){return e.length>0?(zt(e,e.length,0,t),e):t}const fl={}.hasOwnProperty;function _C(e){const t={};let n=-1;for(;++n<e.length;)AC(t,e[n]);return t}function AC(e,t){let n;for(n in t){const o=(fl.call(e,n)?e[n]:void 0)||(e[n]={}),i=t[n];let a;if(i)for(a in i){fl.call(o,a)||(o[a]=[]);const s=i[a];zC(o[a],Array.isArray(s)?s:s?[s]:[])}}}function zC(e,t){let n=-1;const r=[];for(;++n<t.length;)(t[n].add==="after"?e:r).push(t[n]);zt(e,0,0,r)}function fd(e,t){const n=Number.parseInt(e,t);return n<9||n===11||n>13&&n<32||n>126&&n<160||n>55295&&n<57344||n>64975&&n<65008||(n&65535)===65535||(n&65535)===65534||n>1114111?"�":String.fromCodePoint(n)}function Sn(e){return e.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}const Lt=Zt(/[A-Za-z]/),vt=Zt(/[\dA-Za-z]/),DC=Zt(/[#-'*+\--9=?A-Z^-~]/);function Si(e){return e!==null&&(e<32||e===127)}const wi=Zt(/\d/),BC=Zt(/[\dA-Fa-f]/),FC=Zt(/[!-/:-@[-`{-~]/);function we(e){return e!==null&&e<-2}function dt(e){return e!==null&&(e<0||e===32)}function Me(e){return e===-2||e===-1||e===32}const HC=Zt(new RegExp("\\p{P}|\\p{S}","u")),WC=Zt(/\s/);function Zt(e){return t;function t(n){return n!==null&&n>-1&&e.test(String.fromCharCode(n))}}function Rn(e){const t=[];let n=-1,r=0,o=0;for(;++n<e.length;){const i=e.charCodeAt(n);let a="";if(i===37&&vt(e.charCodeAt(n+1))&&vt(e.charCodeAt(n+2)))o=2;else if(i<128)/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(i))||(a=String.fromCharCode(i));else if(i>55295&&i<57344){const s=e.charCodeAt(n+1);i<56320&&s>56319&&s<57344?(a=String.fromCharCode(i,s),o=1):a="�"}else a=String.fromCharCode(i);a&&(t.push(e.slice(r,n),encodeURIComponent(a)),r=n+o+1,a=""),o&&(n+=o,o=0)}return t.join("")+e.slice(r)}function Be(e,t,n,r){const o=r?r-1:Number.POSITIVE_INFINITY;let i=0;return a;function a(l){return Me(l)?(e.enter(n),s(l)):t(l)}function s(l){return Me(l)&&i++<o?(e.consume(l),s):(e.exit(n),t(l))}}const VC={tokenize:UC};function UC(e){const t=e.attempt(this.parser.constructs.contentInitial,r,o);let n;return t;function r(s){if(s===null){e.consume(s);return}return e.enter("lineEnding"),e.consume(s),e.exit("lineEnding"),Be(e,t,"linePrefix")}function o(s){return e.enter("paragraph"),i(s)}function i(s){const l=e.enter("chunkText",{contentType:"text",previous:n});return n&&(n.next=l),n=l,a(s)}function a(s){if(s===null){e.exit("chunkText"),e.exit("paragraph"),e.consume(s);return}return we(s)?(e.consume(s),e.exit("chunkText"),i):(e.consume(s),a)}}const XC={tokenize:GC},pl={tokenize:qC};function GC(e){const t=this,n=[];let r=0,o,i,a;return s;function s(w){if(r<n.length){const E=n[r];return t.containerState=E[1],e.attempt(E[0].continuation,l,c)(w)}return c(w)}function l(w){if(r++,t.containerState._closeFlow){t.containerState._closeFlow=void 0,o&&b();const E=t.events.length;let y=E,x;for(;y--;)if(t.events[y][0]==="exit"&&t.events[y][1].type==="chunkFlow"){x=t.events[y][1].end;break}g(r);let I=E;for(;I<t.events.length;)t.events[I][1].end={...x},I++;return zt(t.events,y+1,0,t.events.slice(E)),t.events.length=I,c(w)}return s(w)}function c(w){if(r===n.length){if(!o)return m(w);if(o.currentConstruct&&o.currentConstruct.concrete)return v(w);t.interrupt=!!(o.currentConstruct&&!o._gfmTableDynamicInterruptHack)}return t.containerState={},e.check(pl,u,f)(w)}function u(w){return o&&b(),g(r),m(w)}function f(w){return t.parser.lazy[t.now().line]=r!==n.length,a=t.now().offset,v(w)}function m(w){return t.containerState={},e.attempt(pl,p,v)(w)}function p(w){return r++,n.push([t.currentConstruct,t.containerState]),m(w)}function v(w){if(w===null){o&&b(),g(0),e.consume(w);return}return o=o||t.parser.flow(t.now()),e.enter("chunkFlow",{_tokenizer:o,contentType:"flow",previous:i}),h(w)}function h(w){if(w===null){C(e.exit("chunkFlow"),!0),g(0),e.consume(w);return}return we(w)?(e.consume(w),C(e.exit("chunkFlow")),r=0,t.interrupt=void 0,s):(e.consume(w),h)}function C(w,E){const y=t.sliceStream(w);if(E&&y.push(null),w.previous=i,i&&(i.next=w),i=w,o.defineSkip(w.start),o.write(y),t.parser.lazy[w.start.line]){let x=o.events.length;for(;x--;)if(o.events[x][1].start.offset<a&&(!o.events[x][1].end||o.events[x][1].end.offset>a))return;const I=t.events.length;let R=I,k,T;for(;R--;)if(t.events[R][0]==="exit"&&t.events[R][1].type==="chunkFlow"){if(k){T=t.events[R][1].end;break}k=!0}for(g(r),x=I;x<t.events.length;)t.events[x][1].end={...T},x++;zt(t.events,R+1,0,t.events.slice(I)),t.events.length=x}}function g(w){let E=n.length;for(;E-- >w;){const y=n[E];t.containerState=y[1],y[0].exit.call(t,e)}n.length=w}function b(){o.write([null]),i=void 0,o=void 0,t.containerState._closeFlow=void 0}}function qC(e,t,n){return Be(e,e.attempt(this.parser.constructs.document,t,n),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}function ml(e){if(e===null||dt(e)||WC(e))return 1;if(HC(e))return 2}function pa(e,t,n){const r=[];let o=-1;for(;++o<e.length;){const i=e[o].resolveAll;i&&!r.includes(i)&&(t=i(t,n),r.push(i))}return t}const $i={name:"attention",resolveAll:YC,tokenize:KC};function YC(e,t){let n=-1,r,o,i,a,s,l,c,u;for(;++n<e.length;)if(e[n][0]==="enter"&&e[n][1].type==="attentionSequence"&&e[n][1]._close){for(r=n;r--;)if(e[r][0]==="exit"&&e[r][1].type==="attentionSequence"&&e[r][1]._open&&t.sliceSerialize(e[r][1]).charCodeAt(0)===t.sliceSerialize(e[n][1]).charCodeAt(0)){if((e[r][1]._close||e[n][1]._open)&&(e[n][1].end.offset-e[n][1].start.offset)%3&&!((e[r][1].end.offset-e[r][1].start.offset+e[n][1].end.offset-e[n][1].start.offset)%3))continue;l=e[r][1].end.offset-e[r][1].start.offset>1&&e[n][1].end.offset-e[n][1].start.offset>1?2:1;const f={...e[r][1].end},m={...e[n][1].start};hl(f,-l),hl(m,l),a={type:l>1?"strongSequence":"emphasisSequence",start:f,end:{...e[r][1].end}},s={type:l>1?"strongSequence":"emphasisSequence",start:{...e[n][1].start},end:m},i={type:l>1?"strongText":"emphasisText",start:{...e[r][1].end},end:{...e[n][1].start}},o={type:l>1?"strong":"emphasis",start:{...a.start},end:{...s.end}},e[r][1].end={...a.start},e[n][1].start={...s.end},c=[],e[r][1].end.offset-e[r][1].start.offset&&(c=Et(c,[["enter",e[r][1],t],["exit",e[r][1],t]])),c=Et(c,[["enter",o,t],["enter",a,t],["exit",a,t],["enter",i,t]]),c=Et(c,pa(t.parser.constructs.insideSpan.null,e.slice(r+1,n),t)),c=Et(c,[["exit",i,t],["enter",s,t],["exit",s,t],["exit",o,t]]),e[n][1].end.offset-e[n][1].start.offset?(u=2,c=Et(c,[["enter",e[n][1],t],["exit",e[n][1],t]])):u=0,zt(e,r-1,n-r+3,c),n=r+c.length-u-2;break}}for(n=-1;++n<e.length;)e[n][1].type==="attentionSequence"&&(e[n][1].type="data");return e}function KC(e,t){const n=this.parser.constructs.attentionMarkers.null,r=this.previous,o=ml(r);let i;return a;function a(l){return i=l,e.enter("attentionSequence"),s(l)}function s(l){if(l===i)return e.consume(l),s;const c=e.exit("attentionSequence"),u=ml(l),f=!u||u===2&&o||n.includes(l),m=!o||o===2&&u||n.includes(r);return c._open=!!(i===42?f:f&&(o||!m)),c._close=!!(i===42?m:m&&(u||!f)),t(l)}}function hl(e,t){e.column+=t,e.offset+=t,e._bufferIndex+=t}const ZC={name:"autolink",tokenize:JC};function JC(e,t,n){let r=0;return o;function o(p){return e.enter("autolink"),e.enter("autolinkMarker"),e.consume(p),e.exit("autolinkMarker"),e.enter("autolinkProtocol"),i}function i(p){return Lt(p)?(e.consume(p),a):p===64?n(p):c(p)}function a(p){return p===43||p===45||p===46||vt(p)?(r=1,s(p)):c(p)}function s(p){return p===58?(e.consume(p),r=0,l):(p===43||p===45||p===46||vt(p))&&r++<32?(e.consume(p),s):(r=0,c(p))}function l(p){return p===62?(e.exit("autolinkProtocol"),e.enter("autolinkMarker"),e.consume(p),e.exit("autolinkMarker"),e.exit("autolink"),t):p===null||p===32||p===60||Si(p)?n(p):(e.consume(p),l)}function c(p){return p===64?(e.consume(p),u):DC(p)?(e.consume(p),c):n(p)}function u(p){return vt(p)?f(p):n(p)}function f(p){return p===46?(e.consume(p),r=0,u):p===62?(e.exit("autolinkProtocol").type="autolinkEmail",e.enter("autolinkMarker"),e.consume(p),e.exit("autolinkMarker"),e.exit("autolink"),t):m(p)}function m(p){if((p===45||vt(p))&&r++<63){const v=p===45?m:f;return e.consume(p),v}return n(p)}}const mo={partial:!0,tokenize:QC};function QC(e,t,n){return r;function r(i){return Me(i)?Be(e,o,"linePrefix")(i):o(i)}function o(i){return i===null||we(i)?t(i):n(i)}}const pd={continuation:{tokenize:tS},exit:nS,name:"blockQuote",tokenize:eS};function eS(e,t,n){const r=this;return o;function o(a){if(a===62){const s=r.containerState;return s.open||(e.enter("blockQuote",{_container:!0}),s.open=!0),e.enter("blockQuotePrefix"),e.enter("blockQuoteMarker"),e.consume(a),e.exit("blockQuoteMarker"),i}return n(a)}function i(a){return Me(a)?(e.enter("blockQuotePrefixWhitespace"),e.consume(a),e.exit("blockQuotePrefixWhitespace"),e.exit("blockQuotePrefix"),t):(e.exit("blockQuotePrefix"),t(a))}}function tS(e,t,n){const r=this;return o;function o(a){return Me(a)?Be(e,i,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(a):i(a)}function i(a){return e.attempt(pd,t,n)(a)}}function nS(e){e.exit("blockQuote")}const md={name:"characterEscape",tokenize:rS};function rS(e,t,n){return r;function r(i){return e.enter("characterEscape"),e.enter("escapeMarker"),e.consume(i),e.exit("escapeMarker"),o}function o(i){return FC(i)?(e.enter("characterEscapeValue"),e.consume(i),e.exit("characterEscapeValue"),e.exit("characterEscape"),t):n(i)}}const hd={name:"characterReference",tokenize:oS};function oS(e,t,n){const r=this;let o=0,i,a;return s;function s(f){return e.enter("characterReference"),e.enter("characterReferenceMarker"),e.consume(f),e.exit("characterReferenceMarker"),l}function l(f){return f===35?(e.enter("characterReferenceMarkerNumeric"),e.consume(f),e.exit("characterReferenceMarkerNumeric"),c):(e.enter("characterReferenceValue"),i=31,a=vt,u(f))}function c(f){return f===88||f===120?(e.enter("characterReferenceMarkerHexadecimal"),e.consume(f),e.exit("characterReferenceMarkerHexadecimal"),e.enter("characterReferenceValue"),i=6,a=BC,u):(e.enter("characterReferenceValue"),i=7,a=wi,u(f))}function u(f){if(f===59&&o){const m=e.exit("characterReferenceValue");return a===vt&&!fa(r.sliceSerialize(m))?n(f):(e.enter("characterReferenceMarker"),e.consume(f),e.exit("characterReferenceMarker"),e.exit("characterReference"),t)}return a(f)&&o++<i?(e.consume(f),u):n(f)}}const gl={partial:!0,tokenize:aS},vl={concrete:!0,name:"codeFenced",tokenize:iS};function iS(e,t,n){const r=this,o={partial:!0,tokenize:y};let i=0,a=0,s;return l;function l(x){return c(x)}function c(x){const I=r.events[r.events.length-1];return i=I&&I[1].type==="linePrefix"?I[2].sliceSerialize(I[1],!0).length:0,s=x,e.enter("codeFenced"),e.enter("codeFencedFence"),e.enter("codeFencedFenceSequence"),u(x)}function u(x){return x===s?(a++,e.consume(x),u):a<3?n(x):(e.exit("codeFencedFenceSequence"),Me(x)?Be(e,f,"whitespace")(x):f(x))}function f(x){return x===null||we(x)?(e.exit("codeFencedFence"),r.interrupt?t(x):e.check(gl,h,E)(x)):(e.enter("codeFencedFenceInfo"),e.enter("chunkString",{contentType:"string"}),m(x))}function m(x){return x===null||we(x)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),f(x)):Me(x)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),Be(e,p,"whitespace")(x)):x===96&&x===s?n(x):(e.consume(x),m)}function p(x){return x===null||we(x)?f(x):(e.enter("codeFencedFenceMeta"),e.enter("chunkString",{contentType:"string"}),v(x))}function v(x){return x===null||we(x)?(e.exit("chunkString"),e.exit("codeFencedFenceMeta"),f(x)):x===96&&x===s?n(x):(e.consume(x),v)}function h(x){return e.attempt(o,E,C)(x)}function C(x){return e.enter("lineEnding"),e.consume(x),e.exit("lineEnding"),g}function g(x){return i>0&&Me(x)?Be(e,b,"linePrefix",i+1)(x):b(x)}function b(x){return x===null||we(x)?e.check(gl,h,E)(x):(e.enter("codeFlowValue"),w(x))}function w(x){return x===null||we(x)?(e.exit("codeFlowValue"),b(x)):(e.consume(x),w)}function E(x){return e.exit("codeFenced"),t(x)}function y(x,I,R){let k=0;return T;function T(L){return x.enter("lineEnding"),x.consume(L),x.exit("lineEnding"),O}function O(L){return x.enter("codeFencedFence"),Me(L)?Be(x,N,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(L):N(L)}function N(L){return L===s?(x.enter("codeFencedFenceSequence"),j(L)):R(L)}function j(L){return L===s?(k++,x.consume(L),j):k>=a?(x.exit("codeFencedFenceSequence"),Me(L)?Be(x,M,"whitespace")(L):M(L)):R(L)}function M(L){return L===null||we(L)?(x.exit("codeFencedFence"),I(L)):R(L)}}}function aS(e,t,n){const r=this;return o;function o(a){return a===null?n(a):(e.enter("lineEnding"),e.consume(a),e.exit("lineEnding"),i)}function i(a){return r.parser.lazy[r.now().line]?n(a):t(a)}}const Uo={name:"codeIndented",tokenize:lS},sS={partial:!0,tokenize:cS};function lS(e,t,n){const r=this;return o;function o(c){return e.enter("codeIndented"),Be(e,i,"linePrefix",5)(c)}function i(c){const u=r.events[r.events.length-1];return u&&u[1].type==="linePrefix"&&u[2].sliceSerialize(u[1],!0).length>=4?a(c):n(c)}function a(c){return c===null?l(c):we(c)?e.attempt(sS,a,l)(c):(e.enter("codeFlowValue"),s(c))}function s(c){return c===null||we(c)?(e.exit("codeFlowValue"),a(c)):(e.consume(c),s)}function l(c){return e.exit("codeIndented"),t(c)}}function cS(e,t,n){const r=this;return o;function o(a){return r.parser.lazy[r.now().line]?n(a):we(a)?(e.enter("lineEnding"),e.consume(a),e.exit("lineEnding"),o):Be(e,i,"linePrefix",5)(a)}function i(a){const s=r.events[r.events.length-1];return s&&s[1].type==="linePrefix"&&s[2].sliceSerialize(s[1],!0).length>=4?t(a):we(a)?o(a):n(a)}}const uS={name:"codeText",previous:fS,resolve:dS,tokenize:pS};function dS(e){let t=e.length-4,n=3,r,o;if((e[n][1].type==="lineEnding"||e[n][1].type==="space")&&(e[t][1].type==="lineEnding"||e[t][1].type==="space")){for(r=n;++r<t;)if(e[r][1].type==="codeTextData"){e[n][1].type="codeTextPadding",e[t][1].type="codeTextPadding",n+=2,t-=2;break}}for(r=n-1,t++;++r<=t;)o===void 0?r!==t&&e[r][1].type!=="lineEnding"&&(o=r):(r===t||e[r][1].type==="lineEnding")&&(e[o][1].type="codeTextData",r!==o+2&&(e[o][1].end=e[r-1][1].end,e.splice(o+2,r-o-2),t-=r-o-2,r=o+2),o=void 0);return e}function fS(e){return e!==96||this.events[this.events.length-1][1].type==="characterEscape"}function pS(e,t,n){let r=0,o,i;return a;function a(f){return e.enter("codeText"),e.enter("codeTextSequence"),s(f)}function s(f){return f===96?(e.consume(f),r++,s):(e.exit("codeTextSequence"),l(f))}function l(f){return f===null?n(f):f===32?(e.enter("space"),e.consume(f),e.exit("space"),l):f===96?(i=e.enter("codeTextSequence"),o=0,u(f)):we(f)?(e.enter("lineEnding"),e.consume(f),e.exit("lineEnding"),l):(e.enter("codeTextData"),c(f))}function c(f){return f===null||f===32||f===96||we(f)?(e.exit("codeTextData"),l(f)):(e.consume(f),c)}function u(f){return f===96?(e.consume(f),o++,u):o===r?(e.exit("codeTextSequence"),e.exit("codeText"),t(f)):(i.type="codeTextData",c(f))}}class mS{constructor(t){this.left=t?[...t]:[],this.right=[]}get(t){if(t<0||t>=this.left.length+this.right.length)throw new RangeError("Cannot access index `"+t+"` in a splice buffer of size `"+(this.left.length+this.right.length)+"`");return t<this.left.length?this.left[t]:this.right[this.right.length-t+this.left.length-1]}get length(){return this.left.length+this.right.length}shift(){return this.setCursor(0),this.right.pop()}slice(t,n){const r=n??Number.POSITIVE_INFINITY;return r<this.left.length?this.left.slice(t,r):t>this.left.length?this.right.slice(this.right.length-r+this.left.length,this.right.length-t+this.left.length).reverse():this.left.slice(t).concat(this.right.slice(this.right.length-r+this.left.length).reverse())}splice(t,n,r){const o=n||0;this.setCursor(Math.trunc(t));const i=this.right.splice(this.right.length-o,Number.POSITIVE_INFINITY);return r&&Fn(this.left,r),i.reverse()}pop(){return this.setCursor(Number.POSITIVE_INFINITY),this.left.pop()}push(t){this.setCursor(Number.POSITIVE_INFINITY),this.left.push(t)}pushMany(t){this.setCursor(Number.POSITIVE_INFINITY),Fn(this.left,t)}unshift(t){this.setCursor(0),this.right.push(t)}unshiftMany(t){this.setCursor(0),Fn(this.right,t.reverse())}setCursor(t){if(!(t===this.left.length||t>this.left.length&&this.right.length===0||t<0&&this.left.length===0))if(t<this.left.length){const n=this.left.splice(t,Number.POSITIVE_INFINITY);Fn(this.right,n.reverse())}else{const n=this.right.splice(this.left.length+this.right.length-t,Number.POSITIVE_INFINITY);Fn(this.left,n.reverse())}}}function Fn(e,t){let n=0;if(t.length<1e4)e.push(...t);else for(;n<t.length;)e.push(...t.slice(n,n+1e4)),n+=1e4}function gd(e){const t={};let n=-1,r,o,i,a,s,l,c;const u=new mS(e);for(;++n<u.length;){for(;n in t;)n=t[n];if(r=u.get(n),n&&r[1].type==="chunkFlow"&&u.get(n-1)[1].type==="listItemPrefix"&&(l=r[1]._tokenizer.events,i=0,i<l.length&&l[i][1].type==="lineEndingBlank"&&(i+=2),i<l.length&&l[i][1].type==="content"))for(;++i<l.length&&l[i][1].type!=="content";)l[i][1].type==="chunkText"&&(l[i][1]._isInFirstContentOfListItem=!0,i++);if(r[0]==="enter")r[1].contentType&&(Object.assign(t,hS(u,n)),n=t[n],c=!0);else if(r[1]._container){for(i=n,o=void 0;i--;)if(a=u.get(i),a[1].type==="lineEnding"||a[1].type==="lineEndingBlank")a[0]==="enter"&&(o&&(u.get(o)[1].type="lineEndingBlank"),a[1].type="lineEnding",o=i);else if(!(a[1].type==="linePrefix"||a[1].type==="listItemIndent"))break;o&&(r[1].end={...u.get(o)[1].start},s=u.slice(o,n),s.unshift(r),u.splice(o,n-o+1,s))}}return zt(e,0,Number.POSITIVE_INFINITY,u.slice(0)),!c}function hS(e,t){const n=e.get(t)[1],r=e.get(t)[2];let o=t-1;const i=[];let a=n._tokenizer;a||(a=r.parser[n.contentType](n.start),n._contentTypeTextTrailing&&(a._contentTypeTextTrailing=!0));const s=a.events,l=[],c={};let u,f,m=-1,p=n,v=0,h=0;const C=[h];for(;p;){for(;e.get(++o)[1]!==p;);i.push(o),p._tokenizer||(u=r.sliceStream(p),p.next||u.push(null),f&&a.defineSkip(p.start),p._isInFirstContentOfListItem&&(a._gfmTasklistFirstContentOfListItem=!0),a.write(u),p._isInFirstContentOfListItem&&(a._gfmTasklistFirstContentOfListItem=void 0)),f=p,p=p.next}for(p=n;++m<s.length;)s[m][0]==="exit"&&s[m-1][0]==="enter"&&s[m][1].type===s[m-1][1].type&&s[m][1].start.line!==s[m][1].end.line&&(h=m+1,C.push(h),p._tokenizer=void 0,p.previous=void 0,p=p.next);for(a.events=[],p?(p._tokenizer=void 0,p.previous=void 0):C.pop(),m=C.length;m--;){const g=s.slice(C[m],C[m+1]),b=i.pop();l.push([b,b+g.length-1]),e.splice(b,2,g)}for(l.reverse(),m=-1;++m<l.length;)c[v+l[m][0]]=v+l[m][1],v+=l[m][1]-l[m][0]-1;return c}const gS={resolve:yS,tokenize:bS},vS={partial:!0,tokenize:xS};function yS(e){return gd(e),e}function bS(e,t){let n;return r;function r(s){return e.enter("content"),n=e.enter("chunkContent",{contentType:"content"}),o(s)}function o(s){return s===null?i(s):we(s)?e.check(vS,a,i)(s):(e.consume(s),o)}function i(s){return e.exit("chunkContent"),e.exit("content"),t(s)}function a(s){return e.consume(s),e.exit("chunkContent"),n.next=e.enter("chunkContent",{contentType:"content",previous:n}),n=n.next,o}}function xS(e,t,n){const r=this;return o;function o(a){return e.exit("chunkContent"),e.enter("lineEnding"),e.consume(a),e.exit("lineEnding"),Be(e,i,"linePrefix")}function i(a){if(a===null||we(a))return n(a);const s=r.events[r.events.length-1];return!r.parser.constructs.disable.null.includes("codeIndented")&&s&&s[1].type==="linePrefix"&&s[2].sliceSerialize(s[1],!0).length>=4?t(a):e.interrupt(r.parser.constructs.flow,n,t)(a)}}function vd(e,t,n,r,o,i,a,s,l){const c=l||Number.POSITIVE_INFINITY;let u=0;return f;function f(g){return g===60?(e.enter(r),e.enter(o),e.enter(i),e.consume(g),e.exit(i),m):g===null||g===32||g===41||Si(g)?n(g):(e.enter(r),e.enter(a),e.enter(s),e.enter("chunkString",{contentType:"string"}),h(g))}function m(g){return g===62?(e.enter(i),e.consume(g),e.exit(i),e.exit(o),e.exit(r),t):(e.enter(s),e.enter("chunkString",{contentType:"string"}),p(g))}function p(g){return g===62?(e.exit("chunkString"),e.exit(s),m(g)):g===null||g===60||we(g)?n(g):(e.consume(g),g===92?v:p)}function v(g){return g===60||g===62||g===92?(e.consume(g),p):p(g)}function h(g){return!u&&(g===null||g===41||dt(g))?(e.exit("chunkString"),e.exit(s),e.exit(a),e.exit(r),t(g)):u<c&&g===40?(e.consume(g),u++,h):g===41?(e.consume(g),u--,h):g===null||g===32||g===40||Si(g)?n(g):(e.consume(g),g===92?C:h)}function C(g){return g===40||g===41||g===92?(e.consume(g),h):h(g)}}function yd(e,t,n,r,o,i){const a=this;let s=0,l;return c;function c(p){return e.enter(r),e.enter(o),e.consume(p),e.exit(o),e.enter(i),u}function u(p){return s>999||p===null||p===91||p===93&&!l||p===94&&!s&&"_hiddenFootnoteSupport"in a.parser.constructs?n(p):p===93?(e.exit(i),e.enter(o),e.consume(p),e.exit(o),e.exit(r),t):we(p)?(e.enter("lineEnding"),e.consume(p),e.exit("lineEnding"),u):(e.enter("chunkString",{contentType:"string"}),f(p))}function f(p){return p===null||p===91||p===93||we(p)||s++>999?(e.exit("chunkString"),u(p)):(e.consume(p),l||(l=!Me(p)),p===92?m:f)}function m(p){return p===91||p===92||p===93?(e.consume(p),s++,f):f(p)}}function bd(e,t,n,r,o,i){let a;return s;function s(m){return m===34||m===39||m===40?(e.enter(r),e.enter(o),e.consume(m),e.exit(o),a=m===40?41:m,l):n(m)}function l(m){return m===a?(e.enter(o),e.consume(m),e.exit(o),e.exit(r),t):(e.enter(i),c(m))}function c(m){return m===a?(e.exit(i),l(a)):m===null?n(m):we(m)?(e.enter("lineEnding"),e.consume(m),e.exit("lineEnding"),Be(e,c,"linePrefix")):(e.enter("chunkString",{contentType:"string"}),u(m))}function u(m){return m===a||m===null||we(m)?(e.exit("chunkString"),c(m)):(e.consume(m),m===92?f:u)}function f(m){return m===a||m===92?(e.consume(m),u):u(m)}}function Gn(e,t){let n;return r;function r(o){return we(o)?(e.enter("lineEnding"),e.consume(o),e.exit("lineEnding"),n=!0,r):Me(o)?Be(e,r,n?"linePrefix":"lineSuffix")(o):t(o)}}const CS={name:"definition",tokenize:wS},SS={partial:!0,tokenize:$S};function wS(e,t,n){const r=this;let o;return i;function i(p){return e.enter("definition"),a(p)}function a(p){return yd.call(r,e,s,n,"definitionLabel","definitionLabelMarker","definitionLabelString")(p)}function s(p){return o=Sn(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)),p===58?(e.enter("definitionMarker"),e.consume(p),e.exit("definitionMarker"),l):n(p)}function l(p){return dt(p)?Gn(e,c)(p):c(p)}function c(p){return vd(e,u,n,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(p)}function u(p){return e.attempt(SS,f,f)(p)}function f(p){return Me(p)?Be(e,m,"whitespace")(p):m(p)}function m(p){return p===null||we(p)?(e.exit("definition"),r.parser.defined.push(o),t(p)):n(p)}}function $S(e,t,n){return r;function r(s){return dt(s)?Gn(e,o)(s):n(s)}function o(s){return bd(e,i,n,"definitionTitle","definitionTitleMarker","definitionTitleString")(s)}function i(s){return Me(s)?Be(e,a,"whitespace")(s):a(s)}function a(s){return s===null||we(s)?t(s):n(s)}}const ES={name:"hardBreakEscape",tokenize:IS};function IS(e,t,n){return r;function r(i){return e.enter("hardBreakEscape"),e.consume(i),o}function o(i){return we(i)?(e.exit("hardBreakEscape"),t(i)):n(i)}}const PS={name:"headingAtx",resolve:TS,tokenize:OS};function TS(e,t){let n=e.length-2,r=3,o,i;return e[r][1].type==="whitespace"&&(r+=2),n-2>r&&e[n][1].type==="whitespace"&&(n-=2),e[n][1].type==="atxHeadingSequence"&&(r===n-1||n-4>r&&e[n-2][1].type==="whitespace")&&(n-=r+1===n?2:4),n>r&&(o={type:"atxHeadingText",start:e[r][1].start,end:e[n][1].end},i={type:"chunkText",start:e[r][1].start,end:e[n][1].end,contentType:"text"},zt(e,r,n-r+1,[["enter",o,t],["enter",i,t],["exit",i,t],["exit",o,t]])),e}function OS(e,t,n){let r=0;return o;function o(u){return e.enter("atxHeading"),i(u)}function i(u){return e.enter("atxHeadingSequence"),a(u)}function a(u){return u===35&&r++<6?(e.consume(u),a):u===null||dt(u)?(e.exit("atxHeadingSequence"),s(u)):n(u)}function s(u){return u===35?(e.enter("atxHeadingSequence"),l(u)):u===null||we(u)?(e.exit("atxHeading"),t(u)):Me(u)?Be(e,s,"whitespace")(u):(e.enter("atxHeadingText"),c(u))}function l(u){return u===35?(e.consume(u),l):(e.exit("atxHeadingSequence"),s(u))}function c(u){return u===null||u===35||dt(u)?(e.exit("atxHeadingText"),s(u)):(e.consume(u),c)}}const RS=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],yl=["pre","script","style","textarea"],NS={concrete:!0,name:"htmlFlow",resolveTo:jS,tokenize:LS},kS={partial:!0,tokenize:AS},MS={partial:!0,tokenize:_S};function jS(e){let t=e.length;for(;t--&&!(e[t][0]==="enter"&&e[t][1].type==="htmlFlow"););return t>1&&e[t-2][1].type==="linePrefix"&&(e[t][1].start=e[t-2][1].start,e[t+1][1].start=e[t-2][1].start,e.splice(t-2,2)),e}function LS(e,t,n){const r=this;let o,i,a,s,l;return c;function c(S){return u(S)}function u(S){return e.enter("htmlFlow"),e.enter("htmlFlowData"),e.consume(S),f}function f(S){return S===33?(e.consume(S),m):S===47?(e.consume(S),i=!0,h):S===63?(e.consume(S),o=3,r.interrupt?t:$):Lt(S)?(e.consume(S),a=String.fromCharCode(S),C):n(S)}function m(S){return S===45?(e.consume(S),o=2,p):S===91?(e.consume(S),o=5,s=0,v):Lt(S)?(e.consume(S),o=4,r.interrupt?t:$):n(S)}function p(S){return S===45?(e.consume(S),r.interrupt?t:$):n(S)}function v(S){const Y="CDATA[";return S===Y.charCodeAt(s++)?(e.consume(S),s===Y.length?r.interrupt?t:N:v):n(S)}function h(S){return Lt(S)?(e.consume(S),a=String.fromCharCode(S),C):n(S)}function C(S){if(S===null||S===47||S===62||dt(S)){const Y=S===47,ne=a.toLowerCase();return!Y&&!i&&yl.includes(ne)?(o=1,r.interrupt?t(S):N(S)):RS.includes(a.toLowerCase())?(o=6,Y?(e.consume(S),g):r.interrupt?t(S):N(S)):(o=7,r.interrupt&&!r.parser.lazy[r.now().line]?n(S):i?b(S):w(S))}return S===45||vt(S)?(e.consume(S),a+=String.fromCharCode(S),C):n(S)}function g(S){return S===62?(e.consume(S),r.interrupt?t:N):n(S)}function b(S){return Me(S)?(e.consume(S),b):T(S)}function w(S){return S===47?(e.consume(S),T):S===58||S===95||Lt(S)?(e.consume(S),E):Me(S)?(e.consume(S),w):T(S)}function E(S){return S===45||S===46||S===58||S===95||vt(S)?(e.consume(S),E):y(S)}function y(S){return S===61?(e.consume(S),x):Me(S)?(e.consume(S),y):w(S)}function x(S){return S===null||S===60||S===61||S===62||S===96?n(S):S===34||S===39?(e.consume(S),l=S,I):Me(S)?(e.consume(S),x):R(S)}function I(S){return S===l?(e.consume(S),l=null,k):S===null||we(S)?n(S):(e.consume(S),I)}function R(S){return S===null||S===34||S===39||S===47||S===60||S===61||S===62||S===96||dt(S)?y(S):(e.consume(S),R)}function k(S){return S===47||S===62||Me(S)?w(S):n(S)}function T(S){return S===62?(e.consume(S),O):n(S)}function O(S){return S===null||we(S)?N(S):Me(S)?(e.consume(S),O):n(S)}function N(S){return S===45&&o===2?(e.consume(S),z):S===60&&o===1?(e.consume(S),D):S===62&&o===4?(e.consume(S),G):S===63&&o===3?(e.consume(S),$):S===93&&o===5?(e.consume(S),F):we(S)&&(o===6||o===7)?(e.exit("htmlFlowData"),e.check(kS,q,j)(S)):S===null||we(S)?(e.exit("htmlFlowData"),j(S)):(e.consume(S),N)}function j(S){return e.check(MS,M,q)(S)}function M(S){return e.enter("lineEnding"),e.consume(S),e.exit("lineEnding"),L}function L(S){return S===null||we(S)?j(S):(e.enter("htmlFlowData"),N(S))}function z(S){return S===45?(e.consume(S),$):N(S)}function D(S){return S===47?(e.consume(S),a="",W):N(S)}function W(S){if(S===62){const Y=a.toLowerCase();return yl.includes(Y)?(e.consume(S),G):N(S)}return Lt(S)&&a.length<8?(e.consume(S),a+=String.fromCharCode(S),W):N(S)}function F(S){return S===93?(e.consume(S),$):N(S)}function $(S){return S===62?(e.consume(S),G):S===45&&o===2?(e.consume(S),$):N(S)}function G(S){return S===null||we(S)?(e.exit("htmlFlowData"),q(S)):(e.consume(S),G)}function q(S){return e.exit("htmlFlow"),t(S)}}function _S(e,t,n){const r=this;return o;function o(a){return we(a)?(e.enter("lineEnding"),e.consume(a),e.exit("lineEnding"),i):n(a)}function i(a){return r.parser.lazy[r.now().line]?n(a):t(a)}}function AS(e,t,n){return r;function r(o){return e.enter("lineEnding"),e.consume(o),e.exit("lineEnding"),e.attempt(mo,t,n)}}const zS={name:"htmlText",tokenize:DS};function DS(e,t,n){const r=this;let o,i,a;return s;function s($){return e.enter("htmlText"),e.enter("htmlTextData"),e.consume($),l}function l($){return $===33?(e.consume($),c):$===47?(e.consume($),y):$===63?(e.consume($),w):Lt($)?(e.consume($),R):n($)}function c($){return $===45?(e.consume($),u):$===91?(e.consume($),i=0,v):Lt($)?(e.consume($),b):n($)}function u($){return $===45?(e.consume($),p):n($)}function f($){return $===null?n($):$===45?(e.consume($),m):we($)?(a=f,D($)):(e.consume($),f)}function m($){return $===45?(e.consume($),p):f($)}function p($){return $===62?z($):$===45?m($):f($)}function v($){const G="CDATA[";return $===G.charCodeAt(i++)?(e.consume($),i===G.length?h:v):n($)}function h($){return $===null?n($):$===93?(e.consume($),C):we($)?(a=h,D($)):(e.consume($),h)}function C($){return $===93?(e.consume($),g):h($)}function g($){return $===62?z($):$===93?(e.consume($),g):h($)}function b($){return $===null||$===62?z($):we($)?(a=b,D($)):(e.consume($),b)}function w($){return $===null?n($):$===63?(e.consume($),E):we($)?(a=w,D($)):(e.consume($),w)}function E($){return $===62?z($):w($)}function y($){return Lt($)?(e.consume($),x):n($)}function x($){return $===45||vt($)?(e.consume($),x):I($)}function I($){return we($)?(a=I,D($)):Me($)?(e.consume($),I):z($)}function R($){return $===45||vt($)?(e.consume($),R):$===47||$===62||dt($)?k($):n($)}function k($){return $===47?(e.consume($),z):$===58||$===95||Lt($)?(e.consume($),T):we($)?(a=k,D($)):Me($)?(e.consume($),k):z($)}function T($){return $===45||$===46||$===58||$===95||vt($)?(e.consume($),T):O($)}function O($){return $===61?(e.consume($),N):we($)?(a=O,D($)):Me($)?(e.consume($),O):k($)}function N($){return $===null||$===60||$===61||$===62||$===96?n($):$===34||$===39?(e.consume($),o=$,j):we($)?(a=N,D($)):Me($)?(e.consume($),N):(e.consume($),M)}function j($){return $===o?(e.consume($),o=void 0,L):$===null?n($):we($)?(a=j,D($)):(e.consume($),j)}function M($){return $===null||$===34||$===39||$===60||$===61||$===96?n($):$===47||$===62||dt($)?k($):(e.consume($),M)}function L($){return $===47||$===62||dt($)?k($):n($)}function z($){return $===62?(e.consume($),e.exit("htmlTextData"),e.exit("htmlText"),t):n($)}function D($){return e.exit("htmlTextData"),e.enter("lineEnding"),e.consume($),e.exit("lineEnding"),W}function W($){return Me($)?Be(e,F,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)($):F($)}function F($){return e.enter("htmlTextData"),a($)}}const ma={name:"labelEnd",resolveAll:WS,resolveTo:VS,tokenize:US},BS={tokenize:XS},FS={tokenize:GS},HS={tokenize:qS};function WS(e){let t=-1;const n=[];for(;++t<e.length;){const r=e[t][1];if(n.push(e[t]),r.type==="labelImage"||r.type==="labelLink"||r.type==="labelEnd"){const o=r.type==="labelImage"?4:2;r.type="data",t+=o}}return e.length!==n.length&&zt(e,0,e.length,n),e}function VS(e,t){let n=e.length,r=0,o,i,a,s;for(;n--;)if(o=e[n][1],i){if(o.type==="link"||o.type==="labelLink"&&o._inactive)break;e[n][0]==="enter"&&o.type==="labelLink"&&(o._inactive=!0)}else if(a){if(e[n][0]==="enter"&&(o.type==="labelImage"||o.type==="labelLink")&&!o._balanced&&(i=n,o.type!=="labelLink")){r=2;break}}else o.type==="labelEnd"&&(a=n);const l={type:e[i][1].type==="labelLink"?"link":"image",start:{...e[i][1].start},end:{...e[e.length-1][1].end}},c={type:"label",start:{...e[i][1].start},end:{...e[a][1].end}},u={type:"labelText",start:{...e[i+r+2][1].end},end:{...e[a-2][1].start}};return s=[["enter",l,t],["enter",c,t]],s=Et(s,e.slice(i+1,i+r+3)),s=Et(s,[["enter",u,t]]),s=Et(s,pa(t.parser.constructs.insideSpan.null,e.slice(i+r+4,a-3),t)),s=Et(s,[["exit",u,t],e[a-2],e[a-1],["exit",c,t]]),s=Et(s,e.slice(a+1)),s=Et(s,[["exit",l,t]]),zt(e,i,e.length,s),e}function US(e,t,n){const r=this;let o=r.events.length,i,a;for(;o--;)if((r.events[o][1].type==="labelImage"||r.events[o][1].type==="labelLink")&&!r.events[o][1]._balanced){i=r.events[o][1];break}return s;function s(m){return i?i._inactive?f(m):(a=r.parser.defined.includes(Sn(r.sliceSerialize({start:i.end,end:r.now()}))),e.enter("labelEnd"),e.enter("labelMarker"),e.consume(m),e.exit("labelMarker"),e.exit("labelEnd"),l):n(m)}function l(m){return m===40?e.attempt(BS,u,a?u:f)(m):m===91?e.attempt(FS,u,a?c:f)(m):a?u(m):f(m)}function c(m){return e.attempt(HS,u,f)(m)}function u(m){return t(m)}function f(m){return i._balanced=!0,n(m)}}function XS(e,t,n){return r;function r(f){return e.enter("resource"),e.enter("resourceMarker"),e.consume(f),e.exit("resourceMarker"),o}function o(f){return dt(f)?Gn(e,i)(f):i(f)}function i(f){return f===41?u(f):vd(e,a,s,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(f)}function a(f){return dt(f)?Gn(e,l)(f):u(f)}function s(f){return n(f)}function l(f){return f===34||f===39||f===40?bd(e,c,n,"resourceTitle","resourceTitleMarker","resourceTitleString")(f):u(f)}function c(f){return dt(f)?Gn(e,u)(f):u(f)}function u(f){return f===41?(e.enter("resourceMarker"),e.consume(f),e.exit("resourceMarker"),e.exit("resource"),t):n(f)}}function GS(e,t,n){const r=this;return o;function o(s){return yd.call(r,e,i,a,"reference","referenceMarker","referenceString")(s)}function i(s){return r.parser.defined.includes(Sn(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)))?t(s):n(s)}function a(s){return n(s)}}function qS(e,t,n){return r;function r(i){return e.enter("reference"),e.enter("referenceMarker"),e.consume(i),e.exit("referenceMarker"),o}function o(i){return i===93?(e.enter("referenceMarker"),e.consume(i),e.exit("referenceMarker"),e.exit("reference"),t):n(i)}}const YS={name:"labelStartImage",resolveAll:ma.resolveAll,tokenize:KS};function KS(e,t,n){const r=this;return o;function o(s){return e.enter("labelImage"),e.enter("labelImageMarker"),e.consume(s),e.exit("labelImageMarker"),i}function i(s){return s===91?(e.enter("labelMarker"),e.consume(s),e.exit("labelMarker"),e.exit("labelImage"),a):n(s)}function a(s){return s===94&&"_hiddenFootnoteSupport"in r.parser.constructs?n(s):t(s)}}const ZS={name:"labelStartLink",resolveAll:ma.resolveAll,tokenize:JS};function JS(e,t,n){const r=this;return o;function o(a){return e.enter("labelLink"),e.enter("labelMarker"),e.consume(a),e.exit("labelMarker"),e.exit("labelLink"),i}function i(a){return a===94&&"_hiddenFootnoteSupport"in r.parser.constructs?n(a):t(a)}}const Xo={name:"lineEnding",tokenize:QS};function QS(e,t){return n;function n(r){return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),Be(e,t,"linePrefix")}}const Br={name:"thematicBreak",tokenize:ew};function ew(e,t,n){let r=0,o;return i;function i(c){return e.enter("thematicBreak"),a(c)}function a(c){return o=c,s(c)}function s(c){return c===o?(e.enter("thematicBreakSequence"),l(c)):r>=3&&(c===null||we(c))?(e.exit("thematicBreak"),t(c)):n(c)}function l(c){return c===o?(e.consume(c),r++,l):(e.exit("thematicBreakSequence"),Me(c)?Be(e,s,"whitespace")(c):s(c))}}const ut={continuation:{tokenize:ow},exit:aw,name:"list",tokenize:rw},tw={partial:!0,tokenize:sw},nw={partial:!0,tokenize:iw};function rw(e,t,n){const r=this,o=r.events[r.events.length-1];let i=o&&o[1].type==="linePrefix"?o[2].sliceSerialize(o[1],!0).length:0,a=0;return s;function s(p){const v=r.containerState.type||(p===42||p===43||p===45?"listUnordered":"listOrdered");if(v==="listUnordered"?!r.containerState.marker||p===r.containerState.marker:wi(p)){if(r.containerState.type||(r.containerState.type=v,e.enter(v,{_container:!0})),v==="listUnordered")return e.enter("listItemPrefix"),p===42||p===45?e.check(Br,n,c)(p):c(p);if(!r.interrupt||p===49)return e.enter("listItemPrefix"),e.enter("listItemValue"),l(p)}return n(p)}function l(p){return wi(p)&&++a<10?(e.consume(p),l):(!r.interrupt||a<2)&&(r.containerState.marker?p===r.containerState.marker:p===41||p===46)?(e.exit("listItemValue"),c(p)):n(p)}function c(p){return e.enter("listItemMarker"),e.consume(p),e.exit("listItemMarker"),r.containerState.marker=r.containerState.marker||p,e.check(mo,r.interrupt?n:u,e.attempt(tw,m,f))}function u(p){return r.containerState.initialBlankLine=!0,i++,m(p)}function f(p){return Me(p)?(e.enter("listItemPrefixWhitespace"),e.consume(p),e.exit("listItemPrefixWhitespace"),m):n(p)}function m(p){return r.containerState.size=i+r.sliceSerialize(e.exit("listItemPrefix"),!0).length,t(p)}}function ow(e,t,n){const r=this;return r.containerState._closeFlow=void 0,e.check(mo,o,i);function o(s){return r.containerState.furtherBlankLines=r.containerState.furtherBlankLines||r.containerState.initialBlankLine,Be(e,t,"listItemIndent",r.containerState.size+1)(s)}function i(s){return r.containerState.furtherBlankLines||!Me(s)?(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,a(s)):(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,e.attempt(nw,t,a)(s))}function a(s){return r.containerState._closeFlow=!0,r.interrupt=void 0,Be(e,e.attempt(ut,t,n),"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(s)}}function iw(e,t,n){const r=this;return Be(e,o,"listItemIndent",r.containerState.size+1);function o(i){const a=r.events[r.events.length-1];return a&&a[1].type==="listItemIndent"&&a[2].sliceSerialize(a[1],!0).length===r.containerState.size?t(i):n(i)}}function aw(e){e.exit(this.containerState.type)}function sw(e,t,n){const r=this;return Be(e,o,"listItemPrefixWhitespace",r.parser.constructs.disable.null.includes("codeIndented")?void 0:5);function o(i){const a=r.events[r.events.length-1];return!Me(i)&&a&&a[1].type==="listItemPrefixWhitespace"?t(i):n(i)}}const bl={name:"setextUnderline",resolveTo:lw,tokenize:cw};function lw(e,t){let n=e.length,r,o,i;for(;n--;)if(e[n][0]==="enter"){if(e[n][1].type==="content"){r=n;break}e[n][1].type==="paragraph"&&(o=n)}else e[n][1].type==="content"&&e.splice(n,1),!i&&e[n][1].type==="definition"&&(i=n);const a={type:"setextHeading",start:{...e[r][1].start},end:{...e[e.length-1][1].end}};return e[o][1].type="setextHeadingText",i?(e.splice(o,0,["enter",a,t]),e.splice(i+1,0,["exit",e[r][1],t]),e[r][1].end={...e[i][1].end}):e[r][1]=a,e.push(["exit",a,t]),e}function cw(e,t,n){const r=this;let o;return i;function i(c){let u=r.events.length,f;for(;u--;)if(r.events[u][1].type!=="lineEnding"&&r.events[u][1].type!=="linePrefix"&&r.events[u][1].type!=="content"){f=r.events[u][1].type==="paragraph";break}return!r.parser.lazy[r.now().line]&&(r.interrupt||f)?(e.enter("setextHeadingLine"),o=c,a(c)):n(c)}function a(c){return e.enter("setextHeadingLineSequence"),s(c)}function s(c){return c===o?(e.consume(c),s):(e.exit("setextHeadingLineSequence"),Me(c)?Be(e,l,"lineSuffix")(c):l(c))}function l(c){return c===null||we(c)?(e.exit("setextHeadingLine"),t(c)):n(c)}}const uw={tokenize:dw};function dw(e){const t=this,n=e.attempt(mo,r,e.attempt(this.parser.constructs.flowInitial,o,Be(e,e.attempt(this.parser.constructs.flow,o,e.attempt(gS,o)),"linePrefix")));return n;function r(i){if(i===null){e.consume(i);return}return e.enter("lineEndingBlank"),e.consume(i),e.exit("lineEndingBlank"),t.currentConstruct=void 0,n}function o(i){if(i===null){e.consume(i);return}return e.enter("lineEnding"),e.consume(i),e.exit("lineEnding"),t.currentConstruct=void 0,n}}const fw={resolveAll:Cd()},pw=xd("string"),mw=xd("text");function xd(e){return{resolveAll:Cd(e==="text"?hw:void 0),tokenize:t};function t(n){const r=this,o=this.parser.constructs[e],i=n.attempt(o,a,s);return a;function a(u){return c(u)?i(u):s(u)}function s(u){if(u===null){n.consume(u);return}return n.enter("data"),n.consume(u),l}function l(u){return c(u)?(n.exit("data"),i(u)):(n.consume(u),l)}function c(u){if(u===null)return!0;const f=o[u];let m=-1;if(f)for(;++m<f.length;){const p=f[m];if(!p.previous||p.previous.call(r,r.previous))return!0}return!1}}}function Cd(e){return t;function t(n,r){let o=-1,i;for(;++o<=n.length;)i===void 0?n[o]&&n[o][1].type==="data"&&(i=o,o++):(!n[o]||n[o][1].type!=="data")&&(o!==i+2&&(n[i][1].end=n[o-1][1].end,n.splice(i+2,o-i-2),o=i+2),i=void 0);return e?e(n,r):n}}function hw(e,t){let n=0;for(;++n<=e.length;)if((n===e.length||e[n][1].type==="lineEnding")&&e[n-1][1].type==="data"){const r=e[n-1][1],o=t.sliceStream(r);let i=o.length,a=-1,s=0,l;for(;i--;){const c=o[i];if(typeof c=="string"){for(a=c.length;c.charCodeAt(a-1)===32;)s++,a--;if(a)break;a=-1}else if(c===-2)l=!0,s++;else if(c!==-1){i++;break}}if(t._contentTypeTextTrailing&&n===e.length&&(s=0),s){const c={type:n===e.length||l||s<2?"lineSuffix":"hardBreakTrailing",start:{_bufferIndex:i?a:r.start._bufferIndex+a,_index:r.start._index+i,line:r.end.line,column:r.end.column-s,offset:r.end.offset-s},end:{...r.end}};r.end={...c.start},r.start.offset===r.end.offset?Object.assign(r,c):(e.splice(n,0,["enter",c,t],["exit",c,t]),n+=2)}n++}return e}const gw={42:ut,43:ut,45:ut,48:ut,49:ut,50:ut,51:ut,52:ut,53:ut,54:ut,55:ut,56:ut,57:ut,62:pd},vw={91:CS},yw={[-2]:Uo,[-1]:Uo,32:Uo},bw={35:PS,42:Br,45:[bl,Br],60:NS,61:bl,95:Br,96:vl,126:vl},xw={38:hd,92:md},Cw={[-5]:Xo,[-4]:Xo,[-3]:Xo,33:YS,38:hd,42:$i,60:[ZC,zS],91:ZS,92:[ES,md],93:ma,95:$i,96:uS},Sw={null:[$i,fw]},ww={null:[42,95]},$w={null:[]},Ew=Object.freeze(Object.defineProperty({__proto__:null,attentionMarkers:ww,contentInitial:vw,disable:$w,document:gw,flow:bw,flowInitial:yw,insideSpan:Sw,string:xw,text:Cw},Symbol.toStringTag,{value:"Module"}));function Iw(e,t,n){let r={_bufferIndex:-1,_index:0,line:n&&n.line||1,column:n&&n.column||1,offset:n&&n.offset||0};const o={},i=[];let a=[],s=[];const l={attempt:I(y),check:I(x),consume:b,enter:w,exit:E,interrupt:I(x,{interrupt:!0})},c={code:null,containerState:{},defineSkip:h,events:[],now:v,parser:e,previous:null,sliceSerialize:m,sliceStream:p,write:f};let u=t.tokenize.call(c,l);return t.resolveAll&&i.push(t),c;function f(O){return a=Et(a,O),C(),a[a.length-1]!==null?[]:(R(t,0),c.events=pa(i,c.events,c),c.events)}function m(O,N){return Tw(p(O),N)}function p(O){return Pw(a,O)}function v(){const{_bufferIndex:O,_index:N,line:j,column:M,offset:L}=r;return{_bufferIndex:O,_index:N,line:j,column:M,offset:L}}function h(O){o[O.line]=O.column,T()}function C(){let O;for(;r._index<a.length;){const N=a[r._index];if(typeof N=="string")for(O=r._index,r._bufferIndex<0&&(r._bufferIndex=0);r._index===O&&r._bufferIndex<N.length;)g(N.charCodeAt(r._bufferIndex));else g(N)}}function g(O){u=u(O)}function b(O){we(O)?(r.line++,r.column=1,r.offset+=O===-3?2:1,T()):O!==-1&&(r.column++,r.offset++),r._bufferIndex<0?r._index++:(r._bufferIndex++,r._bufferIndex===a[r._index].length&&(r._bufferIndex=-1,r._index++)),c.previous=O}function w(O,N){const j=N||{};return j.type=O,j.start=v(),c.events.push(["enter",j,c]),s.push(j),j}function E(O){const N=s.pop();return N.end=v(),c.events.push(["exit",N,c]),N}function y(O,N){R(O,N.from)}function x(O,N){N.restore()}function I(O,N){return j;function j(M,L,z){let D,W,F,$;return Array.isArray(M)?q(M):"tokenize"in M?q([M]):G(M);function G(K){return ue;function ue(ce){const me=ce!==null&&K[ce],fe=ce!==null&&K.null,se=[...Array.isArray(me)?me:me?[me]:[],...Array.isArray(fe)?fe:fe?[fe]:[]];return q(se)(ce)}}function q(K){return D=K,W=0,K.length===0?z:S(K[W])}function S(K){return ue;function ue(ce){return $=k(),F=K,K.partial||(c.currentConstruct=K),K.name&&c.parser.constructs.disable.null.includes(K.name)?ne():K.tokenize.call(N?Object.assign(Object.create(c),N):c,l,Y,ne)(ce)}}function Y(K){return O(F,$),L}function ne(K){return $.restore(),++W<D.length?S(D[W]):z}}}function R(O,N){O.resolveAll&&!i.includes(O)&&i.push(O),O.resolve&&zt(c.events,N,c.events.length-N,O.resolve(c.events.slice(N),c)),O.resolveTo&&(c.events=O.resolveTo(c.events,c))}function k(){const O=v(),N=c.previous,j=c.currentConstruct,M=c.events.length,L=Array.from(s);return{from:M,restore:z};function z(){r=O,c.previous=N,c.currentConstruct=j,c.events.length=M,s=L,T()}}function T(){r.line in o&&r.column<2&&(r.column=o[r.line],r.offset+=o[r.line]-1)}}function Pw(e,t){const n=t.start._index,r=t.start._bufferIndex,o=t.end._index,i=t.end._bufferIndex;let a;if(n===o)a=[e[n].slice(r,i)];else{if(a=e.slice(n,o),r>-1){const s=a[0];typeof s=="string"?a[0]=s.slice(r):a.shift()}i>0&&a.push(e[o].slice(0,i))}return a}function Tw(e,t){let n=-1;const r=[];let o;for(;++n<e.length;){const i=e[n];let a;if(typeof i=="string")a=i;else switch(i){case-5:{a="\r";break}case-4:{a=`
`;break}case-3:{a=`\r
`;break}case-2:{a=t?" ":"	";break}case-1:{if(!t&&o)continue;a=" ";break}default:a=String.fromCharCode(i)}o=i===-2,r.push(a)}return r.join("")}function Ow(e){const r={constructs:_C([Ew,...(e||{}).extensions||[]]),content:o(VC),defined:[],document:o(XC),flow:o(uw),lazy:{},string:o(pw),text:o(mw)};return r;function o(i){return a;function a(s){return Iw(r,i,s)}}}function Rw(e){for(;!gd(e););return e}const xl=/[\0\t\n\r]/g;function Nw(){let e=1,t="",n=!0,r;return o;function o(i,a,s){const l=[];let c,u,f,m,p;for(i=t+(typeof i=="string"?i.toString():new TextDecoder(a||void 0).decode(i)),f=0,t="",n&&(i.charCodeAt(0)===65279&&f++,n=void 0);f<i.length;){if(xl.lastIndex=f,c=xl.exec(i),m=c&&c.index!==void 0?c.index:i.length,p=i.charCodeAt(m),!c){t=i.slice(f);break}if(p===10&&f===m&&r)l.push(-3),r=void 0;else switch(r&&(l.push(-5),r=void 0),f<m&&(l.push(i.slice(f,m)),e+=m-f),p){case 0:{l.push(65533),e++;break}case 9:{for(u=Math.ceil(e/4)*4,l.push(-2);e++<u;)l.push(-1);break}case 10:{l.push(-4),e=1;break}default:r=!0,e=1}f=m+1}return s&&(r&&l.push(-5),t&&l.push(t),l.push(null)),l}}const kw=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function Mw(e){return e.replace(kw,jw)}function jw(e,t,n){if(t)return t;if(n.charCodeAt(0)===35){const o=n.charCodeAt(1),i=o===120||o===88;return fd(n.slice(i?2:1),i?16:10)}return fa(n)||e}const Sd={}.hasOwnProperty;function Lw(e,t,n){return typeof t!="string"&&(n=t,t=void 0),_w(n)(Rw(Ow(n).document().write(Nw()(e,t,!0))))}function _w(e){const t={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:i(Oe),autolinkProtocol:k,autolinkEmail:k,atxHeading:i(ie),blockQuote:i(fe),characterEscape:k,characterReference:k,codeFenced:i(se),codeFencedFenceInfo:a,codeFencedFenceMeta:a,codeIndented:i(se,a),codeText:i(ee,a),codeTextData:k,data:k,codeFlowValue:k,definition:i(de),definitionDestinationString:a,definitionLabelString:a,definitionTitleString:a,emphasis:i(oe),hardBreakEscape:i(be),hardBreakTrailing:i(be),htmlFlow:i(Ie,a),htmlFlowData:k,htmlText:i(Ie,a),htmlTextData:k,image:i(ye),label:a,link:i(Oe),listItem:i($e),listItemValue:m,listOrdered:i(xe,f),listUnordered:i(xe),paragraph:i(Ae),reference:S,referenceString:a,resourceDestinationString:a,resourceTitleString:a,setextHeading:i(ie),strong:i(Re),thematicBreak:i(Ke)},exit:{atxHeading:l(),atxHeadingSequence:y,autolink:l(),autolinkEmail:me,autolinkProtocol:ce,blockQuote:l(),characterEscapeValue:T,characterReferenceMarkerHexadecimal:ne,characterReferenceMarkerNumeric:ne,characterReferenceValue:K,characterReference:ue,codeFenced:l(C),codeFencedFence:h,codeFencedFenceInfo:p,codeFencedFenceMeta:v,codeFlowValue:T,codeIndented:l(g),codeText:l(L),codeTextData:T,data:T,definition:l(),definitionDestinationString:E,definitionLabelString:b,definitionTitleString:w,emphasis:l(),hardBreakEscape:l(N),hardBreakTrailing:l(N),htmlFlow:l(j),htmlFlowData:T,htmlText:l(M),htmlTextData:T,image:l(D),label:F,labelText:W,lineEnding:O,link:l(z),listItem:l(),listOrdered:l(),listUnordered:l(),paragraph:l(),referenceString:Y,resourceDestinationString:$,resourceTitleString:G,resource:q,setextHeading:l(R),setextHeadingLineSequence:I,setextHeadingText:x,strong:l(),thematicBreak:l()}};wd(t,(e||{}).mdastExtensions||[]);const n={};return r;function r(_){let U={type:"root",children:[]};const le={stack:[U],tokenStack:[],config:t,enter:s,exit:c,buffer:a,resume:u,data:n},he=[];let Ce=-1;for(;++Ce<_.length;)if(_[Ce][1].type==="listOrdered"||_[Ce][1].type==="listUnordered")if(_[Ce][0]==="enter")he.push(Ce);else{const ze=he.pop();Ce=o(_,ze,Ce)}for(Ce=-1;++Ce<_.length;){const ze=t[_[Ce][0]];Sd.call(ze,_[Ce][1].type)&&ze[_[Ce][1].type].call(Object.assign({sliceSerialize:_[Ce][2].sliceSerialize},le),_[Ce][1])}if(le.tokenStack.length>0){const ze=le.tokenStack[le.tokenStack.length-1];(ze[1]||Cl).call(le,void 0,ze[0])}for(U.position={start:Gt(_.length>0?_[0][1].start:{line:1,column:1,offset:0}),end:Gt(_.length>0?_[_.length-2][1].end:{line:1,column:1,offset:0})},Ce=-1;++Ce<t.transforms.length;)U=t.transforms[Ce](U)||U;return U}function o(_,U,le){let he=U-1,Ce=-1,ze=!1,V,re,Z,pe;for(;++he<=le;){const X=_[he];switch(X[1].type){case"listUnordered":case"listOrdered":case"blockQuote":{X[0]==="enter"?Ce++:Ce--,pe=void 0;break}case"lineEndingBlank":{X[0]==="enter"&&(V&&!pe&&!Ce&&!Z&&(Z=he),pe=void 0);break}case"linePrefix":case"listItemValue":case"listItemMarker":case"listItemPrefix":case"listItemPrefixWhitespace":break;default:pe=void 0}if(!Ce&&X[0]==="enter"&&X[1].type==="listItemPrefix"||Ce===-1&&X[0]==="exit"&&(X[1].type==="listUnordered"||X[1].type==="listOrdered")){if(V){let J=he;for(re=void 0;J--;){const ge=_[J];if(ge[1].type==="lineEnding"||ge[1].type==="lineEndingBlank"){if(ge[0]==="exit")continue;re&&(_[re][1].type="lineEndingBlank",ze=!0),ge[1].type="lineEnding",re=J}else if(!(ge[1].type==="linePrefix"||ge[1].type==="blockQuotePrefix"||ge[1].type==="blockQuotePrefixWhitespace"||ge[1].type==="blockQuoteMarker"||ge[1].type==="listItemIndent"))break}Z&&(!re||Z<re)&&(V._spread=!0),V.end=Object.assign({},re?_[re][1].start:X[1].end),_.splice(re||he,0,["exit",V,X[2]]),he++,le++}if(X[1].type==="listItemPrefix"){const J={type:"listItem",_spread:!1,start:Object.assign({},X[1].start),end:void 0};V=J,_.splice(he,0,["enter",J,X[2]]),he++,le++,Z=void 0,pe=!0}}}return _[U][1]._spread=ze,le}function i(_,U){return le;function le(he){s.call(this,_(he),he),U&&U.call(this,he)}}function a(){this.stack.push({type:"fragment",children:[]})}function s(_,U,le){this.stack[this.stack.length-1].children.push(_),this.stack.push(_),this.tokenStack.push([U,le||void 0]),_.position={start:Gt(U.start),end:void 0}}function l(_){return U;function U(le){_&&_.call(this,le),c.call(this,le)}}function c(_,U){const le=this.stack.pop(),he=this.tokenStack.pop();if(he)he[0].type!==_.type&&(U?U.call(this,_,he[0]):(he[1]||Cl).call(this,_,he[0]));else throw new Error("Cannot close `"+_.type+"` ("+Xn({start:_.start,end:_.end})+"): it’s not open");le.position.end=Gt(_.end)}function u(){return jC(this.stack.pop())}function f(){this.data.expectingFirstListItemValue=!0}function m(_){if(this.data.expectingFirstListItemValue){const U=this.stack[this.stack.length-2];U.start=Number.parseInt(this.sliceSerialize(_),10),this.data.expectingFirstListItemValue=void 0}}function p(){const _=this.resume(),U=this.stack[this.stack.length-1];U.lang=_}function v(){const _=this.resume(),U=this.stack[this.stack.length-1];U.meta=_}function h(){this.data.flowCodeInside||(this.buffer(),this.data.flowCodeInside=!0)}function C(){const _=this.resume(),U=this.stack[this.stack.length-1];U.value=_.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),this.data.flowCodeInside=void 0}function g(){const _=this.resume(),U=this.stack[this.stack.length-1];U.value=_.replace(/(\r?\n|\r)$/g,"")}function b(_){const U=this.resume(),le=this.stack[this.stack.length-1];le.label=U,le.identifier=Sn(this.sliceSerialize(_)).toLowerCase()}function w(){const _=this.resume(),U=this.stack[this.stack.length-1];U.title=_}function E(){const _=this.resume(),U=this.stack[this.stack.length-1];U.url=_}function y(_){const U=this.stack[this.stack.length-1];if(!U.depth){const le=this.sliceSerialize(_).length;U.depth=le}}function x(){this.data.setextHeadingSlurpLineEnding=!0}function I(_){const U=this.stack[this.stack.length-1];U.depth=this.sliceSerialize(_).codePointAt(0)===61?1:2}function R(){this.data.setextHeadingSlurpLineEnding=void 0}function k(_){const le=this.stack[this.stack.length-1].children;let he=le[le.length-1];(!he||he.type!=="text")&&(he=Ue(),he.position={start:Gt(_.start),end:void 0},le.push(he)),this.stack.push(he)}function T(_){const U=this.stack.pop();U.value+=this.sliceSerialize(_),U.position.end=Gt(_.end)}function O(_){const U=this.stack[this.stack.length-1];if(this.data.atHardBreak){const le=U.children[U.children.length-1];le.position.end=Gt(_.end),this.data.atHardBreak=void 0;return}!this.data.setextHeadingSlurpLineEnding&&t.canContainEols.includes(U.type)&&(k.call(this,_),T.call(this,_))}function N(){this.data.atHardBreak=!0}function j(){const _=this.resume(),U=this.stack[this.stack.length-1];U.value=_}function M(){const _=this.resume(),U=this.stack[this.stack.length-1];U.value=_}function L(){const _=this.resume(),U=this.stack[this.stack.length-1];U.value=_}function z(){const _=this.stack[this.stack.length-1];if(this.data.inReference){const U=this.data.referenceType||"shortcut";_.type+="Reference",_.referenceType=U,delete _.url,delete _.title}else delete _.identifier,delete _.label;this.data.referenceType=void 0}function D(){const _=this.stack[this.stack.length-1];if(this.data.inReference){const U=this.data.referenceType||"shortcut";_.type+="Reference",_.referenceType=U,delete _.url,delete _.title}else delete _.identifier,delete _.label;this.data.referenceType=void 0}function W(_){const U=this.sliceSerialize(_),le=this.stack[this.stack.length-2];le.label=Mw(U),le.identifier=Sn(U).toLowerCase()}function F(){const _=this.stack[this.stack.length-1],U=this.resume(),le=this.stack[this.stack.length-1];if(this.data.inReference=!0,le.type==="link"){const he=_.children;le.children=he}else le.alt=U}function $(){const _=this.resume(),U=this.stack[this.stack.length-1];U.url=_}function G(){const _=this.resume(),U=this.stack[this.stack.length-1];U.title=_}function q(){this.data.inReference=void 0}function S(){this.data.referenceType="collapsed"}function Y(_){const U=this.resume(),le=this.stack[this.stack.length-1];le.label=U,le.identifier=Sn(this.sliceSerialize(_)).toLowerCase(),this.data.referenceType="full"}function ne(_){this.data.characterReferenceType=_.type}function K(_){const U=this.sliceSerialize(_),le=this.data.characterReferenceType;let he;le?(he=fd(U,le==="characterReferenceMarkerNumeric"?10:16),this.data.characterReferenceType=void 0):he=fa(U);const Ce=this.stack[this.stack.length-1];Ce.value+=he}function ue(_){const U=this.stack.pop();U.position.end=Gt(_.end)}function ce(_){T.call(this,_);const U=this.stack[this.stack.length-1];U.url=this.sliceSerialize(_)}function me(_){T.call(this,_);const U=this.stack[this.stack.length-1];U.url="mailto:"+this.sliceSerialize(_)}function fe(){return{type:"blockquote",children:[]}}function se(){return{type:"code",lang:null,meta:null,value:""}}function ee(){return{type:"inlineCode",value:""}}function de(){return{type:"definition",identifier:"",label:null,title:null,url:""}}function oe(){return{type:"emphasis",children:[]}}function ie(){return{type:"heading",depth:0,children:[]}}function be(){return{type:"break"}}function Ie(){return{type:"html",value:""}}function ye(){return{type:"image",title:null,url:"",alt:null}}function Oe(){return{type:"link",title:null,url:"",children:[]}}function xe(_){return{type:"list",ordered:_.type==="listOrdered",start:null,spread:_._spread,children:[]}}function $e(_){return{type:"listItem",spread:_._spread,checked:null,children:[]}}function Ae(){return{type:"paragraph",children:[]}}function Re(){return{type:"strong",children:[]}}function Ue(){return{type:"text",value:""}}function Ke(){return{type:"thematicBreak"}}}function Gt(e){return{line:e.line,column:e.column,offset:e.offset}}function wd(e,t){let n=-1;for(;++n<t.length;){const r=t[n];Array.isArray(r)?wd(e,r):Aw(e,r)}}function Aw(e,t){let n;for(n in t)if(Sd.call(t,n))switch(n){case"canContainEols":{const r=t[n];r&&e[n].push(...r);break}case"transforms":{const r=t[n];r&&e[n].push(...r);break}case"enter":case"exit":{const r=t[n];r&&Object.assign(e[n],r);break}}}function Cl(e,t){throw e?new Error("Cannot close `"+e.type+"` ("+Xn({start:e.start,end:e.end})+"): a different token (`"+t.type+"`, "+Xn({start:t.start,end:t.end})+") is open"):new Error("Cannot close document, a token (`"+t.type+"`, "+Xn({start:t.start,end:t.end})+") is still open")}function zw(e){const t=this;t.parser=n;function n(r){return Lw(r,{...t.data("settings"),...e,extensions:t.data("micromarkExtensions")||[],mdastExtensions:t.data("fromMarkdownExtensions")||[]})}}function Dw(e,t){const n={type:"element",tagName:"blockquote",properties:{},children:e.wrap(e.all(t),!0)};return e.patch(t,n),e.applyData(t,n)}function Bw(e,t){const n={type:"element",tagName:"br",properties:{},children:[]};return e.patch(t,n),[e.applyData(t,n),{type:"text",value:`
`}]}function Fw(e,t){const n=t.value?t.value+`
`:"",r={};t.lang&&(r.className=["language-"+t.lang]);let o={type:"element",tagName:"code",properties:r,children:[{type:"text",value:n}]};return t.meta&&(o.data={meta:t.meta}),e.patch(t,o),o=e.applyData(t,o),o={type:"element",tagName:"pre",properties:{},children:[o]},e.patch(t,o),o}function Hw(e,t){const n={type:"element",tagName:"del",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}function Ww(e,t){const n={type:"element",tagName:"em",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}function Vw(e,t){const n=typeof e.options.clobberPrefix=="string"?e.options.clobberPrefix:"user-content-",r=String(t.identifier).toUpperCase(),o=Rn(r.toLowerCase()),i=e.footnoteOrder.indexOf(r);let a,s=e.footnoteCounts.get(r);s===void 0?(s=0,e.footnoteOrder.push(r),a=e.footnoteOrder.length):a=i+1,s+=1,e.footnoteCounts.set(r,s);const l={type:"element",tagName:"a",properties:{href:"#"+n+"fn-"+o,id:n+"fnref-"+o+(s>1?"-"+s:""),dataFootnoteRef:!0,ariaDescribedBy:["footnote-label"]},children:[{type:"text",value:String(a)}]};e.patch(t,l);const c={type:"element",tagName:"sup",properties:{},children:[l]};return e.patch(t,c),e.applyData(t,c)}function Uw(e,t){const n={type:"element",tagName:"h"+t.depth,properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}function Xw(e,t){if(e.options.allowDangerousHtml){const n={type:"raw",value:t.value};return e.patch(t,n),e.applyData(t,n)}}function $d(e,t){const n=t.referenceType;let r="]";if(n==="collapsed"?r+="[]":n==="full"&&(r+="["+(t.label||t.identifier)+"]"),t.type==="imageReference")return[{type:"text",value:"!["+t.alt+r}];const o=e.all(t),i=o[0];i&&i.type==="text"?i.value="["+i.value:o.unshift({type:"text",value:"["});const a=o[o.length-1];return a&&a.type==="text"?a.value+=r:o.push({type:"text",value:r}),o}function Gw(e,t){const n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return $d(e,t);const o={src:Rn(r.url||""),alt:t.alt};r.title!==null&&r.title!==void 0&&(o.title=r.title);const i={type:"element",tagName:"img",properties:o,children:[]};return e.patch(t,i),e.applyData(t,i)}function qw(e,t){const n={src:Rn(t.url)};t.alt!==null&&t.alt!==void 0&&(n.alt=t.alt),t.title!==null&&t.title!==void 0&&(n.title=t.title);const r={type:"element",tagName:"img",properties:n,children:[]};return e.patch(t,r),e.applyData(t,r)}function Yw(e,t){const n={type:"text",value:t.value.replace(/\r?\n|\r/g," ")};e.patch(t,n);const r={type:"element",tagName:"code",properties:{},children:[n]};return e.patch(t,r),e.applyData(t,r)}function Kw(e,t){const n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return $d(e,t);const o={href:Rn(r.url||"")};r.title!==null&&r.title!==void 0&&(o.title=r.title);const i={type:"element",tagName:"a",properties:o,children:e.all(t)};return e.patch(t,i),e.applyData(t,i)}function Zw(e,t){const n={href:Rn(t.url)};t.title!==null&&t.title!==void 0&&(n.title=t.title);const r={type:"element",tagName:"a",properties:n,children:e.all(t)};return e.patch(t,r),e.applyData(t,r)}function Jw(e,t,n){const r=e.all(t),o=n?Qw(n):Ed(t),i={},a=[];if(typeof t.checked=="boolean"){const u=r[0];let f;u&&u.type==="element"&&u.tagName==="p"?f=u:(f={type:"element",tagName:"p",properties:{},children:[]},r.unshift(f)),f.children.length>0&&f.children.unshift({type:"text",value:" "}),f.children.unshift({type:"element",tagName:"input",properties:{type:"checkbox",checked:t.checked,disabled:!0},children:[]}),i.className=["task-list-item"]}let s=-1;for(;++s<r.length;){const u=r[s];(o||s!==0||u.type!=="element"||u.tagName!=="p")&&a.push({type:"text",value:`
`}),u.type==="element"&&u.tagName==="p"&&!o?a.push(...u.children):a.push(u)}const l=r[r.length-1];l&&(o||l.type!=="element"||l.tagName!=="p")&&a.push({type:"text",value:`
`});const c={type:"element",tagName:"li",properties:i,children:a};return e.patch(t,c),e.applyData(t,c)}function Qw(e){let t=!1;if(e.type==="list"){t=e.spread||!1;const n=e.children;let r=-1;for(;!t&&++r<n.length;)t=Ed(n[r])}return t}function Ed(e){const t=e.spread;return t??e.children.length>1}function e2(e,t){const n={},r=e.all(t);let o=-1;for(typeof t.start=="number"&&t.start!==1&&(n.start=t.start);++o<r.length;){const a=r[o];if(a.type==="element"&&a.tagName==="li"&&a.properties&&Array.isArray(a.properties.className)&&a.properties.className.includes("task-list-item")){n.className=["contains-task-list"];break}}const i={type:"element",tagName:t.ordered?"ol":"ul",properties:n,children:e.wrap(r,!0)};return e.patch(t,i),e.applyData(t,i)}function t2(e,t){const n={type:"element",tagName:"p",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}function n2(e,t){const n={type:"root",children:e.wrap(e.all(t))};return e.patch(t,n),e.applyData(t,n)}function r2(e,t){const n={type:"element",tagName:"strong",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}function o2(e,t){const n=e.all(t),r=n.shift(),o=[];if(r){const a={type:"element",tagName:"thead",properties:{},children:e.wrap([r],!0)};e.patch(t.children[0],a),o.push(a)}if(n.length>0){const a={type:"element",tagName:"tbody",properties:{},children:e.wrap(n,!0)},s=la(t.children[1]),l=id(t.children[t.children.length-1]);s&&l&&(a.position={start:s,end:l}),o.push(a)}const i={type:"element",tagName:"table",properties:{},children:e.wrap(o,!0)};return e.patch(t,i),e.applyData(t,i)}function i2(e,t,n){const r=n?n.children:void 0,i=(r?r.indexOf(t):1)===0?"th":"td",a=n&&n.type==="table"?n.align:void 0,s=a?a.length:t.children.length;let l=-1;const c=[];for(;++l<s;){const f=t.children[l],m={},p=a?a[l]:void 0;p&&(m.align=p);let v={type:"element",tagName:i,properties:m,children:[]};f&&(v.children=e.all(f),e.patch(f,v),v=e.applyData(f,v)),c.push(v)}const u={type:"element",tagName:"tr",properties:{},children:e.wrap(c,!0)};return e.patch(t,u),e.applyData(t,u)}function a2(e,t){const n={type:"element",tagName:"td",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}const Sl=9,wl=32;function s2(e){const t=String(e),n=/\r?\n|\r/g;let r=n.exec(t),o=0;const i=[];for(;r;)i.push($l(t.slice(o,r.index),o>0,!0),r[0]),o=r.index+r[0].length,r=n.exec(t);return i.push($l(t.slice(o),o>0,!1)),i.join("")}function $l(e,t,n){let r=0,o=e.length;if(t){let i=e.codePointAt(r);for(;i===Sl||i===wl;)r++,i=e.codePointAt(r)}if(n){let i=e.codePointAt(o-1);for(;i===Sl||i===wl;)o--,i=e.codePointAt(o-1)}return o>r?e.slice(r,o):""}function l2(e,t){const n={type:"text",value:s2(String(t.value))};return e.patch(t,n),e.applyData(t,n)}function c2(e,t){const n={type:"element",tagName:"hr",properties:{},children:[]};return e.patch(t,n),e.applyData(t,n)}const u2={blockquote:Dw,break:Bw,code:Fw,delete:Hw,emphasis:Ww,footnoteReference:Vw,heading:Uw,html:Xw,imageReference:Gw,image:qw,inlineCode:Yw,linkReference:Kw,link:Zw,listItem:Jw,list:e2,paragraph:t2,root:n2,strong:r2,table:o2,tableCell:a2,tableRow:i2,text:l2,thematicBreak:c2,toml:Nr,yaml:Nr,definition:Nr,footnoteDefinition:Nr};function Nr(){}const Id=-1,ho=0,qn=1,Jr=2,ha=3,ga=4,va=5,ya=6,Pd=7,Td=8,El=typeof self=="object"?self:globalThis,d2=(e,t)=>{const n=(o,i)=>(e.set(i,o),o),r=o=>{if(e.has(o))return e.get(o);const[i,a]=t[o];switch(i){case ho:case Id:return n(a,o);case qn:{const s=n([],o);for(const l of a)s.push(r(l));return s}case Jr:{const s=n({},o);for(const[l,c]of a)s[r(l)]=r(c);return s}case ha:return n(new Date(a),o);case ga:{const{source:s,flags:l}=a;return n(new RegExp(s,l),o)}case va:{const s=n(new Map,o);for(const[l,c]of a)s.set(r(l),r(c));return s}case ya:{const s=n(new Set,o);for(const l of a)s.add(r(l));return s}case Pd:{const{name:s,message:l}=a;return n(new El[s](l),o)}case Td:return n(BigInt(a),o);case"BigInt":return n(Object(BigInt(a)),o);case"ArrayBuffer":return n(new Uint8Array(a).buffer,a);case"DataView":{const{buffer:s}=new Uint8Array(a);return n(new DataView(s),a)}}return n(new El[i](a),o)};return r},Il=e=>d2(new Map,e)(0),gn="",{toString:f2}={},{keys:p2}=Object,Hn=e=>{const t=typeof e;if(t!=="object"||!e)return[ho,t];const n=f2.call(e).slice(8,-1);switch(n){case"Array":return[qn,gn];case"Object":return[Jr,gn];case"Date":return[ha,gn];case"RegExp":return[ga,gn];case"Map":return[va,gn];case"Set":return[ya,gn];case"DataView":return[qn,n]}return n.includes("Array")?[qn,n]:n.includes("Error")?[Pd,n]:[Jr,n]},kr=([e,t])=>e===ho&&(t==="function"||t==="symbol"),m2=(e,t,n,r)=>{const o=(a,s)=>{const l=r.push(a)-1;return n.set(s,l),l},i=a=>{if(n.has(a))return n.get(a);let[s,l]=Hn(a);switch(s){case ho:{let u=a;switch(l){case"bigint":s=Td,u=a.toString();break;case"function":case"symbol":if(e)throw new TypeError("unable to serialize "+l);u=null;break;case"undefined":return o([Id],a)}return o([s,u],a)}case qn:{if(l){let m=a;return l==="DataView"?m=new Uint8Array(a.buffer):l==="ArrayBuffer"&&(m=new Uint8Array(a)),o([l,[...m]],a)}const u=[],f=o([s,u],a);for(const m of a)u.push(i(m));return f}case Jr:{if(l)switch(l){case"BigInt":return o([l,a.toString()],a);case"Boolean":case"Number":case"String":return o([l,a.valueOf()],a)}if(t&&"toJSON"in a)return i(a.toJSON());const u=[],f=o([s,u],a);for(const m of p2(a))(e||!kr(Hn(a[m])))&&u.push([i(m),i(a[m])]);return f}case ha:return o([s,a.toISOString()],a);case ga:{const{source:u,flags:f}=a;return o([s,{source:u,flags:f}],a)}case va:{const u=[],f=o([s,u],a);for(const[m,p]of a)(e||!(kr(Hn(m))||kr(Hn(p))))&&u.push([i(m),i(p)]);return f}case ya:{const u=[],f=o([s,u],a);for(const m of a)(e||!kr(Hn(m)))&&u.push(i(m));return f}}const{message:c}=a;return o([s,{name:l,message:c}],a)};return i},Pl=(e,{json:t,lossy:n}={})=>{const r=[];return m2(!(t||n),!!t,new Map,r)(e),r},Qr=typeof structuredClone=="function"?(e,t)=>t&&("json"in t||"lossy"in t)?Il(Pl(e,t)):structuredClone(e):(e,t)=>Il(Pl(e,t));function h2(e,t){const n=[{type:"text",value:"↩"}];return t>1&&n.push({type:"element",tagName:"sup",properties:{},children:[{type:"text",value:String(t)}]}),n}function g2(e,t){return"Back to reference "+(e+1)+(t>1?"-"+t:"")}function v2(e){const t=typeof e.options.clobberPrefix=="string"?e.options.clobberPrefix:"user-content-",n=e.options.footnoteBackContent||h2,r=e.options.footnoteBackLabel||g2,o=e.options.footnoteLabel||"Footnotes",i=e.options.footnoteLabelTagName||"h2",a=e.options.footnoteLabelProperties||{className:["sr-only"]},s=[];let l=-1;for(;++l<e.footnoteOrder.length;){const c=e.footnoteById.get(e.footnoteOrder[l]);if(!c)continue;const u=e.all(c),f=String(c.identifier).toUpperCase(),m=Rn(f.toLowerCase());let p=0;const v=[],h=e.footnoteCounts.get(f);for(;h!==void 0&&++p<=h;){v.length>0&&v.push({type:"text",value:" "});let b=typeof n=="string"?n:n(l,p);typeof b=="string"&&(b={type:"text",value:b}),v.push({type:"element",tagName:"a",properties:{href:"#"+t+"fnref-"+m+(p>1?"-"+p:""),dataFootnoteBackref:"",ariaLabel:typeof r=="string"?r:r(l,p),className:["data-footnote-backref"]},children:Array.isArray(b)?b:[b]})}const C=u[u.length-1];if(C&&C.type==="element"&&C.tagName==="p"){const b=C.children[C.children.length-1];b&&b.type==="text"?b.value+=" ":C.children.push({type:"text",value:" "}),C.children.push(...v)}else u.push(...v);const g={type:"element",tagName:"li",properties:{id:t+"fn-"+m},children:e.wrap(u,!0)};e.patch(c,g),s.push(g)}if(s.length!==0)return{type:"element",tagName:"section",properties:{dataFootnotes:!0,className:["footnotes"]},children:[{type:"element",tagName:i,properties:{...Qr(a),id:"footnote-label"},children:[{type:"text",value:o}]},{type:"text",value:`
`},{type:"element",tagName:"ol",properties:{},children:e.wrap(s,!0)},{type:"text",value:`
`}]}}const Od=function(e){if(e==null)return C2;if(typeof e=="function")return go(e);if(typeof e=="object")return Array.isArray(e)?y2(e):b2(e);if(typeof e=="string")return x2(e);throw new Error("Expected function, string, or object as test")};function y2(e){const t=[];let n=-1;for(;++n<e.length;)t[n]=Od(e[n]);return go(r);function r(...o){let i=-1;for(;++i<t.length;)if(t[i].apply(this,o))return!0;return!1}}function b2(e){const t=e;return go(n);function n(r){const o=r;let i;for(i in e)if(o[i]!==t[i])return!1;return!0}}function x2(e){return go(t);function t(n){return n&&n.type===e}}function go(e){return t;function t(n,r,o){return!!(S2(n)&&e.call(this,n,typeof r=="number"?r:void 0,o||void 0))}}function C2(){return!0}function S2(e){return e!==null&&typeof e=="object"&&"type"in e}const Rd=[],w2=!0,Tl=!1,$2="skip";function E2(e,t,n,r){let o;typeof t=="function"&&typeof n!="function"?(r=n,n=t):o=t;const i=Od(o),a=r?-1:1;s(e,void 0,[])();function s(l,c,u){const f=l&&typeof l=="object"?l:{};if(typeof f.type=="string"){const p=typeof f.tagName=="string"?f.tagName:typeof f.name=="string"?f.name:void 0;Object.defineProperty(m,"name",{value:"node ("+(l.type+(p?"<"+p+">":""))+")"})}return m;function m(){let p=Rd,v,h,C;if((!t||i(l,c,u[u.length-1]||void 0))&&(p=I2(n(l,u)),p[0]===Tl))return p;if("children"in l&&l.children){const g=l;if(g.children&&p[0]!==$2)for(h=(r?g.children.length:-1)+a,C=u.concat(g);h>-1&&h<g.children.length;){const b=g.children[h];if(v=s(b,h,C)(),v[0]===Tl)return v;h=typeof v[1]=="number"?v[1]:h+a}}return p}}}function I2(e){return Array.isArray(e)?e:typeof e=="number"?[w2,e]:e==null?Rd:[e]}function Nd(e,t,n,r){let o,i,a;typeof t=="function"&&typeof n!="function"?(i=void 0,a=t,o=n):(i=t,a=n,o=r),E2(e,i,s,o);function s(l,c){const u=c[c.length-1],f=u?u.children.indexOf(l):void 0;return a(l,f,u)}}const Ei={}.hasOwnProperty,P2={};function T2(e,t){const n=t||P2,r=new Map,o=new Map,i=new Map,a={...u2,...n.handlers},s={all:c,applyData:R2,definitionById:r,footnoteById:o,footnoteCounts:i,footnoteOrder:[],handlers:a,one:l,options:n,patch:O2,wrap:k2};return Nd(e,function(u){if(u.type==="definition"||u.type==="footnoteDefinition"){const f=u.type==="definition"?r:o,m=String(u.identifier).toUpperCase();f.has(m)||f.set(m,u)}}),s;function l(u,f){const m=u.type,p=s.handlers[m];if(Ei.call(s.handlers,m)&&p)return p(s,u,f);if(s.options.passThrough&&s.options.passThrough.includes(m)){if("children"in u){const{children:h,...C}=u,g=Qr(C);return g.children=s.all(u),g}return Qr(u)}return(s.options.unknownHandler||N2)(s,u,f)}function c(u){const f=[];if("children"in u){const m=u.children;let p=-1;for(;++p<m.length;){const v=s.one(m[p],u);if(v){if(p&&m[p-1].type==="break"&&(!Array.isArray(v)&&v.type==="text"&&(v.value=Ol(v.value)),!Array.isArray(v)&&v.type==="element")){const h=v.children[0];h&&h.type==="text"&&(h.value=Ol(h.value))}Array.isArray(v)?f.push(...v):f.push(v)}}}return f}}function O2(e,t){e.position&&(t.position=fC(e))}function R2(e,t){let n=t;if(e&&e.data){const r=e.data.hName,o=e.data.hChildren,i=e.data.hProperties;if(typeof r=="string")if(n.type==="element")n.tagName=r;else{const a="children"in n?n.children:[n];n={type:"element",tagName:r,properties:{},children:a}}n.type==="element"&&i&&Object.assign(n.properties,Qr(i)),"children"in n&&n.children&&o!==null&&o!==void 0&&(n.children=o)}return n}function N2(e,t){const n=t.data||{},r="value"in t&&!(Ei.call(n,"hProperties")||Ei.call(n,"hChildren"))?{type:"text",value:t.value}:{type:"element",tagName:"div",properties:{},children:e.all(t)};return e.patch(t,r),e.applyData(t,r)}function k2(e,t){const n=[];let r=-1;for(t&&n.push({type:"text",value:`
`});++r<e.length;)r&&n.push({type:"text",value:`
`}),n.push(e[r]);return t&&e.length>0&&n.push({type:"text",value:`
`}),n}function Ol(e){let t=0,n=e.charCodeAt(t);for(;n===9||n===32;)t++,n=e.charCodeAt(t);return e.slice(t)}function Rl(e,t){const n=T2(e,t),r=n.one(e,void 0),o=v2(n),i=Array.isArray(r)?{type:"root",children:r}:r||{type:"root",children:[]};return o&&i.children.push({type:"text",value:`
`},o),i}function M2(e,t){return e&&"run"in e?async function(n,r){const o=Rl(n,{file:r,...t});await e.run(o,r)}:function(n,r){return Rl(n,{file:r,...e||t})}}function Nl(e){if(e)throw e}var Go,kl;function j2(){if(kl)return Go;kl=1;var e=Object.prototype.hasOwnProperty,t=Object.prototype.toString,n=Object.defineProperty,r=Object.getOwnPropertyDescriptor,o=function(c){return typeof Array.isArray=="function"?Array.isArray(c):t.call(c)==="[object Array]"},i=function(c){if(!c||t.call(c)!=="[object Object]")return!1;var u=e.call(c,"constructor"),f=c.constructor&&c.constructor.prototype&&e.call(c.constructor.prototype,"isPrototypeOf");if(c.constructor&&!u&&!f)return!1;var m;for(m in c);return typeof m>"u"||e.call(c,m)},a=function(c,u){n&&u.name==="__proto__"?n(c,u.name,{enumerable:!0,configurable:!0,value:u.newValue,writable:!0}):c[u.name]=u.newValue},s=function(c,u){if(u==="__proto__")if(e.call(c,u)){if(r)return r(c,u).value}else return;return c[u]};return Go=function l(){var c,u,f,m,p,v,h=arguments[0],C=1,g=arguments.length,b=!1;for(typeof h=="boolean"&&(b=h,h=arguments[1]||{},C=2),(h==null||typeof h!="object"&&typeof h!="function")&&(h={});C<g;++C)if(c=arguments[C],c!=null)for(u in c)f=s(h,u),m=s(c,u),h!==m&&(b&&m&&(i(m)||(p=o(m)))?(p?(p=!1,v=f&&o(f)?f:[]):v=f&&i(f)?f:{},a(h,{name:u,newValue:l(b,v,m)})):typeof m<"u"&&a(h,{name:u,newValue:m}));return h},Go}var L2=j2();const qo=Ti(L2);function Ii(e){if(typeof e!="object"||e===null)return!1;const t=Object.getPrototypeOf(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}function _2(){const e=[],t={run:n,use:r};return t;function n(...o){let i=-1;const a=o.pop();if(typeof a!="function")throw new TypeError("Expected function as last argument, not "+a);s(null,...o);function s(l,...c){const u=e[++i];let f=-1;if(l){a(l);return}for(;++f<o.length;)(c[f]===null||c[f]===void 0)&&(c[f]=o[f]);o=c,u?A2(u,s)(...c):a(null,...c)}}function r(o){if(typeof o!="function")throw new TypeError("Expected `middelware` to be a function, not "+o);return e.push(o),t}}function A2(e,t){let n;return r;function r(...a){const s=e.length>a.length;let l;s&&a.push(o);try{l=e.apply(this,a)}catch(c){const u=c;if(s&&n)throw u;return o(u)}s||(l&&l.then&&typeof l.then=="function"?l.then(i,o):l instanceof Error?o(l):i(l))}function o(a,...s){n||(n=!0,t(a,...s))}function i(a){o(null,a)}}const kt={basename:z2,dirname:D2,extname:B2,join:F2,sep:"/"};function z2(e,t){if(t!==void 0&&typeof t!="string")throw new TypeError('"ext" argument must be a string');mr(e);let n=0,r=-1,o=e.length,i;if(t===void 0||t.length===0||t.length>e.length){for(;o--;)if(e.codePointAt(o)===47){if(i){n=o+1;break}}else r<0&&(i=!0,r=o+1);return r<0?"":e.slice(n,r)}if(t===e)return"";let a=-1,s=t.length-1;for(;o--;)if(e.codePointAt(o)===47){if(i){n=o+1;break}}else a<0&&(i=!0,a=o+1),s>-1&&(e.codePointAt(o)===t.codePointAt(s--)?s<0&&(r=o):(s=-1,r=a));return n===r?r=a:r<0&&(r=e.length),e.slice(n,r)}function D2(e){if(mr(e),e.length===0)return".";let t=-1,n=e.length,r;for(;--n;)if(e.codePointAt(n)===47){if(r){t=n;break}}else r||(r=!0);return t<0?e.codePointAt(0)===47?"/":".":t===1&&e.codePointAt(0)===47?"//":e.slice(0,t)}function B2(e){mr(e);let t=e.length,n=-1,r=0,o=-1,i=0,a;for(;t--;){const s=e.codePointAt(t);if(s===47){if(a){r=t+1;break}continue}n<0&&(a=!0,n=t+1),s===46?o<0?o=t:i!==1&&(i=1):o>-1&&(i=-1)}return o<0||n<0||i===0||i===1&&o===n-1&&o===r+1?"":e.slice(o,n)}function F2(...e){let t=-1,n;for(;++t<e.length;)mr(e[t]),e[t]&&(n=n===void 0?e[t]:n+"/"+e[t]);return n===void 0?".":H2(n)}function H2(e){mr(e);const t=e.codePointAt(0)===47;let n=W2(e,!t);return n.length===0&&!t&&(n="."),n.length>0&&e.codePointAt(e.length-1)===47&&(n+="/"),t?"/"+n:n}function W2(e,t){let n="",r=0,o=-1,i=0,a=-1,s,l;for(;++a<=e.length;){if(a<e.length)s=e.codePointAt(a);else{if(s===47)break;s=47}if(s===47){if(!(o===a-1||i===1))if(o!==a-1&&i===2){if(n.length<2||r!==2||n.codePointAt(n.length-1)!==46||n.codePointAt(n.length-2)!==46){if(n.length>2){if(l=n.lastIndexOf("/"),l!==n.length-1){l<0?(n="",r=0):(n=n.slice(0,l),r=n.length-1-n.lastIndexOf("/")),o=a,i=0;continue}}else if(n.length>0){n="",r=0,o=a,i=0;continue}}t&&(n=n.length>0?n+"/..":"..",r=2)}else n.length>0?n+="/"+e.slice(o+1,a):n=e.slice(o+1,a),r=a-o-1;o=a,i=0}else s===46&&i>-1?i++:i=-1}return n}function mr(e){if(typeof e!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}const V2={cwd:U2};function U2(){return"/"}function Pi(e){return!!(e!==null&&typeof e=="object"&&"href"in e&&e.href&&"protocol"in e&&e.protocol&&e.auth===void 0)}function X2(e){if(typeof e=="string")e=new URL(e);else if(!Pi(e)){const t=new TypeError('The "path" argument must be of type string or an instance of URL. Received `'+e+"`");throw t.code="ERR_INVALID_ARG_TYPE",t}if(e.protocol!=="file:"){const t=new TypeError("The URL must be of scheme file");throw t.code="ERR_INVALID_URL_SCHEME",t}return G2(e)}function G2(e){if(e.hostname!==""){const r=new TypeError('File URL host must be "localhost" or empty on darwin');throw r.code="ERR_INVALID_FILE_URL_HOST",r}const t=e.pathname;let n=-1;for(;++n<t.length;)if(t.codePointAt(n)===37&&t.codePointAt(n+1)===50){const r=t.codePointAt(n+2);if(r===70||r===102){const o=new TypeError("File URL path must not include encoded / characters");throw o.code="ERR_INVALID_FILE_URL_PATH",o}}return decodeURIComponent(t)}const Yo=["history","path","basename","stem","extname","dirname"];class kd{constructor(t){let n;t?Pi(t)?n={path:t}:typeof t=="string"||q2(t)?n={value:t}:n=t:n={},this.cwd="cwd"in n?"":V2.cwd(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let r=-1;for(;++r<Yo.length;){const i=Yo[r];i in n&&n[i]!==void 0&&n[i]!==null&&(this[i]=i==="history"?[...n[i]]:n[i])}let o;for(o in n)Yo.includes(o)||(this[o]=n[o])}get basename(){return typeof this.path=="string"?kt.basename(this.path):void 0}set basename(t){Zo(t,"basename"),Ko(t,"basename"),this.path=kt.join(this.dirname||"",t)}get dirname(){return typeof this.path=="string"?kt.dirname(this.path):void 0}set dirname(t){Ml(this.basename,"dirname"),this.path=kt.join(t||"",this.basename)}get extname(){return typeof this.path=="string"?kt.extname(this.path):void 0}set extname(t){if(Ko(t,"extname"),Ml(this.dirname,"extname"),t){if(t.codePointAt(0)!==46)throw new Error("`extname` must start with `.`");if(t.includes(".",1))throw new Error("`extname` cannot contain multiple dots")}this.path=kt.join(this.dirname,this.stem+(t||""))}get path(){return this.history[this.history.length-1]}set path(t){Pi(t)&&(t=X2(t)),Zo(t,"path"),this.path!==t&&this.history.push(t)}get stem(){return typeof this.path=="string"?kt.basename(this.path,this.extname):void 0}set stem(t){Zo(t,"stem"),Ko(t,"stem"),this.path=kt.join(this.dirname||"",t+(this.extname||""))}fail(t,n,r){const o=this.message(t,n,r);throw o.fatal=!0,o}info(t,n,r){const o=this.message(t,n,r);return o.fatal=void 0,o}message(t,n,r){const o=new lt(t,n,r);return this.path&&(o.name=this.path+":"+o.name,o.file=this.path),o.fatal=!1,this.messages.push(o),o}toString(t){return this.value===void 0?"":typeof this.value=="string"?this.value:new TextDecoder(t||void 0).decode(this.value)}}function Ko(e,t){if(e&&e.includes(kt.sep))throw new Error("`"+t+"` cannot be a path: did not expect `"+kt.sep+"`")}function Zo(e,t){if(!e)throw new Error("`"+t+"` cannot be empty")}function Ml(e,t){if(!e)throw new Error("Setting `"+t+"` requires `path` to be set too")}function q2(e){return!!(e&&typeof e=="object"&&"byteLength"in e&&"byteOffset"in e)}const Y2=function(e){const r=this.constructor.prototype,o=r[e],i=function(){return o.apply(i,arguments)};return Object.setPrototypeOf(i,r),i},K2={}.hasOwnProperty;class ba extends Y2{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=_2()}copy(){const t=new ba;let n=-1;for(;++n<this.attachers.length;){const r=this.attachers[n];t.use(...r)}return t.data(qo(!0,{},this.namespace)),t}data(t,n){return typeof t=="string"?arguments.length===2?(ei("data",this.frozen),this.namespace[t]=n,this):K2.call(this.namespace,t)&&this.namespace[t]||void 0:t?(ei("data",this.frozen),this.namespace=t,this):this.namespace}freeze(){if(this.frozen)return this;const t=this;for(;++this.freezeIndex<this.attachers.length;){const[n,...r]=this.attachers[this.freezeIndex];if(r[0]===!1)continue;r[0]===!0&&(r[0]=void 0);const o=n.call(t,...r);typeof o=="function"&&this.transformers.use(o)}return this.frozen=!0,this.freezeIndex=Number.POSITIVE_INFINITY,this}parse(t){this.freeze();const n=Mr(t),r=this.parser||this.Parser;return Jo("parse",r),r(String(n),n)}process(t,n){const r=this;return this.freeze(),Jo("process",this.parser||this.Parser),Qo("process",this.compiler||this.Compiler),n?o(void 0,n):new Promise(o);function o(i,a){const s=Mr(t),l=r.parse(s);r.run(l,s,function(u,f,m){if(u||!f||!m)return c(u);const p=f,v=r.stringify(p,m);Q2(v)?m.value=v:m.result=v,c(u,m)});function c(u,f){u||!f?a(u):i?i(f):n(void 0,f)}}}processSync(t){let n=!1,r;return this.freeze(),Jo("processSync",this.parser||this.Parser),Qo("processSync",this.compiler||this.Compiler),this.process(t,o),Ll("processSync","process",n),r;function o(i,a){n=!0,Nl(i),r=a}}run(t,n,r){jl(t),this.freeze();const o=this.transformers;return!r&&typeof n=="function"&&(r=n,n=void 0),r?i(void 0,r):new Promise(i);function i(a,s){const l=Mr(n);o.run(t,l,c);function c(u,f,m){const p=f||t;u?s(u):a?a(p):r(void 0,p,m)}}}runSync(t,n){let r=!1,o;return this.run(t,n,i),Ll("runSync","run",r),o;function i(a,s){Nl(a),o=s,r=!0}}stringify(t,n){this.freeze();const r=Mr(n),o=this.compiler||this.Compiler;return Qo("stringify",o),jl(t),o(t,r)}use(t,...n){const r=this.attachers,o=this.namespace;if(ei("use",this.frozen),t!=null)if(typeof t=="function")l(t,n);else if(typeof t=="object")Array.isArray(t)?s(t):a(t);else throw new TypeError("Expected usable value, not `"+t+"`");return this;function i(c){if(typeof c=="function")l(c,[]);else if(typeof c=="object")if(Array.isArray(c)){const[u,...f]=c;l(u,f)}else a(c);else throw new TypeError("Expected usable value, not `"+c+"`")}function a(c){if(!("plugins"in c)&&!("settings"in c))throw new Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");s(c.plugins),c.settings&&(o.settings=qo(!0,o.settings,c.settings))}function s(c){let u=-1;if(c!=null)if(Array.isArray(c))for(;++u<c.length;){const f=c[u];i(f)}else throw new TypeError("Expected a list of plugins, not `"+c+"`")}function l(c,u){let f=-1,m=-1;for(;++f<r.length;)if(r[f][0]===c){m=f;break}if(m===-1)r.push([c,...u]);else if(u.length>0){let[p,...v]=u;const h=r[m][1];Ii(h)&&Ii(p)&&(p=qo(!0,h,p)),r[m]=[c,p,...v]}}}}const Z2=new ba().freeze();function Jo(e,t){if(typeof t!="function")throw new TypeError("Cannot `"+e+"` without `parser`")}function Qo(e,t){if(typeof t!="function")throw new TypeError("Cannot `"+e+"` without `compiler`")}function ei(e,t){if(t)throw new Error("Cannot call `"+e+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function jl(e){if(!Ii(e)||typeof e.type!="string")throw new TypeError("Expected node, got `"+e+"`")}function Ll(e,t,n){if(!n)throw new Error("`"+e+"` finished async. Use `"+t+"` instead")}function Mr(e){return J2(e)?e:new kd(e)}function J2(e){return!!(e&&typeof e=="object"&&"message"in e&&"messages"in e)}function Q2(e){return typeof e=="string"||e$(e)}function e$(e){return!!(e&&typeof e=="object"&&"byteLength"in e&&"byteOffset"in e)}const t$="https://github.com/remarkjs/react-markdown/blob/main/changelog.md",_l=[],Al={allowDangerousHtml:!0},n$=/^(https?|ircs?|mailto|xmpp)$/i,r$=[{from:"astPlugins",id:"remove-buggy-html-in-markdown-parser"},{from:"allowDangerousHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"allowNode",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowElement"},{from:"allowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowedElements"},{from:"className",id:"remove-classname"},{from:"disallowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"disallowedElements"},{from:"escapeHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"includeElementIndex",id:"#remove-includeelementindex"},{from:"includeNodeIndex",id:"change-includenodeindex-to-includeelementindex"},{from:"linkTarget",id:"remove-linktarget"},{from:"plugins",id:"change-plugins-to-remarkplugins",to:"remarkPlugins"},{from:"rawSourcePos",id:"#remove-rawsourcepos"},{from:"renderers",id:"change-renderers-to-components",to:"components"},{from:"source",id:"change-source-to-children",to:"children"},{from:"sourcePos",id:"#remove-sourcepos"},{from:"transformImageUri",id:"#add-urltransform",to:"urlTransform"},{from:"transformLinkUri",id:"#add-urltransform",to:"urlTransform"}];function o$(e){const t=i$(e),n=a$(e);return s$(t.runSync(t.parse(n),n),e)}function i$(e){const t=e.rehypePlugins||_l,n=e.remarkPlugins||_l,r=e.remarkRehypeOptions?{...e.remarkRehypeOptions,...Al}:Al;return Z2().use(zw).use(n).use(M2,r).use(t)}function a$(e){const t=e.children||"",n=new kd;return typeof t=="string"&&(n.value=t),n}function s$(e,t){const n=t.allowedElements,r=t.allowElement,o=t.components,i=t.disallowedElements,a=t.skipHtml,s=t.unwrapDisallowed,l=t.urlTransform||l$;for(const u of r$)Object.hasOwn(t,u.from)&&(""+u.from+(u.to?"use `"+u.to+"` instead":"remove it")+t$+u.id,void 0);return Nd(e,c),vC(e,{Fragment:A.Fragment,components:o,ignoreInvalidStyle:!0,jsx:A.jsx,jsxs:A.jsxs,passKeys:!0,passNode:!0});function c(u,f,m){if(u.type==="raw"&&m&&typeof f=="number")return a?m.children.splice(f,1):m.children[f]={type:"text",value:u.value},f;if(u.type==="element"){let p;for(p in Vo)if(Object.hasOwn(Vo,p)&&Object.hasOwn(u.properties,p)){const v=u.properties[p],h=Vo[p];(h===null||h.includes(u.tagName))&&(u.properties[p]=l(String(v||""),p,u))}}if(u.type==="element"){let p=n?!n.includes(u.tagName):i?i.includes(u.tagName):!1;if(!p&&r&&typeof f=="number"&&(p=!r(u,f,m)),p&&m&&typeof f=="number")return s&&u.children?m.children.splice(f,1,...u.children):m.children.splice(f,1),f}}}function l$(e){const t=e.indexOf(":"),n=e.indexOf("?"),r=e.indexOf("#"),o=e.indexOf("/");return t===-1||o!==-1&&t>o||n!==-1&&t>n||r!==-1&&t>r||n$.test(e.slice(0,t))?e:""}const c$=({sessionId:e,data:t,onOpenWorkspace:n})=>{var c;const[r,o]=d.useState([]),i=d.useCallback(async u=>{try{const f=u==="1"?t.arguments:t.results;await navigator.clipboard.writeText(f),ct.success("Copy Successful")}catch{ct.error("Copy Failed")}},[t]),a=d.useCallback(()=>{n&&n(t)},[n,e,t]),s=d.useCallback(u=>A.jsx(Ff,{size:"small",onClick:f=>f.stopPropagation(),children:A.jsx("span",{className:"action-btn",onClick:()=>i(u),children:"Copy"})}),[i]),l=[{key:"1",label:"tool_call_arguments",extra:s("1"),children:A.jsx("pre",{className:"pre-wrap",children:A.jsx("code",{children:t.arguments})})},{key:"2",label:"tool_call_result",extra:s("2"),children:A.jsx("pre",{className:"pre-wrap",children:A.jsx("code",{children:t.results})})}];return A.jsxs("div",{className:"defaultbox",children:[((c=t==null?void 0:t.artifacts)==null?void 0:c.length)>0&&A.jsx(qe,{type:"link",className:"btn-workspace",icon:A.jsx(fc,{}),onClick:a,children:"View Workspace"}),A.jsx(Hf,{activeKey:r,onChange:u=>o(Array.isArray(u)?u:[u]),items:l})]})},u$=P.memo(c$),d$=({sessionId:e,data:t,onOpenWorkspace:n})=>{var a,s;const r=(a=t==null?void 0:t.card_data)==null?void 0:a.search_items,o=Array.isArray(r)?r.filter(l=>(l==null?void 0:l.title)&&(l==null?void 0:l.link)):[],i=d.useCallback(()=>{n&&n(t)},[n,e,t]);return A.jsxs("div",{className:"cardwrap bg",children:[A.jsx(qe,{type:"link",className:"btn-workspace",icon:A.jsx(fc,{}),onClick:i,children:"View Workspace"}),A.jsxs(en,{justify:"space-between",align:"center",className:"card-length",children:[A.jsx(Wf,{icon:A.jsx(Vf,{}),children:`search keywords: ${((s=t==null?void 0:t.card_data)==null?void 0:s.query)||""}`}),A.jsxs(en,{align:"center",children:[A.jsx(_i,{className:"check-icon"}),o.length," results"]})]}),A.jsx("div",{className:"border-box",children:A.jsx(en,{className:"cardbox",children:o==null?void 0:o.map((l,c)=>A.jsxs(Vi,{title:l==null?void 0:l.title,className:"card-item",onClick:()=>(l==null?void 0:l.link)&&window.open(l==null?void 0:l.link,"_blank","noopener,noreferrer"),children:[A.jsx(Ze.Paragraph,{className:"desc",ellipsis:{rows:3,tooltip:typeof(l==null?void 0:l.snippet)=="string"?l==null?void 0:l.snippet:""},children:l==null?void 0:l.snippet}),A.jsx(Ze.Text,{ellipsis:{tooltip:typeof(l==null?void 0:l.link)=="string"?l==null?void 0:l.link:""},children:l==null?void 0:l.link})]},c))})})]})},f$=e=>{const t=/(.*?)(```tool_card\s*({[\s\S]*?})\s*```)/gs,n=[];let r=0,o;for(;(o=t.exec(e))!==null;){const[,a,s,l]=o;a&&n.push({type:"text",content:a.trim()});try{n.push({type:"tool_card",data:JSON.parse(l),raw:s.trim()})}catch(c){console.error("Failed to parse tool_card JSON:",c),n.push({type:"text",content:s.trim()})}r=t.lastIndex}const i=e.slice(r);return i.trim()&&n.push({type:"text",content:i.trim()}),{segments:n}},p$=({sessionId:e,data:t,onOpenWorkspace:n,isLoading:r=!1})=>{const o=d.useRef(null),i=l=>{n&&n(l)},{segments:a}=f$(t),s=(l,c)=>{var u,f;return!l&&!c?!0:!l||!c?!1:l.tool_call_id===c.tool_call_id&&((u=l.artifacts)==null?void 0:u.length)===((f=c.artifacts)==null?void 0:f.length)&&JSON.stringify(l.artifacts)===JSON.stringify(c.artifacts)};return d.useEffect(()=>{if(!r)return;const c=a.filter(u=>u.type==="tool_card").slice().reverse().find(u=>{var f,m;return u.type==="tool_card"&&((m=(f=u.data)==null?void 0:f.artifacts)==null?void 0:m.length)>0});if(c&&c.type==="tool_card"&&n){const u=c.data;if(s(o.current,u))console.log("latest workspace opened!",u,o.current);else{o.current=u;const f=requestAnimationFrame(()=>{i(u)});return()=>cancelAnimationFrame(f)}}},[a,n,i,r]),A.jsx("div",{className:"card",children:a.map((l,c)=>{var u;if(l.type==="text")return A.jsx("div",{className:"markdownbox",children:A.jsx(o$,{children:l.content})},`text-${c}`);if(l.type==="tool_card")return((u=l.data)==null?void 0:u.card_type)==="tool_call_card_link_list"?A.jsx(d$,{sessionId:e,data:l.data,onOpenWorkspace:i},`tool-${c}`):A.jsx(u$,{sessionId:e,data:l.data,onOpenWorkspace:i},`tool-${c}`)})})},m$=({data:e,isFirst:t,isLast:n})=>{var a,s,l;const r=e||{},o=r.summary&&(typeof r.summary=="string"?JSON.parse(r.summary).summary:(a=r.summary)==null?void 0:a.summary)||"",i=r.event_id?A.jsxs("div",{className:"Tooltipbox",children:[o.length>100?o:"",A.jsx("div",{children:r.event_id})]}):null;return A.jsx(ln,{title:i,placement:"bottom",className:"Tooltipbox",children:A.jsxs("div",{className:"custom-node",children:[A.jsx(Ze.Paragraph,{className:"summary",ellipsis:{rows:4},children:o}),A.jsx("div",{className:"name",children:r.show_name||"Unnamed Node"}),!t&&A.jsx(yr,{type:"target",position:br.Top}),!n&&A.jsx(yr,{type:"source",position:br.Bottom,id:"bottom"}),((s=r.sourceHandle)==null?void 0:s.includes("right"))&&A.jsx(yr,{type:"source",position:br.Right,id:"right"}),((l=r.sourceHandle)==null?void 0:l.includes("left"))&&A.jsx(yr,{type:"source",position:br.Left,id:"left"})]})})};async function Md(e,t={}){const{method:n="GET",headers:r={},body:o}=t;try{const i=await fetch(e,{method:n,headers:{"Content-Type":"application/json",...r},body:o?JSON.stringify(o):void 0});if(!i.ok)throw new Error(`HTTP error! status: ${i.status}`);return await i.json()}catch(i){throw i instanceof Error?ct.error(`Request failed: ${i.message}`):ct.error("Request failed: Unknown error"),i}}const h$=e=>Md(`/api/trace/agent?trace_id=${e}`),g$=(e,t)=>Math.hypot(t.x-e.x,t.y-e.y),v$=(e,t)=>{const n=new La.graphlib.Graph;return n.setDefaultEdgeLabel(()=>({})),n.setGraph({rankdir:"TB",nodesep:50,ranksep:50}),e.forEach(o=>{n.setNode(o.id,{width:200,height:100})}),t.forEach(o=>{n.setEdge(o.source,o.target)}),La.layout(n),t.forEach(o=>{const i=e.find(u=>u.id===o.source),a=e.find(u=>u.id===o.target);if(!i||!a)return;const s=n.node(o.source),l=n.node(o.target);if(g$(s,l)>300){const u=l.x>s.x?"right":"left";i.data=i.data||{},i.data.sourceHandle=i.data.sourceHandle||[],i.data.sourceHandle.push(u),o.sourceHandle=u}}),{nodes:e.map(o=>{const i=n.node(o.id);return{...o,position:{x:i.x-100,y:i.y-50}}}),edges:t}},y$={customNode:m$},b$=({traceId:e,drawerVisible:t})=>{const[n,r]=d.useState([]),[o,i]=d.useState([]),[a,s]=d.useState(!1),[l,c]=d.useState(null),u=d.useCallback(v=>{r(h=>Xf(v,h).map(g=>({...g,type:g.type||"customNode",data:g.data})))},[]),f=d.useCallback((v=[])=>v.map(h=>({id:h.span_id||h.id||"",type:"customNode",position:h.position||{x:0,y:0},data:{...h.data,label:h.show_name,summary:h.summary||"",show_name:h.show_name,event_id:h.event_id}})),[]),m=d.useCallback((v=[])=>v.map(h=>({id:`${h.source}-${h.target}`,source:h.source,target:h.target,className:"node-edge",type:"smoothstep"})),[]),p=d.useCallback(async()=>{if(!(!e||!t)){s(!0),c(null);try{const v=await h$(e),h=f((v==null?void 0:v.nodes)||[]),C=m((v==null?void 0:v.edges)||[]),{nodes:g,edges:b}=await v$(h,C);r(g),i(b)}catch(v){c("Failed to load trace data, please try again later."),console.error("Failed to fetch and build trace elements:",v)}finally{s(!1)}}},[e,t,f,m]);return d.useEffect(()=>{p()},[p]),A.jsxs("div",{className:"traceXYbox",style:{height:"100%",width:"100%"},children:[a&&A.jsx("div",{className:"loading-indicator",children:"Loading..."}),l&&A.jsx("div",{className:"error-message",children:l}),!a&&!l&&n.length===0&&A.jsx("div",{className:"empty-state",children:"No trace data available"}),n.length>0&&A.jsxs(Gf,{nodes:n,edges:o,nodeTypes:y$,nodesDraggable:!0,onNodesChange:u,snapToGrid:!0,snapGrid:[15,15],fitView:!0,minZoom:.1,maxZoom:2,children:[A.jsx(qf,{gap:16}),A.jsx(Yf,{})]})]})},x$=e=>A.jsx(Uf,{children:A.jsx(b$,{...e})}),C$=(e,t)=>Md(`api/workspaces/${e}/artifacts`,{method:"POST",body:t}),S$=({sessionId:e,toolCardData:t})=>{const[n,r]=d.useState([]),[o,i]=d.useState(),a=(t==null?void 0:t.card_type)==="tool_call_card_link_list",s=d.useRef(null);d.useEffect(()=>{if(!t)return;(async()=>{var f,m,p,v,h,C;try{const g=(m=(f=t.artifacts)==null?void 0:f[0])==null?void 0:m.artifact_type,b=(v=(p=t.artifacts)==null?void 0:p[0])==null?void 0:v.artifact_id;if(!g||!b){console.warn("Invalid artifact data");return}const w={sessionId:e,artifactType:g,artifactId:b};if(s.current&&s.current.sessionId===w.sessionId&&s.current.artifactType===w.artifactType&&s.current.artifactId===w.artifactId)return;s.current=w;const E=await C$(e,{artifact_types:[g],artifact_ids:[b]}),y=(C=(h=E==null?void 0:E.data)==null?void 0:h[0])==null?void 0:C.content;a?r(Array.isArray(y)?y:[]):i(y)}catch(g){console.error("Failed to fetch workspace artifacts:",g)}})()},[e,t,a]);const l=()=>A.jsx("div",{className:"listbox",children:n.map((u,f)=>A.jsx("div",{className:"list",children:A.jsxs(Ze.Link,{href:u==null?void 0:u.link,target:"_blank",children:[A.jsx(Ze.Paragraph,{className:"name",ellipsis:{rows:1},children:u==null?void 0:u.title}),A.jsx(Ze.Paragraph,{className:"desc",ellipsis:{rows:3},children:u==null?void 0:u.snippet}),A.jsx(Ze.Paragraph,{className:"link",ellipsis:{rows:1},children:u==null?void 0:u.link})]})},f))}),c=()=>A.jsx(Xi,{preview:!1,src:o,alt:"Workspace Artifact"});return A.jsx("div",{className:"workspacebox",children:A.jsx("div",{className:"border listwrap",children:a?l():c()})})},w$=[],$$=[],E$=kx(({token:e,css:t})=>({layout:t`
      width: 100%;
      min-width: 1000px;
      height: 100vh;
      display: flex;
      background: ${e.colorBgContainer};
      font-family: AlibabaPuHuiTi, ${e.fontFamily}, sans-serif;
    `,sider:t`
      background: ${e.colorBgLayout}80;
      width: 280px;
      height: 100%;
      display: flex;
      flex-direction: column;
      padding: 0 12px;
      box-sizing: border-box;
      transition: width 0.3s ease, padding 0.3s ease;
      position: relative;
      border-right: 1px solid ${e.colorBorderSecondary};
      
      &.collapsed {
        width: 60px;
        padding: 0 8px;
      }
      
      &.expanded {
      }
      
      .sider-content {
        display: flex;
        flex-direction: column;
        height: 100%;
        flex: 1;
        opacity: 1;
        visibility: visible;
        transition: opacity 0.3s ease, visibility 0.3s ease;
      }
    `,collapseButton:t`
      position: absolute;
      top: 50%;
      right: -10px;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background: ${e.colorBgContainer};
      border: 1px solid ${e.colorBorderSecondary};
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      z-index: 1000;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.06);
      transition: all 0.2s ease;
      transform: translateY(-50%);
      
      &:hover {
        background: ${e.colorBgTextHover};
        transform: translateY(-50%) scale(1.15);
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        border-color: ${e.colorPrimary};
      }
      
      .anticon {
        font-size: 10px;
        color: ${e.colorTextTertiary};
        transition: color 0.2s ease;
      }
      
      &:hover .anticon {
        color: ${e.colorPrimary};
      }
    `,logo:t`
      display: flex;
      align-items: center;
      justify-content: start;
      box-sizing: border-box;
      gap: 8px;
      margin: 24px 0;
      transition: justify-content 0.3s ease;
      text-decoration: none;

      span {
        font-weight: bold;
        color: ${e.colorText};
        font-size: 16px;
        transition: opacity 0.3s ease;
      }
      
      &.centered {
        justify-content: center;
        
        span {
          opacity: 0;
          width: 0;
          overflow: hidden;
        }
      }
    `,addBtn:t`
      background: #1677ff0f;
      border: 1px solid #1677ff34;
      height: 40px;
    `,conversations:t`
      flex: 1;
      overflow-y: auto;
      margin-top: 12px;
      padding: 0;

      .ant-conversations-list {
        padding-inline-start: 0;
      }
    `,siderFooter:t`
      border-top: 1px solid ${e.colorBorderSecondary};
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    `,chat:t`
      height: 100%;
      width: 100%;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      padding-block: ${e.paddingLG}px;
      gap: 16px;
      transition: margin-left 0.3s ease, margin-right 0.3s ease;
    `,chatPrompt:t`
      .ant-prompts-label {
        color: #000000e0 !important;
      }
      .ant-prompts-desc {
        color: #000000a6 !important;
        width: 100%;
      }
      .ant-prompts-icon {
        color: #000000a6 !important;
      }
    `,chatList:t`
      flex: 1;
      overflow: auto;
    `,loadingMessage:t`
      background-image: linear-gradient(90deg, #ff6b23 0%, #af3cb8 31%, #53b6ff 89%);
      background-size: 100% 2px;
      background-repeat: no-repeat;
      background-position: bottom;
    `,placeholder:t`
      width: 100%;
      max-width: 700px;
      margin: 0 auto;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
    `,sender:t`
      width: 100%;
      max-width: 700px;
      margin: 0 auto;
    `,speechButton:t`
      font-size: 18px;
      color: ${e.colorText} !important;
    `,senderPrompt:t`
      width: 100%;
      max-width: 700px;
      margin: 0 auto;
      color: ${e.colorText};
    `,sendButton:t`
      background-color: #000000 !important;
      border: none !important;
      transition: opacity 0.2s;
      
      &:hover {
        background-color: rgba(0, 0, 0, 0.7) !important;
      }
      
      &:disabled {
        opacity: 0.5 !important;
        cursor: not-allowed;
        background-color: rgba(0, 0, 0, 0.1) !important;
      }
      
      &:disabled:hover,
      &:disabled:focus {
        opacity: 0.5 !important;
        background-color: rgba(0, 0, 0, 0.1) !important;
      }
    `})),O$=()=>{const{styles:e}=E$(),t=d.useRef(null),{sessionId:n,generateNewSessionId:r,updateURLSessionId:o,setSessionId:i}=Dx(),{agentId:a,setAgentIdAndUpdateURL:s}=Mx(),[l,c]=d.useState(!0),[u,f]=d.useState({}),[m,p]=d.useState({}),[v,h]=d.useState(w$),[C,g]=d.useState(""),[b,w]=d.useState(!1),[E,y]=d.useState([]),[x,I]=d.useState(""),[R,k]=d.useState([]),[T,O]=d.useState(""),[N,j]=d.useState(!1),[M,L]=d.useState(!0),[z,D]=d.useState("TraceXY"),[W,F]=d.useState(""),[$,G]=d.useState(""),[q,S]=d.useState(null),Y={width:"40px",height:"40px",display:"flex",alignItems:"center",justifyContent:"center",border:"1px solid #1677ff34",borderRadius:"8px",transition:"all 0.2s ease"},ne={onMouseEnter:V=>{V.currentTarget.style.backgroundColor="#1677ff0f",V.currentTarget.style.borderColor="#1677ff",V.currentTarget.style.transform="scale(1.05)"},onMouseLeave:V=>{V.currentTarget.style.backgroundColor="transparent",V.currentTarget.style.borderColor="#1677ff34",V.currentTarget.style.transform="scale(1)"}},K={height:"calc(100vh - 50px)",overflow:"auto"},ue={display:"flex",alignItems:"center",justifyContent:"center",height:"200px",color:"#999"},ce=()=>{if(v.some(Z=>Z.label==="New Conversation")){ct.warning("New session already exists, please ask a question.");return}if(ie.isRequesting()){ct.error("Message is Requesting, you can create a new conversation after request done or abort it right now...");return}L(!0);const V=r();h([{key:V,label:"New Conversation",group:""},...v]),g(V),Oe([])},me=(V,re)=>{if(console.log("openRightSider",V,re),L(!1),c(!0),D(V),V==="TraceXY"&&re){F(re);const Z=m[n];if(Z!=null&&Z.messages){const pe=Z.messages.find(X=>X.trace_id===re&&X.role==="user");pe&&G(pe.content)}}else V==="Workspace"&&re&&S(re)},fe=async V=>{try{await de(),g(V),i(V),o(V);const re=m[V];if((re==null?void 0:re.messages.length)>0){const Z=re.messages.map((pe,X)=>({id:`${V}-${X}`,message:{role:pe.role,trace_id:pe.trace_id,content:pe.content},status:"success"}));Oe(Z)}else Oe((u==null?void 0:u[V])||[])}catch(re){console.error("Error fetching session data:",re)}},se=async V=>{try{(await(await fetch("/api/session/delete",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({session_id:V})})).json()).code===0?(ct.success("Session deleted"),de()):ct.error("Failed to delete session")}catch(re){console.error("Error deleting session:",re),ct.error("Failed to delete session")}},ee=async()=>{j(!0);try{const V=await fetch("/api/agent/models");if(V.ok){const re=await V.json(),Z=Object.values(re).map(pe=>({label:pe.name||pe.id,value:pe.id}));k(Z)}else ct.error("Failed to fetch models")}catch(V){console.error("Error fetching models:",V),ct.error("Error fetching models")}finally{j(!1)}},de=async()=>{try{const V=await fetch("/api/session/list");if(V.ok){const re=await V.json(),Z={};re.forEach(X=>{Z[X.session_id]=X}),p(Z);const pe=re.map(X=>{let J=X.name||X.description;if(!J&&X.messages.length>0){const ge=X.messages.find(Te=>Te.role==="user");ge?J=ge.content.length>50?ge.content.substring(0,50)+"...":ge.content:J="New Conversation"}return J||(J="New Conversation"),{key:X.session_id,label:J,group:""}});h(pe)}else console.error("Failed to fetch sessions")}catch(V){console.error("Error fetching sessions:",V)}};d.useEffect(()=>{ee(),de()},[]),d.useEffect(()=>{if(a&&R.length>0){const V=R.find(re=>re.value===a);O(V?a:""),V||s("")}},[a,R,s]);const oe=V=>{O(V),s(V)},[ie]=tb({baseURL:"/api/agent/chat/completions",model:T,dangerouslyApiKey:"Bearer sk-xxxxxxxxxxxxxxxxxxxx"}),be=ie.isRequesting(),{onRequest:Ie,messages:ye,setMessages:Oe}=G1({agent:ie,requestFallback:(V,{error:re})=>re.name==="AbortError"?{content:"Request is aborted",role:"assistant"}:{content:"Request failed, please try again!",role:"assistant"},transformMessage:V=>{var Te,We,nt,mt,Nn,kn,Pt,Vt,hr,Mn;const{originMessage:re,chunk:Z}=V||{};let pe="",X="",J="";try{if(Z!=null&&Z.data&&!(Z!=null&&Z.data.includes("DONE"))){const ht=JSON.parse(Z==null?void 0:Z.data),jn=(nt=(We=(Te=ht==null?void 0:ht.choices)==null?void 0:Te[0])==null?void 0:We.delta)==null?void 0:nt.trace_id;jn&&(J=jn),X=((kn=(Nn=(mt=ht==null?void 0:ht.choices)==null?void 0:mt[0])==null?void 0:Nn.delta)==null?void 0:kn.reasoning_content)||"",pe=((hr=(Vt=(Pt=ht==null?void 0:ht.choices)==null?void 0:Pt[0])==null?void 0:Vt.delta)==null?void 0:hr.content)||""}}catch(ht){console.error(ht)}let ge="";return!(re!=null&&re.content)&&X?ge=`<think>${X}`:(Mn=re==null?void 0:re.content)!=null&&Mn.includes("<think>")&&!(re!=null&&re.content.includes("</think>"))&&pe?ge=`${re==null?void 0:re.content}</think>${pe}`:ge=`${(re==null?void 0:re.content)||""}${X}${pe}`,!Z&&(re!=null&&re.trace_id)&&(J=re==null?void 0:re.trace_id),{content:ge,role:"assistant",trace_id:J}},resolveAbortController:V=>{t.current&&t.current.abort(),t.current=V}}),xe=()=>{const V=!l;c(V),V||L(!0)},$e=V=>{if(V!=null&&V.trim()){if(be){ct.error("Request is in progress, please wait for the request to complete.");return}Ie({stream:!0,session_id:n,message:{role:"user",content:V}}),G(V)}},Ae=async V=>{try{await navigator.clipboard.writeText(V),ct.success("Message copied to clipboard")}catch(re){console.error("Failed to copy message:",re),ct.error("Failed to copy message")}},Re=()=>A.jsxs(A.Fragment,{children:[A.jsxs("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",gap:"16px",marginTop:"20px",flex:1},children:[A.jsx(qe,{type:"text",icon:A.jsx(Fr,{}),size:"large",style:Y,title:"New Conversation",...ne,onClick:ce}),v.length>0&&A.jsxs("div",{style:{position:"relative",...Y,border:"1px solid #d9d9d9",backgroundColor:C?"#1677ff0f":"transparent",cursor:"pointer"},onMouseEnter:V=>{V.currentTarget.style.backgroundColor="#1677ff0f",V.currentTarget.style.borderColor="#1677ff",V.currentTarget.style.transform="scale(1.05)"},onMouseLeave:V=>{V.currentTarget.style.backgroundColor=C?"#1677ff0f":"transparent",V.currentTarget.style.borderColor="#d9d9d9",V.currentTarget.style.transform="scale(1)"},onClick:()=>{c(!1),L(!0)},title:`${v.length} Conversations - Click to expand`,children:[A.jsx(qe,{type:"text",icon:A.jsx(rm,{}),size:"large",style:{border:"none",background:"transparent",pointerEvents:"none"}}),A.jsx("span",{style:{position:"absolute",top:"-6px",right:"-6px",backgroundColor:"#ff4d4f",color:"white",borderRadius:"50%",width:"18px",height:"18px",fontSize:"12px",display:"flex",alignItems:"center",justifyContent:"center",fontWeight:"bold"},children:v.length>99?"99+":v.length})]})]}),A.jsxs("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",gap:"8px",borderTop:"1px solid #f0f0f0",paddingTop:"12px",marginTop:"auto"},children:[A.jsx(Vr,{size:32}),A.jsx(qe,{type:"text",icon:A.jsx(_a,{}),size:"large",style:Y,title:"Help"})]})]}),Ue=()=>A.jsxs(A.Fragment,{children:[A.jsx(qe,{onClick:ce,type:"link",className:e.addBtn,icon:A.jsx(Fr,{}),children:"New Conversation"}),A.jsx(B1,{items:v,className:e.conversations,activeKey:C,onActiveChange:fe,groupable:!1,styles:{item:{padding:"0 8px"}},menu:V=>({items:[{label:"Delete",key:"delete",icon:A.jsx(lc,{}),danger:!0,onClick:()=>se(V.key)}]})}),A.jsxs("div",{className:e.siderFooter,children:[A.jsx(Vr,{size:24}),A.jsx(qe,{type:"text",icon:A.jsx(_a,{})})]})]}),Ke=A.jsxs("div",{className:`${e.sider} ${l?"collapsed":"expanded"}`,children:[A.jsx("div",{className:e.collapseButton,onClick:xe,children:l?A.jsx(za,{}):A.jsx(wm,{})}),A.jsxs("div",{className:"sider-content",children:[A.jsxs("a",{href:"https://github.com/inclusionAI/AWorld",className:`${e.logo} ${l?"centered":""}`,target:"_blank",children:[A.jsx("img",{src:Zu,alt:"AWorld Logo",width:"32",height:"32"}),!l&&A.jsx("span",{children:"AWorld"})]}),l?Re():Ue()]})]}),_=V=>{console.log("renderMessageActions",V);const re=[{icon:A.jsx(ic,{}),onClick:()=>Ae(V.content||""),key:"copy"},{icon:A.jsx(up,{}),onClick:()=>{var Z;return me("TraceXY",(Z=V.props)==null?void 0:Z.trace_id)},key:"trace"},{icon:A.jsx(Qf,{}),onClick:()=>window.open("/trace_ui.html","_blank"),key:"alert"}];return A.jsx("div",{style:{display:"flex"},children:re.map(Z=>A.jsx(qe,{type:"text",size:"small",icon:Z.icon,onClick:Z.onClick},Z.key))})},U=A.jsx("div",{className:e.chatList,children:ye!=null&&ye.length?A.jsx(Yi.List,{items:ye.map((V,re)=>{var Z;return{...V.message,content:A.jsx(p$,{sessionId:n,data:V.message.content||"",trace_id:((Z=V.message)==null?void 0:Z.trace_id)||"",onOpenWorkspace:pe=>me("Workspace",pe),isLoading:V.status==="loading"}),classNames:{content:V.status==="loading"?e.loadingMessage:""},typing:V.status==="loading"?{step:5,interval:20,suffix:A.jsx(A.Fragment,{children:"💗"})}:!1,styles:{content:{backgroundColor:"#f5f5f5",maxWidth:"98%"}}}}),style:{height:"100%",paddingInline:"10px",margin:"0 auto",maxWidth:`calc(100vw - ${l?"60px":"280px"} - ${M?"0px":"500px"} - 40px)`},roles:{assistant:{placement:"start",footer:_,loadingRender:()=>A.jsx(Kf,{size:"small"})},user:{placement:"end"}}}):A.jsx("div",{className:e.placeholder,children:A.jsx(Hx,{onSubmit:V=>{V!=null&&V.trim()&&($e(V),I(""))},models:R,selectedModel:T,onModelChange:oe,modelsLoading:N})})}),le=()=>{x.trim()&&($e(x),I(""))},he=(V,re)=>{const{SendButton:Z,LoadingButton:pe,SpeechButton:X}=re.components;return A.jsxs(en,{gap:4,children:[A.jsx(X,{className:e.speechButton}),be?A.jsx(pe,{type:"default"}):A.jsx(Z,{type:"primary",disabled:!x.trim(),className:e.sendButton})]})},Ce=A.jsx(pi.Header,{title:"Upload File",open:b,onOpenChange:w,styles:{content:{padding:0}},children:A.jsx(Su,{beforeUpload:()=>!1,items:E,onChange:V=>y(V.fileList),placeholder:V=>V==="drop"?{title:"Drop file here"}:{icon:A.jsx(gp,{}),title:"Upload files",description:"Click or drag files to this area to upload"}})}),ze=A.jsxs(A.Fragment,{children:[A.jsx(Bx,{items:$$,onItemClick:V=>{const re=V.data.description;re!=null&&re.trim()&&$e(re)},className:e.senderPrompt}),A.jsx(pi,{value:x,header:Ce,onSubmit:le,onChange:I,onCancel:()=>{var V;return(V=t.current)==null?void 0:V.abort()},prefix:A.jsx(qe,{type:"text",icon:A.jsx(pc,{style:{fontSize:18}}),onClick:()=>w(!b)}),loading:be,className:e.sender,allowSpeech:!0,actions:he,placeholder:"Ask or input / use skills"})]});return d.useEffect(()=>{ye!=null&&ye.length&&C&&f(V=>({...V,[C]:ye}))},[ye,C]),A.jsxs("div",{className:e.layout,children:[Ke,A.jsxs("div",{className:e.chat,style:{transition:"margin-right 0.3s ease"},children:[U,(ye==null?void 0:ye.length)>0&&ze]}),!M&&A.jsxs("div",{className:`${e.sider} ${M?"collapsed":"expanded"}`,style:{right:0,width:"500px",flexShrink:0,borderLeft:"1px solid #f0f0f0",borderRight:"none"},children:[A.jsx("div",{className:e.collapseButton,style:{left:"-10px",right:"auto"},onClick:()=>L(!0),children:A.jsx(za,{})}),A.jsxs("div",{className:"sider-content",children:[z==="Workspace"&&A.jsx(Ur,{size:"small",style:{height:"100%"},items:[{key:"Workspace",label:"Workspace",children:A.jsx("div",{style:K,children:q?A.jsx(S$,{sessionId:n,toolCardData:q},`workspace-${M}`):A.jsx("div",{style:ue,children:"No workspace data available"})})}]}),z==="TraceXY"&&A.jsx(Ur,{size:"small",style:{height:"100%"},items:[{key:"TraceXY",label:"Trace",children:A.jsx("div",{style:K,children:A.jsx(x$,{traceId:W,traceQuery:$,drawerVisible:!M},`${W}-${M}`)})}]})]})]})]})};export{O$ as default};
