.chatPrompt .ant-prompts-label{color:#000000e0!important}.chatPrompt .ant-prompts-desc{color:#000000a6!important;width:100%}.chatPrompt .ant-prompts-icon{color:#000000a6!important}.welcome-container{display:flex;justify-content:center;background-color:#fff;align-items:center;position:relative;bottom:50px}.content{width:100%;display:flex;flex-direction:column;align-items:center}.logo-title-container{display:flex;align-items:center;justify-content:center;gap:12px;margin-bottom:8px}.logo-title-container img{transition:transform .2s ease}.logo-title-container img:hover{transform:scale(1.1)}.logo-title-container .aworld-link{color:inherit;text-decoration:none;transition:color .2s ease}.logo-title-container .aworld-link:hover{color:#1677ff}.input-area{position:relative;width:100%;margin-top:24px}.text-input{border-radius:20px;padding:12px 50px 50px 20px;border:1px solid #d9d9d9;font-size:16px}.submit-button{position:absolute;right:12px;bottom:12px;width:40px!important;height:40px!important;background-color:#000;border:none;transition:opacity .2s}.submit-button:hover,.submit-button:focus{background-color:#000000b3!important}.submit-button:disabled{opacity:.5;cursor:not-allowed;background-color:#0000001a!important}.submit-button:disabled:hover,.submit-button:disabled:focus{opacity:.5;background-color:#0000001a!important}.controls-area{width:100%;margin-top:20px}.model-select{width:100%;height:44px;border-radius:50px}.model-select .ant-select-selector{border-radius:12px!important;padding-left:12px!important;border:1px solid #d9d9d9!important}.model-select .ant-select-selection-item{padding-right:24px!important}.model-select .ant-select-arrow{right:15px}.select-item{line-height:30px}.select-item small{margin-left:10px;color:#b8b8b8;font-weight:400}.select-item .icon-right{color:#d9d9d9}.defaultbox{position:relative}.defaultbox .btn-workspace{position:absolute;top:-40px;right:0}.defaultbox .pre-wrap{white-space:pre-wrap}.defaultbox .action-btn{color:#1890ff;cursor:pointer;transition:color .3s;padding:0 4px}.defaultbox .action-btn:hover{color:#40a9ff}.defaultbox .action-btn:active{color:#096dd9}.defaultbox .ant-collapse{width:100%}.cardwrap{background-color:#eee;border-radius:10px;padding:10px;position:relative}.cardwrap .btn-workspace{position:absolute;top:-38px;right:-6px}.cardwrap .card-length{font-size:14px;color:#333}.cardwrap .card-length .ant-tag{max-width:480px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;padding:0 10px;border-radius:8px;line-height:24px}.cardwrap .card-length .check-icon{color:#1890ff;margin-right:8px;font-size:16px}.cardbox{overflow-x:auto;margin-top:10px}.cardbox .card-item{width:175px;min-width:175px}.cardbox .card-item .ant-card-head{padding:0 14px;min-height:50px}.cardbox .card-item .ant-card-body{padding:10px 14px 12px}.cardbox .card-item .ant-card-body .desc{margin-bottom:0}.cardbox .card-item+.card-item{margin-left:6px}.markdownbox p>strong{padding-left:5px}.markdownbox pre{white-space:pre-wrap}.traceXYbox{width:80%;max-width:700px;height:100%;position:relative;top:-20px;background:#f8f9fa;border-radius:8px;box-shadow:0 2px 8px #0000001a;overflow:hidden}.traceXYbox .react-flow__node{width:300px;min-width:14.5%;text-align:center;max-width:30%;border:1px solid #d9d9d9;border-radius:8px;background:linear-gradient(135deg,#fff,#f8f8f8);box-shadow:0 2px 6px #0000001a;font-size:10px;margin-bottom:25px}.traceXYbox .react-flow__node:hover{box-shadow:0 4px 12px #00000026;transform:translateY(-2px)}.traceXYbox .react-flow__node-selected{border-color:#1890ff;box-shadow:0 0 0 2px #1890ff33}.traceXYbox .react-flow__node .desc{margin:0;font-size:12px}.traceXYbox .react-flow__handle{background-color:#ccc}.traceXYbox .react-flow__edge-path{stroke:#ddd;stroke-width:2;animation:dashdraw .5s linear}.traceXYbox .react-flow__controls{box-shadow:0 2px 8px #0000001a;border-radius:4px;overflow:hidden}.traceXYbox .trace-id{position:absolute;bottom:15px;right:15px;background:#ffffffe6;padding:6px 12px;border-radius:20px;font-size:10px;color:#666;box-shadow:0 1px 4px #0000001a;border:1px solid #eee}@keyframes dashdraw{0%{stroke-dashoffset:100}}.Tooltipbox{padding:5px 8px}.Tooltipbox .summary{margin:0;line-height:1.4;font-size:12px;text-align:left}.Tooltipbox pre{white-space:pre-wrap;word-break:break-word;word-wrap:break-word}.empty-state{display:flex;justify-content:center;align-items:center;height:100%;color:#888}.virtual-node-edge,.node-edge{pointer-events:none!important}.virtual-node-edge:hover,.node-edge:hover,.virtual-node-edge-selected,.node-edge-selected{box-shadow:none!important;transform:none!important;border-color:transparent!important}.workspacebox{width:100%;box-sizing:border-box}.workspacebox .btn{color:#555;height:28px;background-color:#daffd5;border-radius:10px;position:fixed;top:14px;right:380px}.workspacebox .btn:hover{color:#555!important;border:1px solid #daffd5!important;background-color:#f6ffed!important}.workspacebox.border,.workspacebox .border{border:1px solid #c1c1c1;border-radius:10px}.workspacebox .tabbox{width:100%;box-sizing:border-box;margin-bottom:12px}.workspacebox .tabbox .num{width:30px;height:30px;text-align:center;line-height:30px;border-radius:50%;margin-right:10px;background-color:#efefef}.workspacebox .tabbox .tab{width:29%;padding:5px 10px;cursor:pointer}.workspacebox .tabbox .tab.active .num{background-color:#c4efa6;color:#555}.workspacebox .tabbox .tab .name{font-size:14px}.workspacebox .tabbox .tab .desc{font-size:12px;color:#999}.workspacebox .listwrap{background-color:#fafafa}.workspacebox .listwrap .title{text-align:center;line-height:40px;border-bottom:1px solid #a7a7a7}.workspacebox .listwrap .listbox .list{padding:10px 14px}.workspacebox .listwrap .listbox .list .name{font-size:14px;margin-bottom:3px;display:flex;align-items:center}.workspacebox .listwrap .listbox .list .name:before{display:inline-block;width:12px;height:12px;margin-right:5px;border-radius:50%;border:1px solid #999;background-color:#d8d8d8}.workspacebox .listwrap .listbox .list .desc,.workspacebox .listwrap .listbox .list .link{color:#999;font-size:12px}.workspacebox .listwrap .listbox .list .desc{margin-bottom:0}.workspacebox .listwrap .listbox .list:not(:last-child){border-bottom:1px solid #a7a7a7}
