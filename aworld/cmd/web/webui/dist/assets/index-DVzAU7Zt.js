import{r as a,R as J,a as Qa,j as ne}from"./index-C7nkBYbk.js";import{I as ot,_ as le,ah as Wt,ao as ft,ad as hn,g as Ce,e as G,al as z,d as A,o as en,q as tn,r as L,t as Ct,as as Yt,w as St,a2 as jo,bC as _o,x as Cn,be as Lo,bD as Ho,bE as Ao,h as Za,a3 as cr,p as kt,f as pt,b0 as Nt,aw as dr,a as we,ag as Fo,b as el,C as Un,k as qe,a_ as xt,P as tl,ac as ur,y as nl,z as _r,S as rl,v as Wo,A as ol,E as al,F as ll,G as il,H as Lr,bF as sl,bG as cl,bH as dl,bI as ul,bJ as fl,bK as vl,J as ml,a4 as Vo,N as fr,m as pl,bL as gl,aC as Hr,aB as Ar,bo as vr,an as Mt,ay as bn,bM as Fr,bN as qo,bk as mr,a6 as pr,bO as hl,bP as Wr,l as bl,bQ as gr,az as Vr,bR as qr,bS as Xr,a$ as yl,bT as Xo,a1 as hr,bl as br,bU as Cl,bb as xl,b3 as Sl,b4 as wl,b5 as $l,b6 as $e,b7 as El,b9 as Nl,K as kl,aY as Rl,af as Gr,bc as Ol,bV as Il,bs as Pl,B as Rn,bW as Go,bX as Ur,bY as Tl,bZ as Kl,ae as Jr,ar as Dl,aW as Ml,L as Bl,aA as vn,Y as zl,b_ as jl,bB as _l,bA as Ll,b$ as Tt,at as Uo,bu as Dt,bq as En,bj as Hn,c0 as Hl,c1 as Jo,au as Al,ba as Fl,aZ as Wl,bt as Yr,br as Vl,bn as Jn,c2 as ql,bv as Qr,bw as Xl,c3 as Gl,c4 as Ul,by as Jl,bz as Yl,c5 as Ql}from"./index-CcSzV6ZK.js";var Zl={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"}}]},name:"caret-down",theme:"filled"},ei=function(t,r){return a.createElement(ot,le({},t,{ref:r,icon:Zl}))},ti=a.forwardRef(ei),ni={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"}}]},name:"caret-down",theme:"outlined"},ri=function(t,r){return a.createElement(ot,le({},t,{ref:r,icon:ni}))},oi=a.forwardRef(ri),ai={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"}}]},name:"caret-up",theme:"outlined"},li=function(t,r){return a.createElement(ot,le({},t,{ref:r,icon:ai}))},ii=a.forwardRef(li),si={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M272.9 512l265.4-339.1c4.1-5.2.4-12.9-6.3-12.9h-77.3c-4.9 0-9.6 2.3-12.6 6.1L186.8 492.3a31.99 31.99 0 000 39.5l255.3 326.1c3 3.9 7.7 6.1 12.6 6.1H532c6.7 0 10.4-7.7 6.3-12.9L272.9 512zm304 0l265.4-339.1c4.1-5.2.4-12.9-6.3-12.9h-77.3c-4.9 0-9.6 2.3-12.6 6.1L490.8 492.3a31.99 31.99 0 000 39.5l255.3 326.1c3 3.9 7.7 6.1 12.6 6.1H836c6.7 0 10.4-7.7 6.3-12.9L576.9 512z"}}]},name:"double-left",theme:"outlined"},ci=function(t,r){return a.createElement(ot,le({},t,{ref:r,icon:si}))},Zr=a.forwardRef(ci),di={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M533.2 492.3L277.9 166.1c-3-3.9-7.7-6.1-12.6-6.1H188c-6.7 0-10.4 7.7-6.3 12.9L447.1 512 181.7 851.1A7.98 7.98 0 00188 864h77.3c4.9 0 9.6-2.3 12.6-6.1l255.3-326.1c9.1-11.7 9.1-27.9 0-39.5zm304 0L581.9 166.1c-3-3.9-7.7-6.1-12.6-6.1H492c-6.7 0-10.4 7.7-6.3 12.9L751.1 512 485.7 851.1A7.98 7.98 0 00492 864h77.3c4.9 0 9.6-2.3 12.6-6.1l255.3-326.1c9.1-11.7 9.1-27.9 0-39.5z"}}]},name:"double-right",theme:"outlined"},ui=function(t,r){return a.createElement(ot,le({},t,{ref:r,icon:di}))},eo=a.forwardRef(ui),fi={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494z"}}]},name:"file",theme:"outlined"},vi=function(t,r){return a.createElement(ot,le({},t,{ref:r,icon:fi}))},Yo=a.forwardRef(vi),mi={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M349 838c0 17.7 14.2 32 31.8 32h262.4c17.6 0 31.8-14.3 31.8-32V642H349v196zm531.1-684H143.9c-24.5 0-39.8 26.7-27.5 48l221.3 376h348.8l221.3-376c12.1-21.3-3.2-48-27.7-48z"}}]},name:"filter",theme:"filled"},pi=function(t,r){return a.createElement(ot,le({},t,{ref:r,icon:mi}))},gi=a.forwardRef(pi),hi={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 444H820V330.4c0-17.7-14.3-32-32-32H473L355.7 186.2a8.15 8.15 0 00-5.5-2.2H96c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h698c13 0 24.8-7.9 29.7-20l134-332c1.5-3.8 2.3-7.9 2.3-12 0-17.7-14.3-32-32-32zM136 256h188.5l119.6 114.4H748V444H238c-13 0-24.8 7.9-29.7 20L136 643.2V256zm635.3 512H159l103.3-256h612.4L771.3 768z"}}]},name:"folder-open",theme:"outlined"},bi=function(t,r){return a.createElement(ot,le({},t,{ref:r,icon:hi}))},yi=a.forwardRef(bi),Ci={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 298.4H521L403.7 186.2a8.15 8.15 0 00-5.5-2.2H144c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V330.4c0-17.7-14.3-32-32-32zM840 768H184V256h188.5l119.6 114.4H840V768z"}}]},name:"folder",theme:"outlined"},xi=function(t,r){return a.createElement(ot,le({},t,{ref:r,icon:Ci}))},Qo=a.forwardRef(xi),Si={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.4 800.9c.2-.3.5-.6.7-.9C920.6 722.1 960 621.7 960 512s-39.4-210.1-104.8-288c-.2-.3-.5-.5-.7-.8-1.1-1.3-2.1-2.5-3.2-3.7-.4-.5-.8-.9-1.2-1.4l-4.1-4.7-.1-.1c-1.5-1.7-3.1-3.4-4.6-5.1l-.1-.1c-3.2-3.4-6.4-6.8-9.7-10.1l-.1-.1-4.8-4.8-.3-.3c-1.5-1.5-3-2.9-4.5-4.3-.5-.5-1-1-1.6-1.5-1-1-2-1.9-3-2.8-.3-.3-.7-.6-1-1C736.4 109.2 629.5 64 512 64s-224.4 45.2-304.3 119.2c-.3.3-.7.6-1 1-1 .9-2 1.9-3 2.9-.5.5-1 1-1.6 1.5-1.5 1.4-3 2.9-4.5 4.3l-.3.3-4.8 4.8-.1.1c-3.3 3.3-6.5 6.7-9.7 10.1l-.1.1c-1.6 1.7-3.1 3.4-4.6 5.1l-.1.1c-1.4 1.5-2.8 3.1-4.1 4.7-.4.5-.8.9-1.2 1.4-1.1 1.2-2.1 2.5-3.2 3.7-.2.3-.5.5-.7.8C103.4 301.9 64 402.3 64 512s39.4 210.1 104.8 288c.2.3.5.6.7.9l3.1 3.7c.4.5.8.9 1.2 1.4l4.1 4.7c0 .1.1.1.1.2 1.5 1.7 3 3.4 4.6 5l.1.1c3.2 3.4 6.4 6.8 9.6 10.1l.1.1c1.6 1.6 3.1 3.2 4.7 4.7l.3.3c3.3 3.3 6.7 6.5 10.1 9.6 80.1 74 187 119.2 304.5 119.2s224.4-45.2 304.3-119.2a300 300 0 0010-9.6l.3-.3c1.6-1.6 3.2-3.1 4.7-4.7l.1-.1c3.3-3.3 6.5-6.7 9.6-10.1l.1-.1c1.5-1.7 3.1-3.3 4.6-5 0-.1.1-.1.1-.2 1.4-1.5 2.8-3.1 4.1-4.7.4-.5.8-.9 1.2-1.4a99 99 0 003.3-3.7zm4.1-142.6c-13.8 32.6-32 62.8-54.2 90.2a444.07 444.07 0 00-81.5-55.9c11.6-46.9 18.8-98.4 20.7-152.6H887c-3 40.9-12.6 80.6-28.5 118.3zM887 484H743.5c-1.9-54.2-9.1-105.7-20.7-152.6 29.3-15.6 56.6-34.4 81.5-55.9A373.86 373.86 0 01887 484zM658.3 165.5c39.7 16.8 75.8 40 107.6 69.2a394.72 394.72 0 01-59.4 41.8c-15.7-45-35.8-84.1-59.2-115.4 3.7 1.4 7.4 2.9 11 4.4zm-90.6 700.6c-9.2 7.2-18.4 12.7-27.7 16.4V697a389.1 389.1 0 01115.7 26.2c-8.3 24.6-17.9 47.3-29 67.8-17.4 32.4-37.8 58.3-59 75.1zm59-633.1c11 20.6 20.7 43.3 29 67.8A389.1 389.1 0 01540 327V141.6c9.2 3.7 18.5 9.1 27.7 16.4 21.2 16.7 41.6 42.6 59 75zM540 640.9V540h147.5c-1.6 44.2-7.1 87.1-16.3 127.8l-.3 1.2A445.02 445.02 0 00540 640.9zm0-156.9V383.1c45.8-2.8 89.8-12.5 130.9-28.1l.3 1.2c9.2 40.7 14.7 83.5 16.3 127.8H540zm-56 56v100.9c-45.8 2.8-89.8 12.5-130.9 28.1l-.3-1.2c-9.2-40.7-14.7-83.5-16.3-127.8H484zm-147.5-56c1.6-44.2 7.1-87.1 16.3-127.8l.3-1.2c41.1 15.6 85 25.3 130.9 28.1V484H336.5zM484 697v185.4c-9.2-3.7-18.5-9.1-27.7-16.4-21.2-16.7-41.7-42.7-59.1-75.1-11-20.6-20.7-43.3-29-67.8 37.2-14.6 75.9-23.3 115.8-26.1zm0-370a389.1 389.1 0 01-115.7-26.2c8.3-24.6 17.9-47.3 29-67.8 17.4-32.4 37.8-58.4 59.1-75.1 9.2-7.2 18.4-12.7 27.7-16.4V327zM365.7 165.5c3.7-1.5 7.3-3 11-4.4-23.4 31.3-43.5 70.4-59.2 115.4-21-12-40.9-26-59.4-41.8 31.8-29.2 67.9-52.4 107.6-69.2zM165.5 365.7c13.8-32.6 32-62.8 54.2-90.2 24.9 21.5 52.2 40.3 81.5 55.9-11.6 46.9-18.8 98.4-20.7 152.6H137c3-40.9 12.6-80.6 28.5-118.3zM137 540h143.5c1.9 54.2 9.1 105.7 20.7 152.6a444.07 444.07 0 00-81.5 55.9A373.86 373.86 0 01137 540zm228.7 318.5c-39.7-16.8-75.8-40-107.6-69.2 18.5-15.8 38.4-29.7 59.4-41.8 15.7 45 35.8 84.1 59.2 115.4-3.7-1.4-7.4-2.9-11-4.4zm292.6 0c-3.7 1.5-7.3 3-11 4.4 23.4-31.3 43.5-70.4 59.2-115.4 21 12 40.9 26 59.4 41.8a373.81 373.81 0 01-107.6 69.2z"}}]},name:"global",theme:"outlined"},wi=function(t,r){return a.createElement(ot,le({},t,{ref:r,icon:Si}))},$i=a.forwardRef(wi),Ei={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M300 276.5a56 56 0 1056-97 56 56 0 00-56 97zm0 284a56 56 0 1056-97 56 56 0 00-56 97zM640 228a56 56 0 10112 0 56 56 0 00-112 0zm0 284a56 56 0 10112 0 56 56 0 00-112 0zM300 844.5a56 56 0 1056-97 56 56 0 00-56 97zM640 796a56 56 0 10112 0 56 56 0 00-112 0z"}}]},name:"holder",theme:"outlined"},Ni=function(t,r){return a.createElement(ot,le({},t,{ref:r,icon:Ei}))},ki=a.forwardRef(Ni),Ri={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M328 544h368c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"}},{tag:"path",attrs:{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"}}]},name:"minus-square",theme:"outlined"},Oi=function(t,r){return a.createElement(ot,le({},t,{ref:r,icon:Ri}))},Ii=a.forwardRef(Oi),Pi={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M328 544h152v152c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V544h152c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H544V328c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v152H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"}},{tag:"path",attrs:{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"}}]},name:"plus-square",theme:"outlined"},Ti=function(t,r){return a.createElement(ot,le({},t,{ref:r,icon:Pi}))},Ki=a.forwardRef(Ti),Di={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M758.2 839.1C851.8 765.9 912 651.9 912 523.9 912 303 733.5 124.3 512.6 124 291.4 123.7 112 302.8 112 523.9c0 125.2 57.5 236.9 147.6 310.2 3.5 2.8 8.6 2.2 11.4-1.3l39.4-50.5c2.7-3.4 2.1-8.3-1.2-11.1-8.1-6.6-15.9-13.7-23.4-21.2a318.64 318.64 0 01-68.6-101.7C200.4 609 192 567.1 192 523.9s8.4-85.1 25.1-124.5c16.1-38.1 39.2-72.3 68.6-101.7 29.4-29.4 63.6-52.5 101.7-68.6C426.9 212.4 468.8 204 512 204s85.1 8.4 124.5 25.1c38.1 16.1 72.3 39.2 101.7 68.6 29.4 29.4 52.5 63.6 68.6 101.7 16.7 39.4 25.1 81.3 25.1 124.5s-8.4 85.1-25.1 124.5a318.64 318.64 0 01-68.6 101.7c-9.3 9.3-19.1 18-29.3 26L668.2 724a8 8 0 00-14.1 3l-39.6 162.2c-1.2 5 2.6 9.9 7.7 9.9l167 .8c6.7 0 10.5-7.7 6.3-12.9l-37.3-47.9z"}}]},name:"redo",theme:"outlined"},Mi=function(t,r){return a.createElement(ot,le({},t,{ref:r,icon:Di}))},Bi=a.forwardRef(Mi),zi={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M909.1 209.3l-56.4 44.1C775.8 155.1 656.2 92 521.9 92 290 92 102.3 279.5 102 511.5 101.7 743.7 289.8 932 521.9 932c181.3 0 335.8-115 394.6-276.1 1.5-4.2-.7-8.9-4.9-10.3l-56.7-19.5a8 8 0 00-10.1 4.8c-1.8 5-3.8 10-5.9 14.9-17.3 41-42.1 77.8-73.7 109.4A344.77 344.77 0 01655.9 829c-42.3 17.9-87.4 27-133.8 27-46.5 0-91.5-9.1-133.8-27A341.5 341.5 0 01279 755.2a342.16 342.16 0 01-73.7-109.4c-17.9-42.4-27-87.4-27-133.9s9.1-91.5 27-133.9c17.3-41 42.1-77.8 73.7-109.4 31.6-31.6 68.4-56.4 109.3-73.8 42.3-17.9 87.4-27 133.8-27 46.5 0 91.5 9.1 133.8 27a341.5 341.5 0 01109.3 73.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.6 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c-.1-6.6-7.8-10.3-13-6.2z"}}]},name:"reload",theme:"outlined"},ji=function(t,r){return a.createElement(ot,le({},t,{ref:r,icon:zi}))},_i=a.forwardRef(ji),Li={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M893.3 293.3L730.7 130.7c-7.5-7.5-16.7-13-26.7-16V112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V338.5c0-17-6.7-33.2-18.7-45.2zM384 184h256v104H384V184zm456 656H184V184h136v136c0 17.7 14.3 32 32 32h320c17.7 0 32-14.3 32-32V205.8l136 136V840zM512 442c-79.5 0-144 64.5-144 144s64.5 144 144 144 144-64.5 144-144-64.5-144-144-144zm0 224c-44.2 0-80-35.8-80-80s35.8-80 80-80 80 35.8 80 80-35.8 80-80 80z"}}]},name:"save",theme:"outlined"},Hi=function(t,r){return a.createElement(ot,le({},t,{ref:r,icon:Li}))},Ai=a.forwardRef(Hi),Fi={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M511.4 124C290.5 124.3 112 303 112 523.9c0 128 60.2 242 153.8 315.2l-37.5 48c-4.1 5.3-.3 13 6.3 12.9l167-.8c5.2 0 9-4.9 7.7-9.9L369.8 727a8 8 0 00-14.1-3L315 776.1c-10.2-8-20-16.7-29.3-26a318.64 318.64 0 01-68.6-101.7C200.4 609 192 567.1 192 523.9s8.4-85.1 25.1-124.5c16.1-38.1 39.2-72.3 68.6-101.7 29.4-29.4 63.6-52.5 101.7-68.6C426.9 212.4 468.8 204 512 204s85.1 8.4 124.5 25.1c38.1 16.1 72.3 39.2 101.7 68.6 29.4 29.4 52.5 63.6 68.6 101.7 16.7 39.4 25.1 81.3 25.1 124.5s-8.4 85.1-25.1 124.5a318.64 318.64 0 01-68.6 101.7c-7.5 7.5-15.3 14.5-23.4 21.2a7.93 7.93 0 00-1.2 11.1l39.4 50.5c2.8 3.5 7.9 4.1 11.4 1.3C854.5 760.8 912 649.1 912 523.9c0-221.1-179.4-400.2-400.6-399.9z"}}]},name:"undo",theme:"outlined"},Wi=function(t,r){return a.createElement(ot,le({},t,{ref:r,icon:Fi}))},Vi=a.forwardRef(Wi);function Yn(e){return e!=null&&e===e.window}const qi=e=>{var t,r;if(typeof window>"u")return 0;let n=0;return Yn(e)?n=e.pageYOffset:e instanceof Document?n=e.documentElement.scrollTop:(e instanceof HTMLElement||e)&&(n=e.scrollTop),e&&!Yn(e)&&typeof n!="number"&&(n=(r=((t=e.ownerDocument)!==null&&t!==void 0?t:e).documentElement)===null||r===void 0?void 0:r.scrollTop),n};function Xi(e,t,r,n){const o=r-t;return e/=n/2,e<1?o/2*e*e*e+t:o/2*((e-=2)*e*e+2)+t}function Gi(e,t={}){const{getContainer:r=()=>window,callback:n,duration:o=450}=t,l=r(),d=qi(l),i=Date.now(),s=()=>{const v=Date.now()-i,u=Xi(v>o?o:v,d,e,o);Yn(l)?l.scrollTo(window.pageXOffset,u):l instanceof Document||l.constructor.name==="HTMLDocument"?l.documentElement.scrollTop=u:l.scrollTop=u,v<o?Wt(s):typeof n=="function"&&n()};Wt(s)}const Zo=a.createContext(null),Ui=Zo.Provider,ea=a.createContext(null),Ji=ea.Provider;var Yi=["prefixCls","className","style","checked","disabled","defaultChecked","type","title","onChange"],ta=a.forwardRef(function(e,t){var r=e.prefixCls,n=r===void 0?"rc-checkbox":r,o=e.className,l=e.style,d=e.checked,i=e.disabled,s=e.defaultChecked,c=s===void 0?!1:s,v=e.type,u=v===void 0?"checkbox":v,m=e.title,f=e.onChange,p=ft(e,Yi),b=a.useRef(null),g=a.useRef(null),h=hn(c,{value:d}),x=Ce(h,2),C=x[0],S=x[1];a.useImperativeHandle(t,function(){return{focus:function(R){var y;(y=b.current)===null||y===void 0||y.focus(R)},blur:function(){var R;(R=b.current)===null||R===void 0||R.blur()},input:b.current,nativeElement:g.current}});var w=G(n,o,z(z({},"".concat(n,"-checked"),C),"".concat(n,"-disabled"),i)),E=function(R){i||("checked"in e||S(R.target.checked),f==null||f({target:A(A({},e),{},{type:u,checked:R.target.checked}),stopPropagation:function(){R.stopPropagation()},preventDefault:function(){R.preventDefault()},nativeEvent:R.nativeEvent}))};return a.createElement("span",{className:w,title:m,style:l,ref:g},a.createElement("input",le({},p,{className:"".concat(n,"-input"),ref:b,onChange:E,disabled:i,checked:!!C,type:u})),a.createElement("span",{className:"".concat(n,"-inner")}))});function na(e){const t=J.useRef(null),r=()=>{Wt.cancel(t.current),t.current=null};return[()=>{r(),t.current=Wt(()=>{t.current=null})},l=>{t.current&&(l.stopPropagation(),r()),e==null||e(l)}]}const Qi=e=>{const{componentCls:t,antCls:r}=e,n=`${t}-group`;return{[n]:Object.assign(Object.assign({},Ct(e)),{display:"inline-block",fontSize:0,[`&${n}-rtl`]:{direction:"rtl"},[`&${n}-block`]:{display:"flex"},[`${r}-badge ${r}-badge-count`]:{zIndex:1},[`> ${r}-badge:not(:first-child) > ${r}-button-wrapper`]:{borderInlineStart:"none"}})}},Zi=e=>{const{componentCls:t,wrapperMarginInlineEnd:r,colorPrimary:n,radioSize:o,motionDurationSlow:l,motionDurationMid:d,motionEaseInOutCirc:i,colorBgContainer:s,colorBorder:c,lineWidth:v,colorBgContainerDisabled:u,colorTextDisabled:m,paddingXS:f,dotColorDisabled:p,lineType:b,radioColor:g,radioBgColor:h,calc:x}=e,C=`${t}-inner`,w=x(o).sub(x(4).mul(2)),E=x(1).mul(o).equal({unit:!0});return{[`${t}-wrapper`]:Object.assign(Object.assign({},Ct(e)),{display:"inline-flex",alignItems:"baseline",marginInlineStart:0,marginInlineEnd:r,cursor:"pointer","&:last-child":{marginInlineEnd:0},[`&${t}-wrapper-rtl`]:{direction:"rtl"},"&-disabled":{cursor:"not-allowed",color:e.colorTextDisabled},"&::after":{display:"inline-block",width:0,overflow:"hidden",content:'"\\a0"'},"&-block":{flex:1,justifyContent:"center"},[`${t}-checked::after`]:{position:"absolute",insetBlockStart:0,insetInlineStart:0,width:"100%",height:"100%",border:`${L(v)} ${b} ${n}`,borderRadius:"50%",visibility:"hidden",opacity:0,content:'""'},[t]:Object.assign(Object.assign({},Ct(e)),{position:"relative",display:"inline-block",outline:"none",cursor:"pointer",alignSelf:"center",borderRadius:"50%"}),[`${t}-wrapper:hover &,
        &:hover ${C}`]:{borderColor:n},[`${t}-input:focus-visible + ${C}`]:Object.assign({},Yt(e)),[`${t}:hover::after, ${t}-wrapper:hover &::after`]:{visibility:"visible"},[`${t}-inner`]:{"&::after":{boxSizing:"border-box",position:"absolute",insetBlockStart:"50%",insetInlineStart:"50%",display:"block",width:E,height:E,marginBlockStart:x(1).mul(o).div(-2).equal({unit:!0}),marginInlineStart:x(1).mul(o).div(-2).equal({unit:!0}),backgroundColor:g,borderBlockStart:0,borderInlineStart:0,borderRadius:E,transform:"scale(0)",opacity:0,transition:`all ${l} ${i}`,content:'""'},boxSizing:"border-box",position:"relative",insetBlockStart:0,insetInlineStart:0,display:"block",width:E,height:E,backgroundColor:s,borderColor:c,borderStyle:"solid",borderWidth:v,borderRadius:"50%",transition:`all ${d}`},[`${t}-input`]:{position:"absolute",inset:0,zIndex:1,cursor:"pointer",opacity:0},[`${t}-checked`]:{[C]:{borderColor:n,backgroundColor:h,"&::after":{transform:`scale(${e.calc(e.dotSize).div(o).equal()})`,opacity:1,transition:`all ${l} ${i}`}}},[`${t}-disabled`]:{cursor:"not-allowed",[C]:{backgroundColor:u,borderColor:c,cursor:"not-allowed","&::after":{backgroundColor:p}},[`${t}-input`]:{cursor:"not-allowed"},[`${t}-disabled + span`]:{color:m,cursor:"not-allowed"},[`&${t}-checked`]:{[C]:{"&::after":{transform:`scale(${x(w).div(o).equal()})`}}}},[`span${t} + *`]:{paddingInlineStart:f,paddingInlineEnd:f}})}},es=e=>{const{buttonColor:t,controlHeight:r,componentCls:n,lineWidth:o,lineType:l,colorBorder:d,motionDurationSlow:i,motionDurationMid:s,buttonPaddingInline:c,fontSize:v,buttonBg:u,fontSizeLG:m,controlHeightLG:f,controlHeightSM:p,paddingXS:b,borderRadius:g,borderRadiusSM:h,borderRadiusLG:x,buttonCheckedBg:C,buttonSolidCheckedColor:S,colorTextDisabled:w,colorBgContainerDisabled:E,buttonCheckedBgDisabled:k,buttonCheckedColorDisabled:R,colorPrimary:y,colorPrimaryHover:P,colorPrimaryActive:I,buttonSolidCheckedBg:T,buttonSolidCheckedHoverBg:O,buttonSolidCheckedActiveBg:N,calc:$}=e;return{[`${n}-button-wrapper`]:{position:"relative",display:"inline-block",height:r,margin:0,paddingInline:c,paddingBlock:0,color:t,fontSize:v,lineHeight:L($(r).sub($(o).mul(2)).equal()),background:u,border:`${L(o)} ${l} ${d}`,borderBlockStartWidth:$(o).add(.02).equal(),borderInlineStartWidth:0,borderInlineEndWidth:o,cursor:"pointer",transition:[`color ${s}`,`background ${s}`,`box-shadow ${s}`].join(","),a:{color:t},[`> ${n}-button`]:{position:"absolute",insetBlockStart:0,insetInlineStart:0,zIndex:-1,width:"100%",height:"100%"},"&:not(:first-child)":{"&::before":{position:"absolute",insetBlockStart:$(o).mul(-1).equal(),insetInlineStart:$(o).mul(-1).equal(),display:"block",boxSizing:"content-box",width:1,height:"100%",paddingBlock:o,paddingInline:0,backgroundColor:d,transition:`background-color ${i}`,content:'""'}},"&:first-child":{borderInlineStart:`${L(o)} ${l} ${d}`,borderStartStartRadius:g,borderEndStartRadius:g},"&:last-child":{borderStartEndRadius:g,borderEndEndRadius:g},"&:first-child:last-child":{borderRadius:g},[`${n}-group-large &`]:{height:f,fontSize:m,lineHeight:L($(f).sub($(o).mul(2)).equal()),"&:first-child":{borderStartStartRadius:x,borderEndStartRadius:x},"&:last-child":{borderStartEndRadius:x,borderEndEndRadius:x}},[`${n}-group-small &`]:{height:p,paddingInline:$(b).sub(o).equal(),paddingBlock:0,lineHeight:L($(p).sub($(o).mul(2)).equal()),"&:first-child":{borderStartStartRadius:h,borderEndStartRadius:h},"&:last-child":{borderStartEndRadius:h,borderEndEndRadius:h}},"&:hover":{position:"relative",color:y},"&:has(:focus-visible)":Object.assign({},Yt(e)),[`${n}-inner, input[type='checkbox'], input[type='radio']`]:{width:0,height:0,opacity:0,pointerEvents:"none"},[`&-checked:not(${n}-button-wrapper-disabled)`]:{zIndex:1,color:y,background:C,borderColor:y,"&::before":{backgroundColor:y},"&:first-child":{borderColor:y},"&:hover":{color:P,borderColor:P,"&::before":{backgroundColor:P}},"&:active":{color:I,borderColor:I,"&::before":{backgroundColor:I}}},[`${n}-group-solid &-checked:not(${n}-button-wrapper-disabled)`]:{color:S,background:T,borderColor:T,"&:hover":{color:S,background:O,borderColor:O},"&:active":{color:S,background:N,borderColor:N}},"&-disabled":{color:w,backgroundColor:E,borderColor:d,cursor:"not-allowed","&:first-child, &:hover":{color:w,backgroundColor:E,borderColor:d}},[`&-disabled${n}-button-wrapper-checked`]:{color:R,backgroundColor:k,borderColor:d,boxShadow:"none"},"&-block":{flex:1,textAlign:"center"}}}},ts=e=>{const{wireframe:t,padding:r,marginXS:n,lineWidth:o,fontSizeLG:l,colorText:d,colorBgContainer:i,colorTextDisabled:s,controlItemBgActiveDisabled:c,colorTextLightSolid:v,colorPrimary:u,colorPrimaryHover:m,colorPrimaryActive:f,colorWhite:p}=e,b=4,g=l,h=t?g-b*2:g-(b+o)*2;return{radioSize:g,dotSize:h,dotColorDisabled:s,buttonSolidCheckedColor:v,buttonSolidCheckedBg:u,buttonSolidCheckedHoverBg:m,buttonSolidCheckedActiveBg:f,buttonBg:i,buttonCheckedBg:i,buttonColor:d,buttonCheckedBgDisabled:c,buttonCheckedColorDisabled:s,buttonPaddingInline:r-o,wrapperMarginInlineEnd:n,radioColor:t?u:p,radioBgColor:t?i:u}},ra=en("Radio",e=>{const{controlOutline:t,controlOutlineWidth:r}=e,n=`0 0 0 ${L(r)} ${t}`,l=tn(e,{radioFocusShadow:n,radioButtonFocusShadow:n});return[Qi(l),Zi(l),es(l)]},ts,{unitless:{radioSize:!0,dotSize:!0}});var ns=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const rs=(e,t)=>{var r,n;const o=a.useContext(Zo),l=a.useContext(ea),{getPrefixCls:d,direction:i,radio:s}=a.useContext(St),c=a.useRef(null),v=jo(t,c),{isFormItemInput:u}=a.useContext(_o),m=K=>{var D,j;(D=e.onChange)===null||D===void 0||D.call(e,K),(j=o==null?void 0:o.onChange)===null||j===void 0||j.call(o,K)},{prefixCls:f,className:p,rootClassName:b,children:g,style:h,title:x}=e,C=ns(e,["prefixCls","className","rootClassName","children","style","title"]),S=d("radio",f),w=((o==null?void 0:o.optionType)||l)==="button",E=w?`${S}-button`:S,k=Cn(S),[R,y,P]=ra(S,k),I=Object.assign({},C),T=a.useContext(Lo);o&&(I.name=o.name,I.onChange=m,I.checked=e.value===o.value,I.disabled=(r=I.disabled)!==null&&r!==void 0?r:o.disabled),I.disabled=(n=I.disabled)!==null&&n!==void 0?n:T;const O=G(`${E}-wrapper`,{[`${E}-wrapper-checked`]:I.checked,[`${E}-wrapper-disabled`]:I.disabled,[`${E}-wrapper-rtl`]:i==="rtl",[`${E}-wrapper-in-form-item`]:u,[`${E}-wrapper-block`]:!!(o!=null&&o.block)},s==null?void 0:s.className,p,b,y,P,k),[N,$]=na(I.onClick);return R(a.createElement(Ho,{component:"Radio",disabled:I.disabled},a.createElement("label",{className:O,style:Object.assign(Object.assign({},s==null?void 0:s.style),h),onMouseEnter:e.onMouseEnter,onMouseLeave:e.onMouseLeave,title:x,onClick:N},a.createElement(ta,Object.assign({},I,{className:G(I.className,{[Ao]:!w}),type:"radio",prefixCls:E,ref:v,onClick:$})),g!==void 0?a.createElement("span",{className:`${E}-label`},g):null)))},On=a.forwardRef(rs),os=a.forwardRef((e,t)=>{const{getPrefixCls:r,direction:n}=a.useContext(St),o=Za(),{prefixCls:l,className:d,rootClassName:i,options:s,buttonStyle:c="outline",disabled:v,children:u,size:m,style:f,id:p,optionType:b,name:g=o,defaultValue:h,value:x,block:C=!1,onChange:S,onMouseEnter:w,onMouseLeave:E,onFocus:k,onBlur:R}=e,[y,P]=hn(h,{value:x}),I=a.useCallback(U=>{const ee=y,se=U.target.value;"value"in e||P(se),se!==ee&&(S==null||S(U))},[y,P,S]),T=r("radio",l),O=`${T}-group`,N=Cn(T),[$,K,D]=ra(T,N);let j=u;s&&s.length>0&&(j=s.map(U=>typeof U=="string"||typeof U=="number"?a.createElement(On,{key:U.toString(),prefixCls:T,disabled:v,value:U,checked:y===U},U):a.createElement(On,{key:`radio-group-value-options-${U.value}`,prefixCls:T,disabled:U.disabled||v,value:U.value,checked:y===U.value,title:U.title,style:U.style,className:U.className,id:U.id,required:U.required},U.label)));const M=cr(m),q=G(O,`${O}-${c}`,{[`${O}-${M}`]:M,[`${O}-rtl`]:n==="rtl",[`${O}-block`]:C},d,i,K,D,N),X=a.useMemo(()=>({onChange:I,value:y,disabled:v,name:g,optionType:b,block:C}),[I,y,v,g,b,C]);return $(a.createElement("div",Object.assign({},kt(e,{aria:!0,data:!0}),{className:q,style:f,onMouseEnter:w,onMouseLeave:E,onFocus:k,onBlur:R,id:p,ref:t}),a.createElement(Ui,{value:X},j)))}),as=a.memo(os);var ls=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const is=(e,t)=>{const{getPrefixCls:r}=a.useContext(St),{prefixCls:n}=e,o=ls(e,["prefixCls"]),l=r("radio",n);return a.createElement(Ji,{value:"button"},a.createElement(On,Object.assign({prefixCls:l},o,{type:"radio",ref:t})))},ss=a.forwardRef(is),xn=On;xn.Button=ss;xn.Group=as;xn.__ANT_RADIO=!0;function ut(e,t){return e[t]}var cs=["children"];function oa(e,t){return"".concat(e,"-").concat(t)}function ds(e){return e&&e.type&&e.type.isTreeNode}function Sn(e,t){return e??t}function Qt(e){var t=e||{},r=t.title,n=t._title,o=t.key,l=t.children,d=r||"title";return{title:d,_title:n||[d],key:o||"key",children:l||"children"}}function aa(e){function t(r){var n=Fo(r);return n.map(function(o){if(!ds(o))return Nt(!o,"Tree/TreeNode can only accept TreeNode as children."),null;var l=o.key,d=o.props,i=d.children,s=ft(d,cs),c=A({key:l},s),v=t(i);return v.length&&(c.children=v),c}).filter(function(o){return o})}return t(e)}function An(e,t,r){var n=Qt(r),o=n._title,l=n.key,d=n.children,i=new Set(t===!0?[]:t),s=[];function c(v){var u=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;return v.map(function(m,f){for(var p=oa(u?u.pos:"0",f),b=Sn(m[l],p),g,h=0;h<o.length;h+=1){var x=o[h];if(m[x]!==void 0){g=m[x];break}}var C=Object.assign(dr(m,[].concat(we(o),[l,d])),{title:g,key:b,parent:u,pos:p,children:null,data:m,isStart:[].concat(we(u?u.isStart:[]),[f===0]),isEnd:[].concat(we(u?u.isEnd:[]),[f===v.length-1])});return s.push(C),t===!0||i.has(b)?C.children=c(m[d]||[],C):C.children=[],C})}return c(e),s}function us(e,t,r){var n={};pt(r)==="object"?n=r:n={externalGetKey:r},n=n||{};var o=n,l=o.childrenPropName,d=o.externalGetKey,i=o.fieldNames,s=Qt(i),c=s.key,v=s.children,u=l||v,m;d?typeof d=="string"?m=function(b){return b[d]}:typeof d=="function"&&(m=function(b){return d(b)}):m=function(b,g){return Sn(b[c],g)};function f(p,b,g,h){var x=p?p[u]:e,C=p?oa(g.pos,b):"0",S=p?[].concat(we(h),[p]):[];if(p){var w=m(p,C),E={node:p,index:b,pos:C,key:w,parentPos:g.node?g.pos:null,level:g.level+1,nodes:S};t(E)}x&&x.forEach(function(k,R){f(k,R,{node:p,pos:C,level:g?g.level+1:-1},S)})}f(null)}function yr(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=t.initWrapper,n=t.processEntity,o=t.onProcessFinished,l=t.externalGetKey,d=t.childrenPropName,i=t.fieldNames,s=arguments.length>2?arguments[2]:void 0,c=l||s,v={},u={},m={posEntities:v,keyEntities:u};return r&&(m=r(m)||m),us(e,function(f){var p=f.node,b=f.index,g=f.pos,h=f.key,x=f.parentPos,C=f.level,S=f.nodes,w={node:p,nodes:S,index:b,key:h,pos:g,level:C},E=Sn(h,g);v[g]=w,u[E]=w,w.parent=v[x],w.parent&&(w.parent.children=w.parent.children||[],w.parent.children.push(w)),n&&n(w,m)},{externalGetKey:c,childrenPropName:d,fieldNames:i}),o&&o(m),m}function pn(e,t){var r=t.expandedKeys,n=t.selectedKeys,o=t.loadedKeys,l=t.loadingKeys,d=t.checkedKeys,i=t.halfCheckedKeys,s=t.dragOverNodeKey,c=t.dropPosition,v=t.keyEntities,u=ut(v,e),m={eventKey:e,expanded:r.indexOf(e)!==-1,selected:n.indexOf(e)!==-1,loaded:o.indexOf(e)!==-1,loading:l.indexOf(e)!==-1,checked:d.indexOf(e)!==-1,halfChecked:i.indexOf(e)!==-1,pos:String(u?u.pos:""),dragOver:s===e&&c===0,dragOverGapTop:s===e&&c===-1,dragOverGapBottom:s===e&&c===1};return m}function Ve(e){var t=e.data,r=e.expanded,n=e.selected,o=e.checked,l=e.loaded,d=e.loading,i=e.halfChecked,s=e.dragOver,c=e.dragOverGapTop,v=e.dragOverGapBottom,u=e.pos,m=e.active,f=e.eventKey,p=A(A({},t),{},{expanded:r,selected:n,checked:o,loaded:l,loading:d,halfChecked:i,dragOver:s,dragOverGapTop:c,dragOverGapBottom:v,pos:u,active:m,key:f});return"props"in p||Object.defineProperty(p,"props",{get:function(){return Nt(!1,"Second param return from event is node data instead of TreeNode instance. Please read value directly instead of reading from `props`."),e}}),p}function la(e,t){var r=new Set;return e.forEach(function(n){t.has(n)||r.add(n)}),r}function fs(e){var t=e||{},r=t.disabled,n=t.disableCheckbox,o=t.checkable;return!!(r||n)||o===!1}function vs(e,t,r,n){for(var o=new Set(e),l=new Set,d=0;d<=r;d+=1){var i=t.get(d)||new Set;i.forEach(function(u){var m=u.key,f=u.node,p=u.children,b=p===void 0?[]:p;o.has(m)&&!n(f)&&b.filter(function(g){return!n(g.node)}).forEach(function(g){o.add(g.key)})})}for(var s=new Set,c=r;c>=0;c-=1){var v=t.get(c)||new Set;v.forEach(function(u){var m=u.parent,f=u.node;if(!(n(f)||!u.parent||s.has(u.parent.key))){if(n(u.parent.node)){s.add(m.key);return}var p=!0,b=!1;(m.children||[]).filter(function(g){return!n(g.node)}).forEach(function(g){var h=g.key,x=o.has(h);p&&!x&&(p=!1),!b&&(x||l.has(h))&&(b=!0)}),p&&o.add(m.key),b&&l.add(m.key),s.add(m.key)}})}return{checkedKeys:Array.from(o),halfCheckedKeys:Array.from(la(l,o))}}function ms(e,t,r,n,o){for(var l=new Set(e),d=new Set(t),i=0;i<=n;i+=1){var s=r.get(i)||new Set;s.forEach(function(m){var f=m.key,p=m.node,b=m.children,g=b===void 0?[]:b;!l.has(f)&&!d.has(f)&&!o(p)&&g.filter(function(h){return!o(h.node)}).forEach(function(h){l.delete(h.key)})})}d=new Set;for(var c=new Set,v=n;v>=0;v-=1){var u=r.get(v)||new Set;u.forEach(function(m){var f=m.parent,p=m.node;if(!(o(p)||!m.parent||c.has(m.parent.key))){if(o(m.parent.node)){c.add(f.key);return}var b=!0,g=!1;(f.children||[]).filter(function(h){return!o(h.node)}).forEach(function(h){var x=h.key,C=l.has(x);b&&!C&&(b=!1),!g&&(C||d.has(x))&&(g=!0)}),b||l.delete(f.key),g&&d.add(f.key),c.add(f.key)}})}return{checkedKeys:Array.from(l),halfCheckedKeys:Array.from(la(d,l))}}function Ut(e,t,r,n){var o=[],l;n?l=n:l=fs;var d=new Set(e.filter(function(v){var u=!!ut(r,v);return u||o.push(v),u})),i=new Map,s=0;Object.keys(r).forEach(function(v){var u=r[v],m=u.level,f=i.get(m);f||(f=new Set,i.set(m,f)),f.add(u),s=Math.max(s,m)}),Nt(!o.length,"Tree missing follow keys: ".concat(o.slice(0,100).map(function(v){return"'".concat(v,"'")}).join(", ")));var c;return t===!0?c=vs(d,i,s,l):c=ms(d,t.halfCheckedKeys,i,s,l),c}const ps=e=>{const{checkboxCls:t}=e,r=`${t}-wrapper`;return[{[`${t}-group`]:Object.assign(Object.assign({},Ct(e)),{display:"inline-flex",flexWrap:"wrap",columnGap:e.marginXS,[`> ${e.antCls}-row`]:{flex:1}}),[r]:Object.assign(Object.assign({},Ct(e)),{display:"inline-flex",alignItems:"baseline",cursor:"pointer","&:after":{display:"inline-block",width:0,overflow:"hidden",content:"'\\a0'"},[`& + ${r}`]:{marginInlineStart:0},[`&${r}-in-form-item`]:{'input[type="checkbox"]':{width:14,height:14}}}),[t]:Object.assign(Object.assign({},Ct(e)),{position:"relative",whiteSpace:"nowrap",lineHeight:1,cursor:"pointer",borderRadius:e.borderRadiusSM,alignSelf:"center",[`${t}-input`]:{position:"absolute",inset:0,zIndex:1,cursor:"pointer",opacity:0,margin:0,[`&:focus-visible + ${t}-inner`]:Object.assign({},Yt(e))},[`${t}-inner`]:{boxSizing:"border-box",display:"block",width:e.checkboxSize,height:e.checkboxSize,direction:"ltr",backgroundColor:e.colorBgContainer,border:`${L(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,borderCollapse:"separate",transition:`all ${e.motionDurationSlow}`,"&:after":{boxSizing:"border-box",position:"absolute",top:"50%",insetInlineStart:"25%",display:"table",width:e.calc(e.checkboxSize).div(14).mul(5).equal(),height:e.calc(e.checkboxSize).div(14).mul(8).equal(),border:`${L(e.lineWidthBold)} solid ${e.colorWhite}`,borderTop:0,borderInlineStart:0,transform:"rotate(45deg) scale(0) translate(-50%,-50%)",opacity:0,content:'""',transition:`all ${e.motionDurationFast} ${e.motionEaseInBack}, opacity ${e.motionDurationFast}`}},"& + span":{paddingInlineStart:e.paddingXS,paddingInlineEnd:e.paddingXS}})},{[`
        ${r}:not(${r}-disabled),
        ${t}:not(${t}-disabled)
      `]:{[`&:hover ${t}-inner`]:{borderColor:e.colorPrimary}},[`${r}:not(${r}-disabled)`]:{[`&:hover ${t}-checked:not(${t}-disabled) ${t}-inner`]:{backgroundColor:e.colorPrimaryHover,borderColor:"transparent"},[`&:hover ${t}-checked:not(${t}-disabled):after`]:{borderColor:e.colorPrimaryHover}}},{[`${t}-checked`]:{[`${t}-inner`]:{backgroundColor:e.colorPrimary,borderColor:e.colorPrimary,"&:after":{opacity:1,transform:"rotate(45deg) scale(1) translate(-50%,-50%)",transition:`all ${e.motionDurationMid} ${e.motionEaseOutBack} ${e.motionDurationFast}`}}},[`
        ${r}-checked:not(${r}-disabled),
        ${t}-checked:not(${t}-disabled)
      `]:{[`&:hover ${t}-inner`]:{backgroundColor:e.colorPrimaryHover,borderColor:"transparent"}}},{[t]:{"&-indeterminate":{"&":{[`${t}-inner`]:{backgroundColor:`${e.colorBgContainer}`,borderColor:`${e.colorBorder}`,"&:after":{top:"50%",insetInlineStart:"50%",width:e.calc(e.fontSizeLG).div(2).equal(),height:e.calc(e.fontSizeLG).div(2).equal(),backgroundColor:e.colorPrimary,border:0,transform:"translate(-50%, -50%) scale(1)",opacity:1,content:'""'}},[`&:hover ${t}-inner`]:{backgroundColor:`${e.colorBgContainer}`,borderColor:`${e.colorPrimary}`}}}}},{[`${r}-disabled`]:{cursor:"not-allowed"},[`${t}-disabled`]:{[`&, ${t}-input`]:{cursor:"not-allowed",pointerEvents:"none"},[`${t}-inner`]:{background:e.colorBgContainerDisabled,borderColor:e.colorBorder,"&:after":{borderColor:e.colorTextDisabled}},"&:after":{display:"none"},"& + span":{color:e.colorTextDisabled},[`&${t}-indeterminate ${t}-inner::after`]:{background:e.colorTextDisabled}}}]};function ia(e,t){const r=tn(t,{checkboxCls:`.${e}`,checkboxSize:t.controlInteractiveSize});return[ps(r)]}const sa=en("Checkbox",(e,{prefixCls:t})=>[ia(t,e)]),ca=J.createContext(null);var gs=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const hs=(e,t)=>{var r;const{prefixCls:n,className:o,rootClassName:l,children:d,indeterminate:i=!1,style:s,onMouseEnter:c,onMouseLeave:v,skipGroup:u=!1,disabled:m}=e,f=gs(e,["prefixCls","className","rootClassName","children","indeterminate","style","onMouseEnter","onMouseLeave","skipGroup","disabled"]),{getPrefixCls:p,direction:b,checkbox:g}=a.useContext(St),h=a.useContext(ca),{isFormItemInput:x}=a.useContext(_o),C=a.useContext(Lo),S=(r=(h==null?void 0:h.disabled)||m)!==null&&r!==void 0?r:C,w=a.useRef(f.value),E=a.useRef(null),k=jo(t,E);a.useEffect(()=>{h==null||h.registerValue(f.value)},[]),a.useEffect(()=>{if(!u)return f.value!==w.current&&(h==null||h.cancelValue(w.current),h==null||h.registerValue(f.value),w.current=f.value),()=>h==null?void 0:h.cancelValue(f.value)},[f.value]),a.useEffect(()=>{var j;!((j=E.current)===null||j===void 0)&&j.input&&(E.current.input.indeterminate=i)},[i]);const R=p("checkbox",n),y=Cn(R),[P,I,T]=sa(R,y),O=Object.assign({},f);h&&!u&&(O.onChange=(...j)=>{f.onChange&&f.onChange.apply(f,j),h.toggleOption&&h.toggleOption({label:d,value:f.value})},O.name=h.name,O.checked=h.value.includes(f.value));const N=G(`${R}-wrapper`,{[`${R}-rtl`]:b==="rtl",[`${R}-wrapper-checked`]:O.checked,[`${R}-wrapper-disabled`]:S,[`${R}-wrapper-in-form-item`]:x},g==null?void 0:g.className,o,l,T,y,I),$=G({[`${R}-indeterminate`]:i},Ao,I),[K,D]=na(O.onClick);return P(a.createElement(Ho,{component:"Checkbox",disabled:S},a.createElement("label",{className:N,style:Object.assign(Object.assign({},g==null?void 0:g.style),s),onMouseEnter:c,onMouseLeave:v,onClick:K},a.createElement(ta,Object.assign({},O,{onClick:D,prefixCls:R,className:$,disabled:S,ref:k})),d!=null&&a.createElement("span",{className:`${R}-label`},d))))},da=a.forwardRef(hs);var bs=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const ys=a.forwardRef((e,t)=>{const{defaultValue:r,children:n,options:o=[],prefixCls:l,className:d,rootClassName:i,style:s,onChange:c}=e,v=bs(e,["defaultValue","children","options","prefixCls","className","rootClassName","style","onChange"]),{getPrefixCls:u,direction:m}=a.useContext(St),[f,p]=a.useState(v.value||r||[]),[b,g]=a.useState([]);a.useEffect(()=>{"value"in v&&p(v.value||[])},[v.value]);const h=a.useMemo(()=>o.map($=>typeof $=="string"||typeof $=="number"?{label:$,value:$}:$),[o]),x=$=>{g(K=>K.filter(D=>D!==$))},C=$=>{g(K=>[].concat(we(K),[$]))},S=$=>{const K=f.indexOf($.value),D=we(f);K===-1?D.push($.value):D.splice(K,1),"value"in v||p(D),c==null||c(D.filter(j=>b.includes(j)).sort((j,M)=>{const q=h.findIndex(U=>U.value===j),X=h.findIndex(U=>U.value===M);return q-X}))},w=u("checkbox",l),E=`${w}-group`,k=Cn(w),[R,y,P]=sa(w,k),I=dr(v,["value","disabled"]),T=o.length?h.map($=>a.createElement(da,{prefixCls:w,key:$.value.toString(),disabled:"disabled"in $?$.disabled:v.disabled,value:$.value,checked:f.includes($.value),onChange:$.onChange,className:G(`${E}-item`,$.className),style:$.style,title:$.title,id:$.id,required:$.required},$.label)):n,O=a.useMemo(()=>({toggleOption:S,value:f,disabled:v.disabled,name:v.name,registerValue:C,cancelValue:x}),[S,f,v.disabled,v.name,C,x]),N=G(E,{[`${E}-rtl`]:m==="rtl"},d,i,P,k,y);return R(a.createElement("div",Object.assign({className:N,style:s},I,{ref:t}),a.createElement(ca.Provider,{value:O},T)))}),Zt=da;Zt.Group=ys;Zt.__ANT_CHECKBOX=!0;var to=a.createContext(null),ua=a.createContext({}),Cs=["prefixCls","className","containerRef"],xs=function(t){var r=t.prefixCls,n=t.className,o=t.containerRef,l=ft(t,Cs),d=a.useContext(ua),i=d.panel,s=el(i,o);return a.createElement("div",le({className:G("".concat(r,"-content"),n),role:"dialog",ref:s},kt(t,{aria:!0}),{"aria-modal":"true"},l))};function no(e){return typeof e=="string"&&String(Number(e))===e?(Nt(!1,"Invalid value type of `width` or `height` which should be number type instead."),Number(e)):e}var ro={width:0,height:0,overflow:"hidden",outline:"none",position:"absolute"};function Ss(e,t){var r,n,o,l=e.prefixCls,d=e.open,i=e.placement,s=e.inline,c=e.push,v=e.forceRender,u=e.autoFocus,m=e.keyboard,f=e.classNames,p=e.rootClassName,b=e.rootStyle,g=e.zIndex,h=e.className,x=e.id,C=e.style,S=e.motion,w=e.width,E=e.height,k=e.children,R=e.mask,y=e.maskClosable,P=e.maskMotion,I=e.maskClassName,T=e.maskStyle,O=e.afterOpenChange,N=e.onClose,$=e.onMouseEnter,K=e.onMouseOver,D=e.onMouseLeave,j=e.onClick,M=e.onKeyDown,q=e.onKeyUp,X=e.styles,U=e.drawerRender,ee=a.useRef(),se=a.useRef(),ge=a.useRef();a.useImperativeHandle(t,function(){return ee.current});var ce=function(H){var de=H.keyCode,ve=H.shiftKey;switch(de){case qe.TAB:{if(de===qe.TAB){if(!ve&&document.activeElement===ge.current){var fe;(fe=se.current)===null||fe===void 0||fe.focus({preventScroll:!0})}else if(ve&&document.activeElement===se.current){var Pe;(Pe=ge.current)===null||Pe===void 0||Pe.focus({preventScroll:!0})}}break}case qe.ESC:{N&&m&&(H.stopPropagation(),N(H));break}}};a.useEffect(function(){if(d&&u){var B;(B=ee.current)===null||B===void 0||B.focus({preventScroll:!0})}},[d]);var Z=a.useState(!1),te=Ce(Z,2),pe=te[0],ie=te[1],W=a.useContext(to),V;typeof c=="boolean"?V=c?{}:{distance:0}:V=c||{};var F=(r=(n=(o=V)===null||o===void 0?void 0:o.distance)!==null&&n!==void 0?n:W==null?void 0:W.pushDistance)!==null&&r!==void 0?r:180,Q=a.useMemo(function(){return{pushDistance:F,push:function(){ie(!0)},pull:function(){ie(!1)}}},[F]);a.useEffect(function(){if(d){var B;W==null||(B=W.push)===null||B===void 0||B.call(W)}else{var H;W==null||(H=W.pull)===null||H===void 0||H.call(W)}},[d]),a.useEffect(function(){return function(){var B;W==null||(B=W.pull)===null||B===void 0||B.call(W)}},[]);var oe=a.createElement(Un,le({key:"mask"},P,{visible:R&&d}),function(B,H){var de=B.className,ve=B.style;return a.createElement("div",{className:G("".concat(l,"-mask"),de,f==null?void 0:f.mask,I),style:A(A(A({},ve),T),X==null?void 0:X.mask),onClick:y&&d?N:void 0,ref:H})}),Y=typeof S=="function"?S(i):S,ae={};if(pe&&F)switch(i){case"top":ae.transform="translateY(".concat(F,"px)");break;case"bottom":ae.transform="translateY(".concat(-F,"px)");break;case"left":ae.transform="translateX(".concat(F,"px)");break;default:ae.transform="translateX(".concat(-F,"px)");break}i==="left"||i==="right"?ae.width=no(w):ae.height=no(E);var Oe={onMouseEnter:$,onMouseOver:K,onMouseLeave:D,onClick:j,onKeyDown:M,onKeyUp:q},Ae=a.createElement(Un,le({key:"panel"},Y,{visible:d,forceRender:v,onVisibleChanged:function(H){O==null||O(H)},removeOnLeave:!1,leavedClassName:"".concat(l,"-content-wrapper-hidden")}),function(B,H){var de=B.className,ve=B.style,fe=a.createElement(xs,le({id:x,containerRef:H,prefixCls:l,className:G(h,f==null?void 0:f.content),style:A(A({},C),X==null?void 0:X.content)},kt(e,{aria:!0}),Oe),k);return a.createElement("div",le({className:G("".concat(l,"-content-wrapper"),f==null?void 0:f.wrapper,de),style:A(A(A({},ae),ve),X==null?void 0:X.wrapper)},kt(e,{data:!0})),U?U(fe):fe)}),Ee=A({},b);return g&&(Ee.zIndex=g),a.createElement(to.Provider,{value:Q},a.createElement("div",{className:G(l,"".concat(l,"-").concat(i),p,z(z({},"".concat(l,"-open"),d),"".concat(l,"-inline"),s)),style:Ee,tabIndex:-1,ref:ee,onKeyDown:ce},oe,a.createElement("div",{tabIndex:0,ref:se,style:ro,"aria-hidden":"true","data-sentinel":"start"}),Ae,a.createElement("div",{tabIndex:0,ref:ge,style:ro,"aria-hidden":"true","data-sentinel":"end"})))}var ws=a.forwardRef(Ss),$s=function(t){var r=t.open,n=r===void 0?!1:r,o=t.prefixCls,l=o===void 0?"rc-drawer":o,d=t.placement,i=d===void 0?"right":d,s=t.autoFocus,c=s===void 0?!0:s,v=t.keyboard,u=v===void 0?!0:v,m=t.width,f=m===void 0?378:m,p=t.mask,b=p===void 0?!0:p,g=t.maskClosable,h=g===void 0?!0:g,x=t.getContainer,C=t.forceRender,S=t.afterOpenChange,w=t.destroyOnClose,E=t.onMouseEnter,k=t.onMouseOver,R=t.onMouseLeave,y=t.onClick,P=t.onKeyDown,I=t.onKeyUp,T=t.panelRef,O=a.useState(!1),N=Ce(O,2),$=N[0],K=N[1],D=a.useState(!1),j=Ce(D,2),M=j[0],q=j[1];xt(function(){q(!0)},[]);var X=M?n:!1,U=a.useRef(),ee=a.useRef();xt(function(){X&&(ee.current=document.activeElement)},[X]);var se=function(pe){var ie;if(K(pe),S==null||S(pe),!pe&&ee.current&&!((ie=U.current)!==null&&ie!==void 0&&ie.contains(ee.current))){var W;(W=ee.current)===null||W===void 0||W.focus({preventScroll:!0})}},ge=a.useMemo(function(){return{panel:T}},[T]);if(!C&&!$&&!X&&w)return null;var ce={onMouseEnter:E,onMouseOver:k,onMouseLeave:R,onClick:y,onKeyDown:P,onKeyUp:I},Z=A(A({},t),{},{open:X,prefixCls:l,placement:i,autoFocus:c,keyboard:u,width:f,mask:b,maskClosable:h,inline:x===!1,afterOpenChange:se,ref:U},ce);return a.createElement(ua.Provider,{value:ge},a.createElement(tl,{open:X||C||$,autoDestroy:!1,getContainer:x,autoLock:b&&(X||$)},a.createElement(ws,Z)))};const fa=e=>{var t,r;const{prefixCls:n,title:o,footer:l,extra:d,loading:i,onClose:s,headerStyle:c,bodyStyle:v,footerStyle:u,children:m,classNames:f,styles:p}=e,b=ur("drawer"),g=a.useCallback(w=>a.createElement("button",{type:"button",onClick:s,className:`${n}-close`},w),[s]),[h,x]=nl(_r(e),_r(b),{closable:!0,closeIconRender:g}),C=a.useMemo(()=>{var w,E;return!o&&!h?null:a.createElement("div",{style:Object.assign(Object.assign(Object.assign({},(w=b.styles)===null||w===void 0?void 0:w.header),c),p==null?void 0:p.header),className:G(`${n}-header`,{[`${n}-header-close-only`]:h&&!o&&!d},(E=b.classNames)===null||E===void 0?void 0:E.header,f==null?void 0:f.header)},a.createElement("div",{className:`${n}-header-title`},x,o&&a.createElement("div",{className:`${n}-title`},o)),d&&a.createElement("div",{className:`${n}-extra`},d))},[h,x,d,c,n,o]),S=a.useMemo(()=>{var w,E;if(!l)return null;const k=`${n}-footer`;return a.createElement("div",{className:G(k,(w=b.classNames)===null||w===void 0?void 0:w.footer,f==null?void 0:f.footer),style:Object.assign(Object.assign(Object.assign({},(E=b.styles)===null||E===void 0?void 0:E.footer),u),p==null?void 0:p.footer)},l)},[l,u,n]);return a.createElement(a.Fragment,null,C,a.createElement("div",{className:G(`${n}-body`,f==null?void 0:f.body,(t=b.classNames)===null||t===void 0?void 0:t.body),style:Object.assign(Object.assign(Object.assign({},(r=b.styles)===null||r===void 0?void 0:r.body),v),p==null?void 0:p.body)},i?a.createElement(rl,{active:!0,title:!1,paragraph:{rows:5},className:`${n}-body-skeleton`}):m),S)},Es=e=>{const t="100%";return{left:`translateX(-${t})`,right:`translateX(${t})`,top:`translateY(-${t})`,bottom:`translateY(${t})`}[e]},va=(e,t)=>({"&-enter, &-appear":Object.assign(Object.assign({},e),{"&-active":t}),"&-leave":Object.assign(Object.assign({},t),{"&-active":e})}),ma=(e,t)=>Object.assign({"&-enter, &-appear, &-leave":{"&-start":{transition:"none"},"&-active":{transition:`all ${t}`}}},va({opacity:e},{opacity:1})),Ns=(e,t)=>[ma(.7,t),va({transform:Es(e)},{transform:"none"})],ks=e=>{const{componentCls:t,motionDurationSlow:r}=e;return{[t]:{[`${t}-mask-motion`]:ma(0,r),[`${t}-panel-motion`]:["left","right","top","bottom"].reduce((n,o)=>Object.assign(Object.assign({},n),{[`&-${o}`]:Ns(o,r)}),{})}}},Rs=e=>{const{borderRadiusSM:t,componentCls:r,zIndexPopup:n,colorBgMask:o,colorBgElevated:l,motionDurationSlow:d,motionDurationMid:i,paddingXS:s,padding:c,paddingLG:v,fontSizeLG:u,lineHeightLG:m,lineWidth:f,lineType:p,colorSplit:b,marginXS:g,colorIcon:h,colorIconHover:x,colorBgTextHover:C,colorBgTextActive:S,colorText:w,fontWeightStrong:E,footerPaddingBlock:k,footerPaddingInline:R,calc:y}=e,P=`${r}-content-wrapper`;return{[r]:{position:"fixed",inset:0,zIndex:n,pointerEvents:"none",color:w,"&-pure":{position:"relative",background:l,display:"flex",flexDirection:"column",[`&${r}-left`]:{boxShadow:e.boxShadowDrawerLeft},[`&${r}-right`]:{boxShadow:e.boxShadowDrawerRight},[`&${r}-top`]:{boxShadow:e.boxShadowDrawerUp},[`&${r}-bottom`]:{boxShadow:e.boxShadowDrawerDown}},"&-inline":{position:"absolute"},[`${r}-mask`]:{position:"absolute",inset:0,zIndex:n,background:o,pointerEvents:"auto"},[P]:{position:"absolute",zIndex:n,maxWidth:"100vw",transition:`all ${d}`,"&-hidden":{display:"none"}},[`&-left > ${P}`]:{top:0,bottom:0,left:{_skip_check_:!0,value:0},boxShadow:e.boxShadowDrawerLeft},[`&-right > ${P}`]:{top:0,right:{_skip_check_:!0,value:0},bottom:0,boxShadow:e.boxShadowDrawerRight},[`&-top > ${P}`]:{top:0,insetInline:0,boxShadow:e.boxShadowDrawerUp},[`&-bottom > ${P}`]:{bottom:0,insetInline:0,boxShadow:e.boxShadowDrawerDown},[`${r}-content`]:{display:"flex",flexDirection:"column",width:"100%",height:"100%",overflow:"auto",background:l,pointerEvents:"auto"},[`${r}-header`]:{display:"flex",flex:0,alignItems:"center",padding:`${L(c)} ${L(v)}`,fontSize:u,lineHeight:m,borderBottom:`${L(f)} ${p} ${b}`,"&-title":{display:"flex",flex:1,alignItems:"center",minWidth:0,minHeight:0}},[`${r}-extra`]:{flex:"none"},[`${r}-close`]:Object.assign({display:"inline-flex",width:y(u).add(s).equal(),height:y(u).add(s).equal(),borderRadius:t,justifyContent:"center",alignItems:"center",marginInlineEnd:g,color:h,fontWeight:E,fontSize:u,fontStyle:"normal",lineHeight:1,textAlign:"center",textTransform:"none",textDecoration:"none",background:"transparent",border:0,cursor:"pointer",transition:`all ${i}`,textRendering:"auto","&:hover":{color:x,backgroundColor:C,textDecoration:"none"},"&:active":{backgroundColor:S}},Wo(e)),[`${r}-title`]:{flex:1,margin:0,fontWeight:e.fontWeightStrong,fontSize:u,lineHeight:m},[`${r}-body`]:{flex:1,minWidth:0,minHeight:0,padding:v,overflow:"auto",[`${r}-body-skeleton`]:{width:"100%",height:"100%",display:"flex",justifyContent:"center"}},[`${r}-footer`]:{flexShrink:0,padding:`${L(k)} ${L(R)}`,borderTop:`${L(f)} ${p} ${b}`},"&-rtl":{direction:"rtl"}}}},Os=e=>({zIndexPopup:e.zIndexPopupBase,footerPaddingBlock:e.paddingXS,footerPaddingInline:e.padding}),pa=en("Drawer",e=>{const t=tn(e,{});return[Rs(t),ks(t)]},Os);var ga=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const Is={distance:180},ha=e=>{const{rootClassName:t,width:r,height:n,size:o="default",mask:l=!0,push:d=Is,open:i,afterOpenChange:s,onClose:c,prefixCls:v,getContainer:u,style:m,className:f,visible:p,afterVisibleChange:b,maskStyle:g,drawerStyle:h,contentWrapperStyle:x,destroyOnClose:C,destroyOnHidden:S}=e,w=ga(e,["rootClassName","width","height","size","mask","push","open","afterOpenChange","onClose","prefixCls","getContainer","style","className","visible","afterVisibleChange","maskStyle","drawerStyle","contentWrapperStyle","destroyOnClose","destroyOnHidden"]),{getPopupContainer:E,getPrefixCls:k,direction:R,className:y,style:P,classNames:I,styles:T}=ur("drawer"),O=k("drawer",v),[N,$,K]=pa(O),D=u===void 0&&E?()=>E(document.body):u,j=G({"no-mask":!l,[`${O}-rtl`]:R==="rtl"},t,$,K),M=a.useMemo(()=>r??(o==="large"?736:378),[r,o]),q=a.useMemo(()=>n??(o==="large"?736:378),[n,o]),X={motionName:Lr(O,"mask-motion"),motionAppear:!0,motionEnter:!0,motionLeave:!0,motionDeadline:500},U=te=>({motionName:Lr(O,`panel-motion-${te}`),motionAppear:!0,motionEnter:!0,motionLeave:!0,motionDeadline:500}),ee=ol(),[se,ge]=al("Drawer",w.zIndex),{classNames:ce={},styles:Z={}}=w;return N(a.createElement(ll,{form:!0,space:!0},a.createElement(il.Provider,{value:ge},a.createElement($s,Object.assign({prefixCls:O,onClose:c,maskMotion:X,motion:U},w,{classNames:{mask:G(ce.mask,I.mask),content:G(ce.content,I.content),wrapper:G(ce.wrapper,I.wrapper)},styles:{mask:Object.assign(Object.assign(Object.assign({},Z.mask),g),T.mask),content:Object.assign(Object.assign(Object.assign({},Z.content),h),T.content),wrapper:Object.assign(Object.assign(Object.assign({},Z.wrapper),x),T.wrapper)},open:i??p,mask:l,push:d,width:M,height:q,style:Object.assign(Object.assign({},P),m),className:G(y,f),rootClassName:j,getContainer:D,afterOpenChange:s??b,panelRef:ee,zIndex:se,destroyOnClose:S??C}),a.createElement(fa,Object.assign({prefixCls:O},w,{onClose:c}))))))},Ps=e=>{const{prefixCls:t,style:r,className:n,placement:o="right"}=e,l=ga(e,["prefixCls","style","className","placement"]),{getPrefixCls:d}=a.useContext(St),i=d("drawer",t),[s,c,v]=pa(i),u=G(i,`${i}-pure`,`${i}-${o}`,c,v,n);return s(a.createElement("div",{className:u,style:r},a.createElement(fa,Object.assign({prefixCls:i},l))))};ha._InternalPanelDoNotUseOrYouWillBeFired=Ps;var Ts={items_per_page:"条/页",jump_to:"跳至",jump_to_confirm:"确定",page:"页",prev_page:"上一页",next_page:"下一页",prev_5:"向前 5 页",next_5:"向后 5 页",prev_3:"向前 3 页",next_3:"向后 3 页",page_size:"页码"},Ks=[10,20,50,100],Ds=function(t){var r=t.pageSizeOptions,n=r===void 0?Ks:r,o=t.locale,l=t.changeSize,d=t.pageSize,i=t.goButton,s=t.quickGo,c=t.rootPrefixCls,v=t.disabled,u=t.buildOptionText,m=t.showSizeChanger,f=t.sizeChangerRender,p=J.useState(""),b=Ce(p,2),g=b[0],h=b[1],x=function(){return!g||Number.isNaN(g)?void 0:Number(g)},C=typeof u=="function"?u:function(T){return"".concat(T," ").concat(o.items_per_page)},S=function(O){h(O.target.value)},w=function(O){i||g===""||(h(""),!(O.relatedTarget&&(O.relatedTarget.className.indexOf("".concat(c,"-item-link"))>=0||O.relatedTarget.className.indexOf("".concat(c,"-item"))>=0))&&(s==null||s(x())))},E=function(O){g!==""&&(O.keyCode===qe.ENTER||O.type==="click")&&(h(""),s==null||s(x()))},k=function(){return n.some(function(O){return O.toString()===d.toString()})?n:n.concat([d]).sort(function(O,N){var $=Number.isNaN(Number(O))?0:Number(O),K=Number.isNaN(Number(N))?0:Number(N);return $-K})},R="".concat(c,"-options");if(!m&&!s)return null;var y=null,P=null,I=null;return m&&f&&(y=f({disabled:v,size:d,onSizeChange:function(O){l==null||l(Number(O))},"aria-label":o.page_size,className:"".concat(R,"-size-changer"),options:k().map(function(T){return{label:C(T),value:T}})})),s&&(i&&(I=typeof i=="boolean"?J.createElement("button",{type:"button",onClick:E,onKeyUp:E,disabled:v,className:"".concat(R,"-quick-jumper-button")},o.jump_to_confirm):J.createElement("span",{onClick:E,onKeyUp:E},i)),P=J.createElement("div",{className:"".concat(R,"-quick-jumper")},o.jump_to,J.createElement("input",{disabled:v,type:"text",value:g,onChange:S,onKeyUp:E,onBlur:w,"aria-label":o.page}),o.page,I)),J.createElement("li",{className:R},y,P)},mn=function(t){var r=t.rootPrefixCls,n=t.page,o=t.active,l=t.className,d=t.showTitle,i=t.onClick,s=t.onKeyPress,c=t.itemRender,v="".concat(r,"-item"),u=G(v,"".concat(v,"-").concat(n),z(z({},"".concat(v,"-active"),o),"".concat(v,"-disabled"),!n),l),m=function(){i(n)},f=function(g){s(g,i,n)},p=c(n,"page",J.createElement("a",{rel:"nofollow"},n));return p?J.createElement("li",{title:d?String(n):null,className:u,onClick:m,onKeyDown:f,tabIndex:0},p):null},Ms=function(t,r,n){return n};function oo(){}function ao(e){var t=Number(e);return typeof t=="number"&&!Number.isNaN(t)&&isFinite(t)&&Math.floor(t)===t}function Ft(e,t,r){var n=typeof e>"u"?t:e;return Math.floor((r-1)/n)+1}var Bs=function(t){var r=t.prefixCls,n=r===void 0?"rc-pagination":r,o=t.selectPrefixCls,l=o===void 0?"rc-select":o,d=t.className,i=t.current,s=t.defaultCurrent,c=s===void 0?1:s,v=t.total,u=v===void 0?0:v,m=t.pageSize,f=t.defaultPageSize,p=f===void 0?10:f,b=t.onChange,g=b===void 0?oo:b,h=t.hideOnSinglePage,x=t.align,C=t.showPrevNextJumpers,S=C===void 0?!0:C,w=t.showQuickJumper,E=t.showLessItems,k=t.showTitle,R=k===void 0?!0:k,y=t.onShowSizeChange,P=y===void 0?oo:y,I=t.locale,T=I===void 0?Ts:I,O=t.style,N=t.totalBoundaryShowSizeChanger,$=N===void 0?50:N,K=t.disabled,D=t.simple,j=t.showTotal,M=t.showSizeChanger,q=M===void 0?u>$:M,X=t.sizeChangerRender,U=t.pageSizeOptions,ee=t.itemRender,se=ee===void 0?Ms:ee,ge=t.jumpPrevIcon,ce=t.jumpNextIcon,Z=t.prevIcon,te=t.nextIcon,pe=J.useRef(null),ie=hn(10,{value:m,defaultValue:p}),W=Ce(ie,2),V=W[0],F=W[1],Q=hn(1,{value:i,defaultValue:c,postState:function(ke){return Math.max(1,Math.min(ke,Ft(void 0,V,u)))}}),oe=Ce(Q,2),Y=oe[0],ae=oe[1],Oe=J.useState(Y),Ae=Ce(Oe,2),Ee=Ae[0],B=Ae[1];a.useEffect(function(){B(Y)},[Y]);var H=Math.max(1,Y-(E?3:5)),de=Math.min(Ft(void 0,V,u),Y+(E?3:5));function ve(ue,ke){var He=ue||J.createElement("button",{type:"button","aria-label":ke,className:"".concat(n,"-item-link")});return typeof ue=="function"&&(He=J.createElement(ue,A({},t))),He}function fe(ue){var ke=ue.target.value,He=Ft(void 0,V,u),ht;return ke===""?ht=ke:Number.isNaN(Number(ke))?ht=Ee:ke>=He?ht=He:ht=Number(ke),ht}function Pe(ue){return ao(ue)&&ue!==Y&&ao(u)&&u>0}var je=u>V?w:!1;function Fe(ue){(ue.keyCode===qe.UP||ue.keyCode===qe.DOWN)&&ue.preventDefault()}function xe(ue){var ke=fe(ue);switch(ke!==Ee&&B(ke),ue.keyCode){case qe.ENTER:_(ke);break;case qe.UP:_(ke-1);break;case qe.DOWN:_(ke+1);break}}function ye(ue){_(fe(ue))}function re(ue){var ke=Ft(ue,V,u),He=Y>ke&&ke!==0?ke:Y;F(ue),B(He),P==null||P(Y,ue),ae(He),g==null||g(He,ue)}function _(ue){if(Pe(ue)&&!K){var ke=Ft(void 0,V,u),He=ue;return ue>ke?He=ke:ue<1&&(He=1),He!==Ee&&B(He),ae(He),g==null||g(He,V),He}return Y}var Ie=Y>1,Ke=Y<Ft(void 0,V,u);function he(){Ie&&_(Y-1)}function Be(){Ke&&_(Y+1)}function Ne(){_(H)}function _e(){_(de)}function We(ue,ke){if(ue.key==="Enter"||ue.charCode===qe.ENTER||ue.keyCode===qe.ENTER){for(var He=arguments.length,ht=new Array(He>2?He-2:0),Lt=2;Lt<He;Lt++)ht[Lt-2]=arguments[Lt];ke.apply(void 0,ht)}}function Ye(ue){We(ue,he)}function gt(ue){We(ue,Be)}function De(ue){We(ue,Ne)}function Ot(ue){We(ue,_e)}function It(ue){var ke=se(ue,"prev",ve(Z,"prev page"));return J.isValidElement(ke)?J.cloneElement(ke,{disabled:!Ie}):ke}function Qe(ue){var ke=se(ue,"next",ve(te,"next page"));return J.isValidElement(ke)?J.cloneElement(ke,{disabled:!Ke}):ke}function Ge(ue){(ue.type==="click"||ue.keyCode===qe.ENTER)&&_(Ee)}var at=null,Ze=kt(t,{aria:!0,data:!0}),Ue=j&&J.createElement("li",{className:"".concat(n,"-total-text")},j(u,[u===0?0:(Y-1)*V+1,Y*V>u?u:Y*V])),et=null,Me=Ft(void 0,V,u);if(h&&u<=V)return null;var Le=[],lt={rootPrefixCls:n,onClick:_,onKeyPress:We,showTitle:R,itemRender:se,page:-1},ln=Y-1>0?Y-1:0,sn=Y+1<Me?Y+1:Me,wt=w&&w.goButton,cn=pt(D)==="object"?D.readOnly:!D,me=wt,be=null;D&&(wt&&(typeof wt=="boolean"?me=J.createElement("button",{type:"button",onClick:Ge,onKeyUp:Ge},T.jump_to_confirm):me=J.createElement("span",{onClick:Ge,onKeyUp:Ge},wt),me=J.createElement("li",{title:R?"".concat(T.jump_to).concat(Y,"/").concat(Me):null,className:"".concat(n,"-simple-pager")},me)),be=J.createElement("li",{title:R?"".concat(Y,"/").concat(Me):null,className:"".concat(n,"-simple-pager")},cn?Ee:J.createElement("input",{type:"text","aria-label":T.jump_to,value:Ee,disabled:K,onKeyDown:Fe,onKeyUp:xe,onChange:xe,onBlur:ye,size:3}),J.createElement("span",{className:"".concat(n,"-slash")},"/"),Me));var Te=E?1:2;if(Me<=3+Te*2){Me||Le.push(J.createElement(mn,le({},lt,{key:"noPager",page:1,className:"".concat(n,"-item-disabled")})));for(var ze=1;ze<=Me;ze+=1)Le.push(J.createElement(mn,le({},lt,{key:ze,page:ze,active:Y===ze})))}else{var Je=E?T.prev_3:T.prev_5,it=E?T.next_3:T.next_5,st=se(H,"jump-prev",ve(ge,"prev page")),Xe=se(de,"jump-next",ve(ce,"next page"));S&&(at=st?J.createElement("li",{title:R?Je:null,key:"prev",onClick:Ne,tabIndex:0,onKeyDown:De,className:G("".concat(n,"-jump-prev"),z({},"".concat(n,"-jump-prev-custom-icon"),!!ge))},st):null,et=Xe?J.createElement("li",{title:R?it:null,key:"next",onClick:_e,tabIndex:0,onKeyDown:Ot,className:G("".concat(n,"-jump-next"),z({},"".concat(n,"-jump-next-custom-icon"),!!ce))},Xe):null);var qt=Math.max(1,Y-Te),dn=Math.min(Y+Te,Me);Y-1<=Te&&(dn=1+Te*2),Me-Y<=Te&&(qt=Me-Te*2);for(var Pt=qt;Pt<=dn;Pt+=1)Le.push(J.createElement(mn,le({},lt,{key:Pt,page:Pt,active:Y===Pt})));if(Y-1>=Te*2&&Y!==3&&(Le[0]=J.cloneElement(Le[0],{className:G("".concat(n,"-item-after-jump-prev"),Le[0].props.className)}),Le.unshift(at)),Me-Y>=Te*2&&Y!==Me-2){var zt=Le[Le.length-1];Le[Le.length-1]=J.cloneElement(zt,{className:G("".concat(n,"-item-before-jump-next"),zt.props.className)}),Le.push(et)}qt!==1&&Le.unshift(J.createElement(mn,le({},lt,{key:1,page:1}))),dn!==Me&&Le.push(J.createElement(mn,le({},lt,{key:Me,page:Me})))}var mt=It(ln);if(mt){var un=!Ie||!Me;mt=J.createElement("li",{title:R?T.prev_page:null,onClick:he,tabIndex:un?null:0,onKeyDown:Ye,className:G("".concat(n,"-prev"),z({},"".concat(n,"-disabled"),un)),"aria-disabled":un},mt)}var jt=Qe(sn);if(jt){var _t,Xt;D?(_t=!Ke,Xt=Ie?0:null):(_t=!Ke||!Me,Xt=_t?null:0),jt=J.createElement("li",{title:R?T.next_page:null,onClick:Be,tabIndex:Xt,onKeyDown:gt,className:G("".concat(n,"-next"),z({},"".concat(n,"-disabled"),_t)),"aria-disabled":_t},jt)}var Mn=G(n,d,z(z(z(z(z({},"".concat(n,"-start"),x==="start"),"".concat(n,"-center"),x==="center"),"".concat(n,"-end"),x==="end"),"".concat(n,"-simple"),D),"".concat(n,"-disabled"),K));return J.createElement("ul",le({className:Mn,style:O,ref:pe},Ze),Ue,mt,D?be:Le,jt,J.createElement(Ds,{locale:T,rootPrefixCls:n,disabled:K,selectPrefixCls:l,changeSize:re,pageSize:V,pageSizeOptions:U,quickGo:je?_:null,goButton:me,showSizeChanger:q,sizeChangerRender:X}))};const zs=e=>{const{componentCls:t}=e;return{[`${t}-disabled`]:{"&, &:hover":{cursor:"not-allowed",[`${t}-item-link`]:{color:e.colorTextDisabled,cursor:"not-allowed"}},"&:focus-visible":{cursor:"not-allowed",[`${t}-item-link`]:{color:e.colorTextDisabled,cursor:"not-allowed"}}},[`&${t}-disabled`]:{cursor:"not-allowed",[`${t}-item`]:{cursor:"not-allowed",backgroundColor:"transparent","&:hover, &:active":{backgroundColor:"transparent"},a:{color:e.colorTextDisabled,backgroundColor:"transparent",border:"none",cursor:"not-allowed"},"&-active":{borderColor:e.colorBorder,backgroundColor:e.itemActiveBgDisabled,"&:hover, &:active":{backgroundColor:e.itemActiveBgDisabled},a:{color:e.itemActiveColorDisabled}}},[`${t}-item-link`]:{color:e.colorTextDisabled,cursor:"not-allowed","&:hover, &:active":{backgroundColor:"transparent"},[`${t}-simple&`]:{backgroundColor:"transparent","&:hover, &:active":{backgroundColor:"transparent"}}},[`${t}-simple-pager`]:{color:e.colorTextDisabled},[`${t}-jump-prev, ${t}-jump-next`]:{[`${t}-item-link-icon`]:{opacity:0},[`${t}-item-ellipsis`]:{opacity:1}}},[`&${t}-simple`]:{[`${t}-prev, ${t}-next`]:{[`&${t}-disabled ${t}-item-link`]:{"&:hover, &:active":{backgroundColor:"transparent"}}}}}},js=e=>{const{componentCls:t}=e;return{[`&${t}-mini ${t}-total-text, &${t}-mini ${t}-simple-pager`]:{height:e.itemSizeSM,lineHeight:L(e.itemSizeSM)},[`&${t}-mini ${t}-item`]:{minWidth:e.itemSizeSM,height:e.itemSizeSM,margin:0,lineHeight:L(e.calc(e.itemSizeSM).sub(2).equal())},[`&${t}-mini ${t}-prev, &${t}-mini ${t}-next`]:{minWidth:e.itemSizeSM,height:e.itemSizeSM,margin:0,lineHeight:L(e.itemSizeSM)},[`&${t}-mini:not(${t}-disabled)`]:{[`${t}-prev, ${t}-next`]:{[`&:hover ${t}-item-link`]:{backgroundColor:e.colorBgTextHover},[`&:active ${t}-item-link`]:{backgroundColor:e.colorBgTextActive},[`&${t}-disabled:hover ${t}-item-link`]:{backgroundColor:"transparent"}}},[`
    &${t}-mini ${t}-prev ${t}-item-link,
    &${t}-mini ${t}-next ${t}-item-link
    `]:{backgroundColor:"transparent",borderColor:"transparent","&::after":{height:e.itemSizeSM,lineHeight:L(e.itemSizeSM)}},[`&${t}-mini ${t}-jump-prev, &${t}-mini ${t}-jump-next`]:{height:e.itemSizeSM,marginInlineEnd:0,lineHeight:L(e.itemSizeSM)},[`&${t}-mini ${t}-options`]:{marginInlineStart:e.paginationMiniOptionsMarginInlineStart,"&-size-changer":{top:e.miniOptionsSizeChangerTop},"&-quick-jumper":{height:e.itemSizeSM,lineHeight:L(e.itemSizeSM),input:Object.assign(Object.assign({},vl(e)),{width:e.paginationMiniQuickJumperInputWidth,height:e.controlHeightSM})}}}},_s=e=>{const{componentCls:t}=e;return{[`
    &${t}-simple ${t}-prev,
    &${t}-simple ${t}-next
    `]:{height:e.itemSizeSM,lineHeight:L(e.itemSizeSM),verticalAlign:"top",[`${t}-item-link`]:{height:e.itemSizeSM,backgroundColor:"transparent",border:0,"&:hover":{backgroundColor:e.colorBgTextHover},"&:active":{backgroundColor:e.colorBgTextActive},"&::after":{height:e.itemSizeSM,lineHeight:L(e.itemSizeSM)}}},[`&${t}-simple ${t}-simple-pager`]:{display:"inline-block",height:e.itemSizeSM,marginInlineEnd:e.marginXS,input:{boxSizing:"border-box",height:"100%",padding:`0 ${L(e.paginationItemPaddingInline)}`,textAlign:"center",backgroundColor:e.itemInputBg,border:`${L(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadius,outline:"none",transition:`border-color ${e.motionDurationMid}`,color:"inherit","&:hover":{borderColor:e.colorPrimary},"&:focus":{borderColor:e.colorPrimaryHover,boxShadow:`${L(e.inputOutlineOffset)} 0 ${L(e.controlOutlineWidth)} ${e.controlOutline}`},"&[disabled]":{color:e.colorTextDisabled,backgroundColor:e.colorBgContainerDisabled,borderColor:e.colorBorder,cursor:"not-allowed"}}}}},Ls=e=>{const{componentCls:t}=e;return{[`${t}-jump-prev, ${t}-jump-next`]:{outline:0,[`${t}-item-container`]:{position:"relative",[`${t}-item-link-icon`]:{color:e.colorPrimary,fontSize:e.fontSizeSM,opacity:0,transition:`all ${e.motionDurationMid}`,"&-svg":{top:0,insetInlineEnd:0,bottom:0,insetInlineStart:0,margin:"auto"}},[`${t}-item-ellipsis`]:{position:"absolute",top:0,insetInlineEnd:0,bottom:0,insetInlineStart:0,display:"block",margin:"auto",color:e.colorTextDisabled,letterSpacing:e.paginationEllipsisLetterSpacing,textAlign:"center",textIndent:e.paginationEllipsisTextIndent,opacity:1,transition:`all ${e.motionDurationMid}`}},"&:hover":{[`${t}-item-link-icon`]:{opacity:1},[`${t}-item-ellipsis`]:{opacity:0}}},[`
    ${t}-prev,
    ${t}-jump-prev,
    ${t}-jump-next
    `]:{marginInlineEnd:e.marginXS},[`
    ${t}-prev,
    ${t}-next,
    ${t}-jump-prev,
    ${t}-jump-next
    `]:{display:"inline-block",minWidth:e.itemSize,height:e.itemSize,color:e.colorText,fontFamily:e.fontFamily,lineHeight:L(e.itemSize),textAlign:"center",verticalAlign:"middle",listStyle:"none",borderRadius:e.borderRadius,cursor:"pointer",transition:`all ${e.motionDurationMid}`},[`${t}-prev, ${t}-next`]:{outline:0,button:{color:e.colorText,cursor:"pointer",userSelect:"none"},[`${t}-item-link`]:{display:"block",width:"100%",height:"100%",padding:0,fontSize:e.fontSizeSM,textAlign:"center",backgroundColor:"transparent",border:`${L(e.lineWidth)} ${e.lineType} transparent`,borderRadius:e.borderRadius,outline:"none",transition:`all ${e.motionDurationMid}`},[`&:hover ${t}-item-link`]:{backgroundColor:e.colorBgTextHover},[`&:active ${t}-item-link`]:{backgroundColor:e.colorBgTextActive},[`&${t}-disabled:hover`]:{[`${t}-item-link`]:{backgroundColor:"transparent"}}},[`${t}-slash`]:{marginInlineEnd:e.paginationSlashMarginInlineEnd,marginInlineStart:e.paginationSlashMarginInlineStart},[`${t}-options`]:{display:"inline-block",marginInlineStart:e.margin,verticalAlign:"middle","&-size-changer":{display:"inline-block",width:"auto"},"&-quick-jumper":{display:"inline-block",height:e.controlHeight,marginInlineStart:e.marginXS,lineHeight:L(e.controlHeight),verticalAlign:"top",input:Object.assign(Object.assign(Object.assign({},dl(e)),ul(e,{borderColor:e.colorBorder,hoverBorderColor:e.colorPrimaryHover,activeBorderColor:e.colorPrimary,activeShadow:e.activeShadow})),{"&[disabled]":Object.assign({},fl(e)),width:e.calc(e.controlHeightLG).mul(1.25).equal(),height:e.controlHeight,boxSizing:"border-box",margin:0,marginInlineStart:e.marginXS,marginInlineEnd:e.marginXS})}}}},Hs=e=>{const{componentCls:t}=e;return{[`${t}-item`]:{display:"inline-block",minWidth:e.itemSize,height:e.itemSize,marginInlineEnd:e.marginXS,fontFamily:e.fontFamily,lineHeight:L(e.calc(e.itemSize).sub(2).equal()),textAlign:"center",verticalAlign:"middle",listStyle:"none",backgroundColor:e.itemBg,border:`${L(e.lineWidth)} ${e.lineType} transparent`,borderRadius:e.borderRadius,outline:0,cursor:"pointer",userSelect:"none",a:{display:"block",padding:`0 ${L(e.paginationItemPaddingInline)}`,color:e.colorText,"&:hover":{textDecoration:"none"}},[`&:not(${t}-item-active)`]:{"&:hover":{transition:`all ${e.motionDurationMid}`,backgroundColor:e.colorBgTextHover},"&:active":{backgroundColor:e.colorBgTextActive}},"&-active":{fontWeight:e.fontWeightStrong,backgroundColor:e.itemActiveBg,borderColor:e.colorPrimary,a:{color:e.colorPrimary},"&:hover":{borderColor:e.colorPrimaryHover},"&:hover a":{color:e.colorPrimaryHover}}}}},As=e=>{const{componentCls:t}=e;return{[t]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},Ct(e)),{display:"flex","&-start":{justifyContent:"start"},"&-center":{justifyContent:"center"},"&-end":{justifyContent:"end"},"ul, ol":{margin:0,padding:0,listStyle:"none"},"&::after":{display:"block",clear:"both",height:0,overflow:"hidden",visibility:"hidden",content:'""'},[`${t}-total-text`]:{display:"inline-block",height:e.itemSize,marginInlineEnd:e.marginXS,lineHeight:L(e.calc(e.itemSize).sub(2).equal()),verticalAlign:"middle"}}),Hs(e)),Ls(e)),_s(e)),js(e)),zs(e)),{[`@media only screen and (max-width: ${e.screenLG}px)`]:{[`${t}-item`]:{"&-after-jump-prev, &-before-jump-next":{display:"none"}}},[`@media only screen and (max-width: ${e.screenSM}px)`]:{[`${t}-options`]:{display:"none"}}}),[`&${e.componentCls}-rtl`]:{direction:"rtl"}}},Fs=e=>{const{componentCls:t}=e;return{[`${t}:not(${t}-disabled)`]:{[`${t}-item`]:Object.assign({},Wo(e)),[`${t}-jump-prev, ${t}-jump-next`]:{"&:focus-visible":Object.assign({[`${t}-item-link-icon`]:{opacity:1},[`${t}-item-ellipsis`]:{opacity:0}},Yt(e))},[`${t}-prev, ${t}-next`]:{[`&:focus-visible ${t}-item-link`]:Object.assign({},Yt(e))}}}},ba=e=>Object.assign({itemBg:e.colorBgContainer,itemSize:e.controlHeight,itemSizeSM:e.controlHeightSM,itemActiveBg:e.colorBgContainer,itemLinkBg:e.colorBgContainer,itemActiveColorDisabled:e.colorTextDisabled,itemActiveBgDisabled:e.controlItemBgActiveDisabled,itemInputBg:e.colorBgContainer,miniOptionsSizeChangerTop:0},cl(e)),ya=e=>tn(e,{inputOutlineOffset:0,paginationMiniOptionsMarginInlineStart:e.calc(e.marginXXS).div(2).equal(),paginationMiniQuickJumperInputWidth:e.calc(e.controlHeightLG).mul(1.1).equal(),paginationItemPaddingInline:e.calc(e.marginXXS).mul(1.5).equal(),paginationEllipsisLetterSpacing:e.calc(e.marginXXS).div(2).equal(),paginationSlashMarginInlineStart:e.marginSM,paginationSlashMarginInlineEnd:e.marginSM,paginationEllipsisTextIndent:"0.13em"},sl(e)),Ws=en("Pagination",e=>{const t=ya(e);return[As(t),Fs(t)]},ba),Vs=e=>{const{componentCls:t}=e;return{[`${t}${t}-bordered${t}-disabled:not(${t}-mini)`]:{"&, &:hover":{[`${t}-item-link`]:{borderColor:e.colorBorder}},"&:focus-visible":{[`${t}-item-link`]:{borderColor:e.colorBorder}},[`${t}-item, ${t}-item-link`]:{backgroundColor:e.colorBgContainerDisabled,borderColor:e.colorBorder,[`&:hover:not(${t}-item-active)`]:{backgroundColor:e.colorBgContainerDisabled,borderColor:e.colorBorder,a:{color:e.colorTextDisabled}},[`&${t}-item-active`]:{backgroundColor:e.itemActiveBgDisabled}},[`${t}-prev, ${t}-next`]:{"&:hover button":{backgroundColor:e.colorBgContainerDisabled,borderColor:e.colorBorder,color:e.colorTextDisabled},[`${t}-item-link`]:{backgroundColor:e.colorBgContainerDisabled,borderColor:e.colorBorder}}},[`${t}${t}-bordered:not(${t}-mini)`]:{[`${t}-prev, ${t}-next`]:{"&:hover button":{borderColor:e.colorPrimaryHover,backgroundColor:e.itemBg},[`${t}-item-link`]:{backgroundColor:e.itemLinkBg,borderColor:e.colorBorder},[`&:hover ${t}-item-link`]:{borderColor:e.colorPrimary,backgroundColor:e.itemBg,color:e.colorPrimary},[`&${t}-disabled`]:{[`${t}-item-link`]:{borderColor:e.colorBorder,color:e.colorTextDisabled}}},[`${t}-item`]:{backgroundColor:e.itemBg,border:`${L(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,[`&:hover:not(${t}-item-active)`]:{borderColor:e.colorPrimary,backgroundColor:e.itemBg,a:{color:e.colorPrimary}},"&-active":{borderColor:e.colorPrimary}}}}},qs=ml(["Pagination","bordered"],e=>{const t=ya(e);return[Vs(t)]},ba);function lo(e){return a.useMemo(()=>typeof e=="boolean"?[e,{}]:e&&typeof e=="object"?[!0,e]:[void 0,void 0],[e])}var Xs=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const Gs=e=>{const{align:t,prefixCls:r,selectPrefixCls:n,className:o,rootClassName:l,style:d,size:i,locale:s,responsive:c,showSizeChanger:v,selectComponentClass:u,pageSizeOptions:m}=e,f=Xs(e,["align","prefixCls","selectPrefixCls","className","rootClassName","style","size","locale","responsive","showSizeChanger","selectComponentClass","pageSizeOptions"]),{xs:p}=Vo(c),[,b]=fr(),{getPrefixCls:g,direction:h,showSizeChanger:x,className:C,style:S}=ur("pagination"),w=g("pagination",r),[E,k,R]=Ws(w),y=cr(i),P=y==="small"||!!(p&&!y&&c),[I]=pl("Pagination",gl),T=Object.assign(Object.assign({},I),s),[O,N]=lo(v),[$,K]=lo(x),D=O??$,j=N??K,M=u||vr,q=a.useMemo(()=>m?m.map(ce=>Number(ce)):void 0,[m]),X=ce=>{var Z;const{disabled:te,size:pe,onSizeChange:ie,"aria-label":W,className:V,options:F}=ce,{className:Q,onChange:oe}=j||{},Y=(Z=F.find(ae=>String(ae.value)===String(pe)))===null||Z===void 0?void 0:Z.value;return a.createElement(M,Object.assign({disabled:te,showSearch:!0,popupMatchSelectWidth:!1,getPopupContainer:ae=>ae.parentNode,"aria-label":W,options:F},j,{value:Y,onChange:(ae,Oe)=>{ie==null||ie(ae),oe==null||oe(ae,Oe)},size:P?"small":"middle",className:G(V,Q)}))},U=a.useMemo(()=>{const ce=a.createElement("span",{className:`${w}-item-ellipsis`},"•••"),Z=a.createElement("button",{className:`${w}-item-link`,type:"button",tabIndex:-1},h==="rtl"?a.createElement(Ar,null):a.createElement(Hr,null)),te=a.createElement("button",{className:`${w}-item-link`,type:"button",tabIndex:-1},h==="rtl"?a.createElement(Hr,null):a.createElement(Ar,null)),pe=a.createElement("a",{className:`${w}-item-link`},a.createElement("div",{className:`${w}-item-container`},h==="rtl"?a.createElement(eo,{className:`${w}-item-link-icon`}):a.createElement(Zr,{className:`${w}-item-link-icon`}),ce)),ie=a.createElement("a",{className:`${w}-item-link`},a.createElement("div",{className:`${w}-item-container`},h==="rtl"?a.createElement(Zr,{className:`${w}-item-link-icon`}):a.createElement(eo,{className:`${w}-item-link-icon`}),ce));return{prevIcon:Z,nextIcon:te,jumpPrevIcon:pe,jumpNextIcon:ie}},[h,w]),ee=g("select",n),se=G({[`${w}-${t}`]:!!t,[`${w}-mini`]:P,[`${w}-rtl`]:h==="rtl",[`${w}-bordered`]:b.wireframe},C,o,l,k,R),ge=Object.assign(Object.assign({},S),d);return E(a.createElement(a.Fragment,null,b.wireframe&&a.createElement(qs,{prefixCls:w}),a.createElement(Bs,Object.assign({},U,f,{style:ge,prefixCls:w,selectPrefixCls:ee,className:se,locale:T,pageSizeOptions:q,showSizeChanger:D,sizeChangerRender:X}))))};var Et={},wn="rc-table-internal-hook";function Cr(e){var t=a.createContext(void 0),r=function(o){var l=o.value,d=o.children,i=a.useRef(l);i.current=l;var s=a.useState(function(){return{getValue:function(){return i.current},listeners:new Set}}),c=Ce(s,1),v=c[0];return xt(function(){Qa.unstable_batchedUpdates(function(){v.listeners.forEach(function(u){u(l)})})},[l]),a.createElement(t.Provider,{value:v},d)};return{Context:t,Provider:r,defaultValue:e}}function rt(e,t){var r=Mt(typeof t=="function"?t:function(u){if(t===void 0)return u;if(!Array.isArray(t))return u[t];var m={};return t.forEach(function(f){m[f]=u[f]}),m}),n=a.useContext(e==null?void 0:e.Context),o=n||{},l=o.listeners,d=o.getValue,i=a.useRef();i.current=r(n?d():e==null?void 0:e.defaultValue);var s=a.useState({}),c=Ce(s,2),v=c[1];return xt(function(){if(!n)return;function u(m){var f=r(m);bn(i.current,f,!0)||v({})}return l.add(u),function(){l.delete(u)}},[n]),i.current}function Us(){var e=a.createContext(null);function t(){return a.useContext(e)}function r(o,l){var d=Fr(o),i=function(c,v){var u=d?{ref:v}:{},m=a.useRef(0),f=a.useRef(c),p=t();return p!==null?a.createElement(o,le({},c,u)):((!l||l(f.current,c))&&(m.current+=1),f.current=c,a.createElement(e.Provider,{value:m.current},a.createElement(o,le({},c,u))))};return d?a.forwardRef(i):i}function n(o,l){var d=Fr(o),i=function(c,v){var u=d?{ref:v}:{};return t(),a.createElement(o,le({},c,u))};return d?a.memo(a.forwardRef(i),l):a.memo(i,l)}return{makeImmutable:r,responseImmutable:n,useImmutableMark:t}}var xr=Us(),Ca=xr.makeImmutable,nn=xr.responseImmutable,Js=xr.useImmutableMark,dt=Cr(),xa=a.createContext({renderWithProps:!1}),Ys="RC_TABLE_KEY";function Qs(e){return e==null?[]:Array.isArray(e)?e:[e]}function Tn(e){var t=[],r={};return e.forEach(function(n){for(var o=n||{},l=o.key,d=o.dataIndex,i=l||Qs(d).join("-")||Ys;r[i];)i="".concat(i,"_next");r[i]=!0,t.push(i)}),t}function Qn(e){return e!=null}function Zs(e){return typeof e=="number"&&!Number.isNaN(e)}function ec(e){return e&&pt(e)==="object"&&!Array.isArray(e)&&!a.isValidElement(e)}function tc(e,t,r,n,o,l){var d=a.useContext(xa),i=Js(),s=qo(function(){if(Qn(n))return[n];var c=t==null||t===""?[]:Array.isArray(t)?t:[t],v=mr(e,c),u=v,m=void 0;if(o){var f=o(v,e,r);ec(f)?(u=f.children,m=f.props,d.renderWithProps=!0):u=f}return[u,m]},[i,e,n,t,o,r],function(c,v){if(l){var u=Ce(c,2),m=u[1],f=Ce(v,2),p=f[1];return l(p,m)}return d.renderWithProps?!0:!bn(c,v,!0)});return s}function nc(e,t,r,n){var o=e+t-1;return e<=n&&o>=r}function rc(e,t){return rt(dt,function(r){var n=nc(e,t||1,r.hoverStartRow,r.hoverEndRow);return[n,r.onHover]})}var oc=function(t){var r=t.ellipsis,n=t.rowType,o=t.children,l,d=r===!0?{showTitle:!0}:r;return d&&(d.showTitle||n==="header")&&(typeof o=="string"||typeof o=="number"?l=o.toString():a.isValidElement(o)&&typeof o.props.children=="string"&&(l=o.props.children)),l};function ac(e){var t,r,n,o,l,d,i,s,c=e.component,v=e.children,u=e.ellipsis,m=e.scope,f=e.prefixCls,p=e.className,b=e.align,g=e.record,h=e.render,x=e.dataIndex,C=e.renderIndex,S=e.shouldCellUpdate,w=e.index,E=e.rowType,k=e.colSpan,R=e.rowSpan,y=e.fixLeft,P=e.fixRight,I=e.firstFixLeft,T=e.lastFixLeft,O=e.firstFixRight,N=e.lastFixRight,$=e.appendNode,K=e.additionalProps,D=K===void 0?{}:K,j=e.isSticky,M="".concat(f,"-cell"),q=rt(dt,["supportSticky","allColumnsFixedLeft","rowHoverable"]),X=q.supportSticky,U=q.allColumnsFixedLeft,ee=q.rowHoverable,se=tc(g,x,C,v,h,S),ge=Ce(se,2),ce=ge[0],Z=ge[1],te={},pe=typeof y=="number"&&X,ie=typeof P=="number"&&X;pe&&(te.position="sticky",te.left=y),ie&&(te.position="sticky",te.right=P);var W=(t=(r=(n=Z==null?void 0:Z.colSpan)!==null&&n!==void 0?n:D.colSpan)!==null&&r!==void 0?r:k)!==null&&t!==void 0?t:1,V=(o=(l=(d=Z==null?void 0:Z.rowSpan)!==null&&d!==void 0?d:D.rowSpan)!==null&&l!==void 0?l:R)!==null&&o!==void 0?o:1,F=rc(w,V),Q=Ce(F,2),oe=Q[0],Y=Q[1],ae=Mt(function(ve){var fe;g&&Y(w,w+V-1),D==null||(fe=D.onMouseEnter)===null||fe===void 0||fe.call(D,ve)}),Oe=Mt(function(ve){var fe;g&&Y(-1,-1),D==null||(fe=D.onMouseLeave)===null||fe===void 0||fe.call(D,ve)});if(W===0||V===0)return null;var Ae=(i=D.title)!==null&&i!==void 0?i:oc({rowType:E,ellipsis:u,children:ce}),Ee=G(M,p,(s={},z(z(z(z(z(z(z(z(z(z(s,"".concat(M,"-fix-left"),pe&&X),"".concat(M,"-fix-left-first"),I&&X),"".concat(M,"-fix-left-last"),T&&X),"".concat(M,"-fix-left-all"),T&&U&&X),"".concat(M,"-fix-right"),ie&&X),"".concat(M,"-fix-right-first"),O&&X),"".concat(M,"-fix-right-last"),N&&X),"".concat(M,"-ellipsis"),u),"".concat(M,"-with-append"),$),"".concat(M,"-fix-sticky"),(pe||ie)&&j&&X),z(s,"".concat(M,"-row-hover"),!Z&&oe)),D.className,Z==null?void 0:Z.className),B={};b&&(B.textAlign=b);var H=A(A(A(A({},Z==null?void 0:Z.style),te),B),D.style),de=ce;return pt(de)==="object"&&!Array.isArray(de)&&!a.isValidElement(de)&&(de=null),u&&(T||O)&&(de=a.createElement("span",{className:"".concat(M,"-content")},de)),a.createElement(c,le({},Z,D,{className:Ee,style:H,title:Ae,scope:m,onMouseEnter:ee?ae:void 0,onMouseLeave:ee?Oe:void 0,colSpan:W!==1?W:null,rowSpan:V!==1?V:null}),$,de)}const rn=a.memo(ac);function Sr(e,t,r,n,o){var l=r[e]||{},d=r[t]||{},i,s;l.fixed==="left"?i=n.left[o==="rtl"?t:e]:d.fixed==="right"&&(s=n.right[o==="rtl"?e:t]);var c=!1,v=!1,u=!1,m=!1,f=r[t+1],p=r[e-1],b=f&&!f.fixed||p&&!p.fixed||r.every(function(S){return S.fixed==="left"});if(o==="rtl"){if(i!==void 0){var g=p&&p.fixed==="left";m=!g&&b}else if(s!==void 0){var h=f&&f.fixed==="right";u=!h&&b}}else if(i!==void 0){var x=f&&f.fixed==="left";c=!x&&b}else if(s!==void 0){var C=p&&p.fixed==="right";v=!C&&b}return{fixLeft:i,fixRight:s,lastFixLeft:c,firstFixRight:v,lastFixRight:u,firstFixLeft:m,isSticky:n.isSticky}}var Sa=a.createContext({});function lc(e){var t=e.className,r=e.index,n=e.children,o=e.colSpan,l=o===void 0?1:o,d=e.rowSpan,i=e.align,s=rt(dt,["prefixCls","direction"]),c=s.prefixCls,v=s.direction,u=a.useContext(Sa),m=u.scrollColumnIndex,f=u.stickyOffsets,p=u.flattenColumns,b=r+l-1,g=b+1===m?l+1:l,h=Sr(r,r+g-1,p,f,v);return a.createElement(rn,le({className:t,index:r,component:"td",prefixCls:c,record:null,dataIndex:null,align:i,colSpan:g,rowSpan:d,render:function(){return n}},h))}var ic=["children"];function sc(e){var t=e.children,r=ft(e,ic);return a.createElement("tr",r,t)}function Kn(e){var t=e.children;return t}Kn.Row=sc;Kn.Cell=lc;function cc(e){var t=e.children,r=e.stickyOffsets,n=e.flattenColumns,o=rt(dt,"prefixCls"),l=n.length-1,d=n[l],i=a.useMemo(function(){return{stickyOffsets:r,flattenColumns:n,scrollColumnIndex:d!=null&&d.scrollbar?l:null}},[d,n,l,r]);return a.createElement(Sa.Provider,{value:i},a.createElement("tfoot",{className:"".concat(o,"-summary")},t))}const Nn=nn(cc);var wa=Kn;function dc(e){return null}function uc(e){return null}function $a(e,t,r,n,o,l,d){var i=l(t,d);e.push({record:t,indent:r,index:d,rowKey:i});var s=o==null?void 0:o.has(i);if(t&&Array.isArray(t[n])&&s)for(var c=0;c<t[n].length;c+=1)$a(e,t[n][c],r+1,n,o,l,c)}function Ea(e,t,r,n){var o=a.useMemo(function(){if(r!=null&&r.size){for(var l=[],d=0;d<(e==null?void 0:e.length);d+=1){var i=e[d];$a(l,i,0,t,r,n,d)}return l}return e==null?void 0:e.map(function(s,c){return{record:s,indent:0,index:c,rowKey:n(s,c)}})},[e,t,r,n]);return o}function Na(e,t,r,n){var o=rt(dt,["prefixCls","fixedInfoList","flattenColumns","expandableType","expandRowByClick","onTriggerExpand","rowClassName","expandedRowClassName","indentSize","expandIcon","expandedRowRender","expandIconColumnIndex","expandedKeys","childrenColumnName","rowExpandable","onRow"]),l=o.flattenColumns,d=o.expandableType,i=o.expandedKeys,s=o.childrenColumnName,c=o.onTriggerExpand,v=o.rowExpandable,u=o.onRow,m=o.expandRowByClick,f=o.rowClassName,p=d==="nest",b=d==="row"&&(!v||v(e)),g=b||p,h=i&&i.has(t),x=s&&e&&e[s],C=Mt(c),S=u==null?void 0:u(e,r),w=S==null?void 0:S.onClick,E=function(P){m&&g&&c(e,P);for(var I=arguments.length,T=new Array(I>1?I-1:0),O=1;O<I;O++)T[O-1]=arguments[O];w==null||w.apply(void 0,[P].concat(T))},k;typeof f=="string"?k=f:typeof f=="function"&&(k=f(e,r,n));var R=Tn(l);return A(A({},o),{},{columnsKey:R,nestExpandable:p,expanded:h,hasNestChildren:x,record:e,onTriggerExpand:C,rowSupportExpand:b,expandable:g,rowProps:A(A({},S),{},{className:G(k,S==null?void 0:S.className),onClick:E})})}function ka(e){var t=e.prefixCls,r=e.children,n=e.component,o=e.cellComponent,l=e.className,d=e.expanded,i=e.colSpan,s=e.isEmpty,c=e.stickyOffset,v=c===void 0?0:c,u=rt(dt,["scrollbarSize","fixHeader","fixColumn","componentWidth","horizonScroll"]),m=u.scrollbarSize,f=u.fixHeader,p=u.fixColumn,b=u.componentWidth,g=u.horizonScroll,h=r;return(s?g&&b:p)&&(h=a.createElement("div",{style:{width:b-v-(f&&!s?m:0),position:"sticky",left:v,overflow:"hidden"},className:"".concat(t,"-expanded-row-fixed")},h)),a.createElement(n,{className:l,style:{display:d?null:"none"}},a.createElement(rn,{component:o,prefixCls:t,colSpan:i},h))}function fc(e){var t=e.prefixCls,r=e.record,n=e.onExpand,o=e.expanded,l=e.expandable,d="".concat(t,"-row-expand-icon");if(!l)return a.createElement("span",{className:G(d,"".concat(t,"-row-spaced"))});var i=function(c){n(r,c),c.stopPropagation()};return a.createElement("span",{className:G(d,z(z({},"".concat(t,"-row-expanded"),o),"".concat(t,"-row-collapsed"),!o)),onClick:i})}function vc(e,t,r){var n=[];function o(l){(l||[]).forEach(function(d,i){n.push(t(d,i)),o(d[r])})}return o(e),n}function Ra(e,t,r,n){return typeof e=="string"?e:typeof e=="function"?e(t,r,n):""}function Oa(e,t,r,n,o){var l,d=arguments.length>5&&arguments[5]!==void 0?arguments[5]:[],i=arguments.length>6&&arguments[6]!==void 0?arguments[6]:0,s=e.record,c=e.prefixCls,v=e.columnsKey,u=e.fixedInfoList,m=e.expandIconColumnIndex,f=e.nestExpandable,p=e.indentSize,b=e.expandIcon,g=e.expanded,h=e.hasNestChildren,x=e.onTriggerExpand,C=e.expandable,S=e.expandedKeys,w=v[r],E=u[r],k;r===(m||0)&&f&&(k=a.createElement(a.Fragment,null,a.createElement("span",{style:{paddingLeft:"".concat(p*n,"px")},className:"".concat(c,"-row-indent indent-level-").concat(n)}),b({prefixCls:c,expanded:g,expandable:h,record:s,onExpand:x})));var R=((l=t.onCell)===null||l===void 0?void 0:l.call(t,s,o))||{};if(i){var y=R.rowSpan,P=y===void 0?1:y;if(C&&P&&r<i){for(var I=P,T=o;T<o+P;T+=1){var O=d[T];S.has(O)&&(I+=1)}R.rowSpan=I}}return{key:w,fixedInfo:E,appendCellNode:k,additionalCellProps:R}}function mc(e){var t=e.className,r=e.style,n=e.record,o=e.index,l=e.renderIndex,d=e.rowKey,i=e.rowKeys,s=e.indent,c=s===void 0?0:s,v=e.rowComponent,u=e.cellComponent,m=e.scopeCellComponent,f=e.expandedRowInfo,p=Na(n,d,o,c),b=p.prefixCls,g=p.flattenColumns,h=p.expandedRowClassName,x=p.expandedRowRender,C=p.rowProps,S=p.expanded,w=p.rowSupportExpand,E=a.useRef(!1);E.current||(E.current=S);var k=Ra(h,n,o,c),R=a.createElement(v,le({},C,{"data-row-key":d,className:G(t,"".concat(b,"-row"),"".concat(b,"-row-level-").concat(c),C==null?void 0:C.className,z({},k,c>=1)),style:A(A({},r),C==null?void 0:C.style)}),g.map(function(I,T){var O=I.render,N=I.dataIndex,$=I.className,K=Oa(p,I,T,c,o,i,f==null?void 0:f.offset),D=K.key,j=K.fixedInfo,M=K.appendCellNode,q=K.additionalCellProps;return a.createElement(rn,le({className:$,ellipsis:I.ellipsis,align:I.align,scope:I.rowScope,component:I.rowScope?m:u,prefixCls:b,key:D,record:n,index:o,renderIndex:l,dataIndex:N,render:O,shouldCellUpdate:I.shouldCellUpdate},j,{appendNode:M,additionalProps:q}))})),y;if(w&&(E.current||S)){var P=x(n,o,c+1,S);y=a.createElement(ka,{expanded:S,className:G("".concat(b,"-expanded-row"),"".concat(b,"-expanded-row-level-").concat(c+1),k),prefixCls:b,component:v,cellComponent:u,colSpan:f?f.colSpan:g.length,stickyOffset:f==null?void 0:f.sticky,isEmpty:!1},P)}return a.createElement(a.Fragment,null,R,y)}const pc=nn(mc);function gc(e){var t=e.columnKey,r=e.onColumnResize,n=a.useRef();return xt(function(){n.current&&r(t,n.current.offsetWidth)},[]),a.createElement(pr,{data:t},a.createElement("td",{ref:n,style:{padding:0,border:0,height:0}},a.createElement("div",{style:{height:0,overflow:"hidden"}}," ")))}function hc(e){var t=e.prefixCls,r=e.columnsKey,n=e.onColumnResize,o=a.useRef(null);return a.createElement("tr",{"aria-hidden":"true",className:"".concat(t,"-measure-row"),style:{height:0,fontSize:0},ref:o},a.createElement(pr.Collection,{onBatchResize:function(d){hl(o.current)&&d.forEach(function(i){var s=i.data,c=i.size;n(s,c.offsetWidth)})}},r.map(function(l){return a.createElement(gc,{key:l,columnKey:l,onColumnResize:n})})))}function bc(e){var t=e.data,r=e.measureColumnWidth,n=rt(dt,["prefixCls","getComponent","onColumnResize","flattenColumns","getRowKey","expandedKeys","childrenColumnName","emptyNode","expandedRowOffset","fixedInfoList","colWidths"]),o=n.prefixCls,l=n.getComponent,d=n.onColumnResize,i=n.flattenColumns,s=n.getRowKey,c=n.expandedKeys,v=n.childrenColumnName,u=n.emptyNode,m=n.expandedRowOffset,f=m===void 0?0:m,p=n.colWidths,b=Ea(t,v,c,s),g=a.useMemo(function(){return b.map(function(y){return y.rowKey})},[b]),h=a.useRef({renderWithProps:!1}),x=a.useMemo(function(){for(var y=i.length-f,P=0,I=0;I<f;I+=1)P+=p[I]||0;return{offset:f,colSpan:y,sticky:P}},[i.length,f,p]),C=l(["body","wrapper"],"tbody"),S=l(["body","row"],"tr"),w=l(["body","cell"],"td"),E=l(["body","cell"],"th"),k;t.length?k=b.map(function(y,P){var I=y.record,T=y.indent,O=y.index,N=y.rowKey;return a.createElement(pc,{key:N,rowKey:N,rowKeys:g,record:I,index:P,renderIndex:O,rowComponent:S,cellComponent:w,scopeCellComponent:E,indent:T,expandedRowInfo:x})}):k=a.createElement(ka,{expanded:!0,className:"".concat(o,"-placeholder"),prefixCls:o,component:S,cellComponent:w,colSpan:i.length,isEmpty:!0},u);var R=Tn(i);return a.createElement(xa.Provider,{value:h.current},a.createElement(C,{className:"".concat(o,"-tbody")},r&&a.createElement(hc,{prefixCls:o,columnsKey:R,onColumnResize:d}),k))}const yc=nn(bc);var Cc=["expandable"],gn="RC_TABLE_INTERNAL_COL_DEFINE";function xc(e){var t=e.expandable,r=ft(e,Cc),n;return"expandable"in e?n=A(A({},r),t):n=r,n.showExpandColumn===!1&&(n.expandIconColumnIndex=-1),n}var Sc=["columnType"];function Ia(e){for(var t=e.colWidths,r=e.columns,n=e.columCount,o=rt(dt,["tableLayout"]),l=o.tableLayout,d=[],i=n||r.length,s=!1,c=i-1;c>=0;c-=1){var v=t[c],u=r&&r[c],m=void 0,f=void 0;if(u&&(m=u[gn],l==="auto"&&(f=u.minWidth)),v||f||m||s){var p=m||{};p.columnType;var b=ft(p,Sc);d.unshift(a.createElement("col",le({key:c,style:{width:v,minWidth:f}},b))),s=!0}}return a.createElement("colgroup",null,d)}var wc=["className","noData","columns","flattenColumns","colWidths","columCount","stickyOffsets","direction","fixHeader","stickyTopOffset","stickyBottomOffset","stickyClassName","onScroll","maxContentScroll","children"];function $c(e,t){return a.useMemo(function(){for(var r=[],n=0;n<t;n+=1){var o=e[n];if(o!==void 0)r[n]=o;else return null}return r},[e.join("_"),t])}var Ec=a.forwardRef(function(e,t){var r=e.className,n=e.noData,o=e.columns,l=e.flattenColumns,d=e.colWidths,i=e.columCount,s=e.stickyOffsets,c=e.direction,v=e.fixHeader,u=e.stickyTopOffset,m=e.stickyBottomOffset,f=e.stickyClassName,p=e.onScroll,b=e.maxContentScroll,g=e.children,h=ft(e,wc),x=rt(dt,["prefixCls","scrollbarSize","isSticky","getComponent"]),C=x.prefixCls,S=x.scrollbarSize,w=x.isSticky,E=x.getComponent,k=E(["header","table"],"table"),R=w&&!v?0:S,y=a.useRef(null),P=a.useCallback(function(j){Wr(t,j),Wr(y,j)},[]);a.useEffect(function(){function j(q){var X=q,U=X.currentTarget,ee=X.deltaX;ee&&(p({currentTarget:U,scrollLeft:U.scrollLeft+ee}),q.preventDefault())}var M=y.current;return M==null||M.addEventListener("wheel",j,{passive:!1}),function(){M==null||M.removeEventListener("wheel",j)}},[]);var I=a.useMemo(function(){return l.every(function(j){return j.width})},[l]),T=l[l.length-1],O={fixed:T?T.fixed:null,scrollbar:!0,onHeaderCell:function(){return{className:"".concat(C,"-cell-scrollbar")}}},N=a.useMemo(function(){return R?[].concat(we(o),[O]):o},[R,o]),$=a.useMemo(function(){return R?[].concat(we(l),[O]):l},[R,l]),K=a.useMemo(function(){var j=s.right,M=s.left;return A(A({},s),{},{left:c==="rtl"?[].concat(we(M.map(function(q){return q+R})),[0]):M,right:c==="rtl"?j:[].concat(we(j.map(function(q){return q+R})),[0]),isSticky:w})},[R,s,w]),D=$c(d,i);return a.createElement("div",{style:A({overflow:"hidden"},w?{top:u,bottom:m}:{}),ref:P,className:G(r,z({},f,!!f))},a.createElement(k,{style:{tableLayout:"fixed",visibility:n||D?null:"hidden"}},(!n||!b||I)&&a.createElement(Ia,{colWidths:D?[].concat(we(D),[R]):[],columCount:i+1,columns:$}),g(A(A({},h),{},{stickyOffsets:K,columns:N,flattenColumns:$}))))});const io=a.memo(Ec);var Nc=function(t){var r=t.cells,n=t.stickyOffsets,o=t.flattenColumns,l=t.rowComponent,d=t.cellComponent,i=t.onHeaderRow,s=t.index,c=rt(dt,["prefixCls","direction"]),v=c.prefixCls,u=c.direction,m;i&&(m=i(r.map(function(p){return p.column}),s));var f=Tn(r.map(function(p){return p.column}));return a.createElement(l,m,r.map(function(p,b){var g=p.column,h=Sr(p.colStart,p.colEnd,o,n,u),x;return g&&g.onHeaderCell&&(x=p.column.onHeaderCell(g)),a.createElement(rn,le({},p,{scope:g.title?p.colSpan>1?"colgroup":"col":null,ellipsis:g.ellipsis,align:g.align,component:d,prefixCls:v,key:f[b]},h,{additionalProps:x,rowType:"header"}))}))};function kc(e){var t=[];function r(d,i){var s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0;t[s]=t[s]||[];var c=i,v=d.filter(Boolean).map(function(u){var m={key:u.key,className:u.className||"",children:u.title,column:u,colStart:c},f=1,p=u.children;return p&&p.length>0&&(f=r(p,c,s+1).reduce(function(b,g){return b+g},0),m.hasSubColumns=!0),"colSpan"in u&&(f=u.colSpan),"rowSpan"in u&&(m.rowSpan=u.rowSpan),m.colSpan=f,m.colEnd=m.colStart+f-1,t[s].push(m),c+=f,f});return v}r(e,0);for(var n=t.length,o=function(i){t[i].forEach(function(s){!("rowSpan"in s)&&!s.hasSubColumns&&(s.rowSpan=n-i)})},l=0;l<n;l+=1)o(l);return t}var Rc=function(t){var r=t.stickyOffsets,n=t.columns,o=t.flattenColumns,l=t.onHeaderRow,d=rt(dt,["prefixCls","getComponent"]),i=d.prefixCls,s=d.getComponent,c=a.useMemo(function(){return kc(n)},[n]),v=s(["header","wrapper"],"thead"),u=s(["header","row"],"tr"),m=s(["header","cell"],"th");return a.createElement(v,{className:"".concat(i,"-thead")},c.map(function(f,p){var b=a.createElement(Nc,{key:p,flattenColumns:o,cells:f,stickyOffsets:r,rowComponent:u,cellComponent:m,onHeaderRow:l,index:p});return b}))};const so=nn(Rc);function co(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";return typeof t=="number"?t:t.endsWith("%")?e*parseFloat(t)/100:null}function Oc(e,t,r){return a.useMemo(function(){if(t&&t>0){var n=0,o=0;e.forEach(function(m){var f=co(t,m.width);f?n+=f:o+=1});var l=Math.max(t,r),d=Math.max(l-n,o),i=o,s=d/o,c=0,v=e.map(function(m){var f=A({},m),p=co(t,f.width);if(p)f.width=p;else{var b=Math.floor(s);f.width=i===1?d:b,d-=b,i-=1}return c+=f.width,f});if(c<l){var u=l/c;d=l,v.forEach(function(m,f){var p=Math.floor(m.width*u);m.width=f===v.length-1?d:p,d-=p})}return[v,Math.max(c,l)]}return[e,t]},[e,t,r])}var Ic=["children"],Pc=["fixed"];function wr(e){return Fo(e).filter(function(t){return a.isValidElement(t)}).map(function(t){var r=t.key,n=t.props,o=n.children,l=ft(n,Ic),d=A({key:r},l);return o&&(d.children=wr(o)),d})}function Pa(e){return e.filter(function(t){return t&&pt(t)==="object"&&!t.hidden}).map(function(t){var r=t.children;return r&&r.length>0?A(A({},t),{},{children:Pa(r)}):t})}function Zn(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"key";return e.filter(function(r){return r&&pt(r)==="object"}).reduce(function(r,n,o){var l=n.fixed,d=l===!0?"left":l,i="".concat(t,"-").concat(o),s=n.children;return s&&s.length>0?[].concat(we(r),we(Zn(s,i).map(function(c){return A({fixed:d},c)}))):[].concat(we(r),[A(A({key:i},n),{},{fixed:d})])},[])}function Tc(e){return e.map(function(t){var r=t.fixed,n=ft(t,Pc),o=r;return r==="left"?o="right":r==="right"&&(o="left"),A({fixed:o},n)})}function Kc(e,t){var r=e.prefixCls,n=e.columns,o=e.children,l=e.expandable,d=e.expandedKeys,i=e.columnTitle,s=e.getRowKey,c=e.onTriggerExpand,v=e.expandIcon,u=e.rowExpandable,m=e.expandIconColumnIndex,f=e.expandedRowOffset,p=f===void 0?0:f,b=e.direction,g=e.expandRowByClick,h=e.columnWidth,x=e.fixed,C=e.scrollWidth,S=e.clientWidth,w=a.useMemo(function(){var N=n||wr(o)||[];return Pa(N.slice())},[n,o]),E=a.useMemo(function(){if(l){var N=w.slice();if(!N.includes(Et)){var $=m||0;$>=0&&($||x==="left"||!x)&&N.splice($,0,Et),x==="right"&&N.splice(w.length,0,Et)}var K=N.indexOf(Et);N=N.filter(function(q,X){return q!==Et||X===K});var D=w[K],j;x?j=x:j=D?D.fixed:null;var M=z(z(z(z(z(z({},gn,{className:"".concat(r,"-expand-icon-col"),columnType:"EXPAND_COLUMN"}),"title",i),"fixed",j),"className","".concat(r,"-row-expand-icon-cell")),"width",h),"render",function(X,U,ee){var se=s(U,ee),ge=d.has(se),ce=u?u(U):!0,Z=v({prefixCls:r,expanded:ge,expandable:ce,record:U,onExpand:c});return g?a.createElement("span",{onClick:function(pe){return pe.stopPropagation()}},Z):Z});return N.map(function(q,X){var U=q===Et?M:q;return X<p?A(A({},U),{},{fixed:U.fixed||"left"}):U})}return w.filter(function(q){return q!==Et})},[l,w,s,d,v,b,p]),k=a.useMemo(function(){var N=E;return t&&(N=t(N)),N.length||(N=[{render:function(){return null}}]),N},[t,E,b]),R=a.useMemo(function(){return b==="rtl"?Tc(Zn(k)):Zn(k)},[k,b,C]),y=a.useMemo(function(){for(var N=-1,$=R.length-1;$>=0;$-=1){var K=R[$].fixed;if(K==="left"||K===!0){N=$;break}}if(N>=0)for(var D=0;D<=N;D+=1){var j=R[D].fixed;if(j!=="left"&&j!==!0)return!0}var M=R.findIndex(function(U){var ee=U.fixed;return ee==="right"});if(M>=0)for(var q=M;q<R.length;q+=1){var X=R[q].fixed;if(X!=="right")return!0}return!1},[R]),P=Oc(R,C,S),I=Ce(P,2),T=I[0],O=I[1];return[k,T,O,y]}function Dc(e,t,r){var n=xc(e),o=n.expandIcon,l=n.expandedRowKeys,d=n.defaultExpandedRowKeys,i=n.defaultExpandAllRows,s=n.expandedRowRender,c=n.onExpand,v=n.onExpandedRowsChange,u=n.childrenColumnName,m=o||fc,f=u||"children",p=a.useMemo(function(){return s?"row":e.expandable&&e.internalHooks===wn&&e.expandable.__PARENT_RENDER_ICON__||t.some(function(w){return w&&pt(w)==="object"&&w[f]})?"nest":!1},[!!s,t]),b=a.useState(function(){return d||(i?vc(t,r,f):[])}),g=Ce(b,2),h=g[0],x=g[1],C=a.useMemo(function(){return new Set(l||h||[])},[l,h]),S=a.useCallback(function(w){var E=r(w,t.indexOf(w)),k,R=C.has(E);R?(C.delete(E),k=we(C)):k=[].concat(we(C),[E]),x(k),c&&c(!R,w),v&&v(k)},[r,C,t,c,v]);return[n,p,C,m,f,S]}function Mc(e,t,r){var n=e.map(function(o,l){return Sr(l,l,e,t,r)});return qo(function(){return n},[n],function(o,l){return!bn(o,l)})}function Bc(e){var t=a.useRef(e),r=a.useState({}),n=Ce(r,2),o=n[1],l=a.useRef(null),d=a.useRef([]);function i(s){d.current.push(s);var c=Promise.resolve();l.current=c,c.then(function(){if(l.current===c){var v=d.current,u=t.current;d.current=[],v.forEach(function(m){t.current=m(t.current)}),l.current=null,u!==t.current&&o({})}})}return a.useEffect(function(){return function(){l.current=null}},[]),[t.current,i]}function zc(e){var t=a.useRef(null),r=a.useRef();function n(){window.clearTimeout(r.current)}function o(d){t.current=d,n(),r.current=window.setTimeout(function(){t.current=null,r.current=void 0},100)}function l(){return t.current}return a.useEffect(function(){return n},[]),[o,l]}function jc(){var e=a.useState(-1),t=Ce(e,2),r=t[0],n=t[1],o=a.useState(-1),l=Ce(o,2),d=l[0],i=l[1],s=a.useCallback(function(c,v){n(c),i(v)},[]);return[r,d,s]}var uo=bl()?window:null;function _c(e,t){var r=pt(e)==="object"?e:{},n=r.offsetHeader,o=n===void 0?0:n,l=r.offsetSummary,d=l===void 0?0:l,i=r.offsetScroll,s=i===void 0?0:i,c=r.getContainer,v=c===void 0?function(){return uo}:c,u=v()||uo,m=!!e;return a.useMemo(function(){return{isSticky:m,stickyClassName:m?"".concat(t,"-sticky-holder"):"",offsetHeader:o,offsetSummary:d,offsetScroll:s,container:u}},[m,s,o,d,t,u])}function Lc(e,t,r){var n=a.useMemo(function(){var o=t.length,l=function(c,v,u){for(var m=[],f=0,p=c;p!==v;p+=u)m.push(f),t[p].fixed&&(f+=e[p]||0);return m},d=l(0,o,1),i=l(o-1,-1,-1).reverse();return r==="rtl"?{left:i,right:d}:{left:d,right:i}},[e,t,r]);return n}function fo(e){var t=e.className,r=e.children;return a.createElement("div",{className:t},r)}function vo(e){var t=gr(e),r=t.getBoundingClientRect(),n=document.documentElement;return{left:r.left+(window.pageXOffset||n.scrollLeft)-(n.clientLeft||document.body.clientLeft||0),top:r.top+(window.pageYOffset||n.scrollTop)-(n.clientTop||document.body.clientTop||0)}}var Hc=function(t,r){var n,o,l=t.scrollBodyRef,d=t.onScroll,i=t.offsetScroll,s=t.container,c=t.direction,v=rt(dt,"prefixCls"),u=((n=l.current)===null||n===void 0?void 0:n.scrollWidth)||0,m=((o=l.current)===null||o===void 0?void 0:o.clientWidth)||0,f=u&&m*(m/u),p=a.useRef(),b=Bc({scrollLeft:0,isHiddenScrollBar:!0}),g=Ce(b,2),h=g[0],x=g[1],C=a.useRef({delta:0,x:0}),S=a.useState(!1),w=Ce(S,2),E=w[0],k=w[1],R=a.useRef(null);a.useEffect(function(){return function(){Wt.cancel(R.current)}},[]);var y=function(){k(!1)},P=function($){$.persist(),C.current.delta=$.pageX-h.scrollLeft,C.current.x=0,k(!0),$.preventDefault()},I=function($){var K,D=$||((K=window)===null||K===void 0?void 0:K.event),j=D.buttons;if(!E||j===0){E&&k(!1);return}var M=C.current.x+$.pageX-C.current.x-C.current.delta,q=c==="rtl";M=Math.max(q?f-m:0,Math.min(q?0:m-f,M));var X=!q||Math.abs(M)+Math.abs(f)<m;X&&(d({scrollLeft:M/m*(u+2)}),C.current.x=$.pageX)},T=function(){Wt.cancel(R.current),R.current=Wt(function(){if(l.current){var $=vo(l.current).top,K=$+l.current.offsetHeight,D=s===window?document.documentElement.scrollTop+window.innerHeight:vo(s).top+s.clientHeight;K-qr()<=D||$>=D-i?x(function(j){return A(A({},j),{},{isHiddenScrollBar:!0})}):x(function(j){return A(A({},j),{},{isHiddenScrollBar:!1})})}})},O=function($){x(function(K){return A(A({},K),{},{scrollLeft:$/u*m||0})})};return a.useImperativeHandle(r,function(){return{setScrollLeft:O,checkScrollBarVisible:T}}),a.useEffect(function(){var N=Vr(document.body,"mouseup",y,!1),$=Vr(document.body,"mousemove",I,!1);return T(),function(){N.remove(),$.remove()}},[f,E]),a.useEffect(function(){if(l.current){for(var N=[],$=gr(l.current);$;)N.push($),$=$.parentElement;return N.forEach(function(K){return K.addEventListener("scroll",T,!1)}),window.addEventListener("resize",T,!1),window.addEventListener("scroll",T,!1),s.addEventListener("scroll",T,!1),function(){N.forEach(function(K){return K.removeEventListener("scroll",T)}),window.removeEventListener("resize",T),window.removeEventListener("scroll",T),s.removeEventListener("scroll",T)}}},[s]),a.useEffect(function(){h.isHiddenScrollBar||x(function(N){var $=l.current;return $?A(A({},N),{},{scrollLeft:$.scrollLeft/$.scrollWidth*$.clientWidth}):N})},[h.isHiddenScrollBar]),u<=m||!f||h.isHiddenScrollBar?null:a.createElement("div",{style:{height:qr(),width:m,bottom:i},className:"".concat(v,"-sticky-scroll")},a.createElement("div",{onMouseDown:P,ref:p,className:G("".concat(v,"-sticky-scroll-bar"),z({},"".concat(v,"-sticky-scroll-bar-active"),E)),style:{width:"".concat(f,"px"),transform:"translate3d(".concat(h.scrollLeft,"px, 0, 0)")}}))};const Ac=a.forwardRef(Hc);var Ta="rc-table",Fc=[],Wc={};function Vc(){return"No Data"}function qc(e,t){var r=A({rowKey:"key",prefixCls:Ta,emptyText:Vc},e),n=r.prefixCls,o=r.className,l=r.rowClassName,d=r.style,i=r.data,s=r.rowKey,c=r.scroll,v=r.tableLayout,u=r.direction,m=r.title,f=r.footer,p=r.summary,b=r.caption,g=r.id,h=r.showHeader,x=r.components,C=r.emptyText,S=r.onRow,w=r.onHeaderRow,E=r.onScroll,k=r.internalHooks,R=r.transformColumns,y=r.internalRefs,P=r.tailor,I=r.getContainerWidth,T=r.sticky,O=r.rowHoverable,N=O===void 0?!0:O,$=i||Fc,K=!!$.length,D=k===wn,j=a.useCallback(function(Se,Re){return mr(x,Se)||Re},[x]),M=a.useMemo(function(){return typeof s=="function"?s:function(Se){var Re=Se&&Se[s];return Re}},[s]),q=j(["body"]),X=jc(),U=Ce(X,3),ee=U[0],se=U[1],ge=U[2],ce=Dc(r,$,M),Z=Ce(ce,6),te=Z[0],pe=Z[1],ie=Z[2],W=Z[3],V=Z[4],F=Z[5],Q=c==null?void 0:c.x,oe=a.useState(0),Y=Ce(oe,2),ae=Y[0],Oe=Y[1],Ae=Kc(A(A(A({},r),te),{},{expandable:!!te.expandedRowRender,columnTitle:te.columnTitle,expandedKeys:ie,getRowKey:M,onTriggerExpand:F,expandIcon:W,expandIconColumnIndex:te.expandIconColumnIndex,direction:u,scrollWidth:D&&P&&typeof Q=="number"?Q:null,clientWidth:ae}),D?R:null),Ee=Ce(Ae,4),B=Ee[0],H=Ee[1],de=Ee[2],ve=Ee[3],fe=de??Q,Pe=a.useMemo(function(){return{columns:B,flattenColumns:H}},[B,H]),je=a.useRef(),Fe=a.useRef(),xe=a.useRef(),ye=a.useRef();a.useImperativeHandle(t,function(){return{nativeElement:je.current,scrollTo:function(Re){var tt;if(xe.current instanceof HTMLElement){var vt=Re.index,nt=Re.top,Gt=Re.key;if(Zs(nt)){var Ht;(Ht=xe.current)===null||Ht===void 0||Ht.scrollTo({top:nt})}else{var At,fn=Gt??M($[vt]);(At=xe.current.querySelector('[data-row-key="'.concat(fn,'"]')))===null||At===void 0||At.scrollIntoView()}}else(tt=xe.current)!==null&&tt!==void 0&&tt.scrollTo&&xe.current.scrollTo(Re)}}});var re=a.useRef(),_=a.useState(!1),Ie=Ce(_,2),Ke=Ie[0],he=Ie[1],Be=a.useState(!1),Ne=Ce(Be,2),_e=Ne[0],We=Ne[1],Ye=a.useState(new Map),gt=Ce(Ye,2),De=gt[0],Ot=gt[1],It=Tn(H),Qe=It.map(function(Se){return De.get(Se)}),Ge=a.useMemo(function(){return Qe},[Qe.join("_")]),at=Lc(Ge,H,u),Ze=c&&Qn(c.y),Ue=c&&Qn(fe)||!!te.fixed,et=Ue&&H.some(function(Se){var Re=Se.fixed;return Re}),Me=a.useRef(),Le=_c(T,n),lt=Le.isSticky,ln=Le.offsetHeader,sn=Le.offsetSummary,wt=Le.offsetScroll,cn=Le.stickyClassName,me=Le.container,be=a.useMemo(function(){return p==null?void 0:p($)},[p,$]),Te=(Ze||lt)&&a.isValidElement(be)&&be.type===Kn&&be.props.fixed,ze,Je,it;Ze&&(Je={overflowY:K?"scroll":"auto",maxHeight:c.y}),Ue&&(ze={overflowX:"auto"},Ze||(Je={overflowY:"hidden"}),it={width:fe===!0?"auto":fe,minWidth:"100%"});var st=a.useCallback(function(Se,Re){Ot(function(tt){if(tt.get(Se)!==Re){var vt=new Map(tt);return vt.set(Se,Re),vt}return tt})},[]),Xe=zc(),qt=Ce(Xe,2),dn=qt[0],Pt=qt[1];function zt(Se,Re){Re&&(typeof Re=="function"?Re(Se):Re.scrollLeft!==Se&&(Re.scrollLeft=Se,Re.scrollLeft!==Se&&setTimeout(function(){Re.scrollLeft=Se},0)))}var mt=Mt(function(Se){var Re=Se.currentTarget,tt=Se.scrollLeft,vt=u==="rtl",nt=typeof tt=="number"?tt:Re.scrollLeft,Gt=Re||Wc;if(!Pt()||Pt()===Gt){var Ht;dn(Gt),zt(nt,Fe.current),zt(nt,xe.current),zt(nt,re.current),zt(nt,(Ht=Me.current)===null||Ht===void 0?void 0:Ht.setScrollLeft)}var At=Re||Fe.current;if(At){var fn=D&&P&&typeof fe=="number"?fe:At.scrollWidth,Ln=At.clientWidth;if(fn===Ln){he(!1),We(!1);return}vt?(he(-nt<fn-Ln),We(-nt>0)):(he(nt>0),We(nt<fn-Ln))}}),un=Mt(function(Se){mt(Se),E==null||E(Se)}),jt=function(){if(Ue&&xe.current){var Re;mt({currentTarget:gr(xe.current),scrollLeft:(Re=xe.current)===null||Re===void 0?void 0:Re.scrollLeft})}else he(!1),We(!1)},_t=function(Re){var tt,vt=Re.width;(tt=Me.current)===null||tt===void 0||tt.checkScrollBarVisible();var nt=je.current?je.current.offsetWidth:vt;D&&I&&je.current&&(nt=I(je.current,nt)||nt),nt!==ae&&(jt(),Oe(nt))},Xt=a.useRef(!1);a.useEffect(function(){Xt.current&&jt()},[Ue,i,B.length]),a.useEffect(function(){Xt.current=!0},[]);var Mn=a.useState(0),ue=Ce(Mn,2),ke=ue[0],He=ue[1],ht=a.useState(!0),Lt=Ce(ht,2),Ir=Lt[0],Xa=Lt[1];xt(function(){(!P||!D)&&(xe.current instanceof Element?He(Xr(xe.current).width):He(Xr(ye.current).width)),Xa(yl("position","sticky"))},[]),a.useEffect(function(){D&&y&&(y.body.current=xe.current)});var Ga=a.useCallback(function(Se){return a.createElement(a.Fragment,null,a.createElement(so,Se),Te==="top"&&a.createElement(Nn,Se,be))},[Te,be]),Ua=a.useCallback(function(Se){return a.createElement(Nn,Se,be)},[be]),Pr=j(["table"],"table"),$n=a.useMemo(function(){return v||(et?fe==="max-content"?"auto":"fixed":Ze||lt||H.some(function(Se){var Re=Se.ellipsis;return Re})?"fixed":"auto")},[Ze,et,H,v,lt]),Bn,zn={colWidths:Ge,columCount:H.length,stickyOffsets:at,onHeaderRow:w,fixHeader:Ze,scroll:c},Tr=a.useMemo(function(){return K?null:typeof C=="function"?C():C},[K,C]),Kr=a.createElement(yc,{data:$,measureColumnWidth:Ze||Ue||lt}),Dr=a.createElement(Ia,{colWidths:H.map(function(Se){var Re=Se.width;return Re}),columns:H}),Mr=b!=null?a.createElement("caption",{className:"".concat(n,"-caption")},b):void 0,Ja=kt(r,{data:!0}),Br=kt(r,{aria:!0});if(Ze||lt){var jn;typeof q=="function"?(jn=q($,{scrollbarSize:ke,ref:xe,onScroll:mt}),zn.colWidths=H.map(function(Se,Re){var tt=Se.width,vt=Re===H.length-1?tt-ke:tt;return typeof vt=="number"&&!Number.isNaN(vt)?vt:0})):jn=a.createElement("div",{style:A(A({},ze),Je),onScroll:un,ref:xe,className:G("".concat(n,"-body"))},a.createElement(Pr,le({style:A(A({},it),{},{tableLayout:$n})},Br),Mr,Dr,Kr,!Te&&be&&a.createElement(Nn,{stickyOffsets:at,flattenColumns:H},be)));var zr=A(A(A({noData:!$.length,maxContentScroll:Ue&&fe==="max-content"},zn),Pe),{},{direction:u,stickyClassName:cn,onScroll:mt});Bn=a.createElement(a.Fragment,null,h!==!1&&a.createElement(io,le({},zr,{stickyTopOffset:ln,className:"".concat(n,"-header"),ref:Fe}),Ga),jn,Te&&Te!=="top"&&a.createElement(io,le({},zr,{stickyBottomOffset:sn,className:"".concat(n,"-summary"),ref:re}),Ua),lt&&xe.current&&xe.current instanceof Element&&a.createElement(Ac,{ref:Me,offsetScroll:wt,scrollBodyRef:xe,onScroll:mt,container:me,direction:u}))}else Bn=a.createElement("div",{style:A(A({},ze),Je),className:G("".concat(n,"-content")),onScroll:mt,ref:xe},a.createElement(Pr,le({style:A(A({},it),{},{tableLayout:$n})},Br),Mr,Dr,h!==!1&&a.createElement(so,le({},zn,Pe)),Kr,be&&a.createElement(Nn,{stickyOffsets:at,flattenColumns:H},be)));var _n=a.createElement("div",le({className:G(n,o,z(z(z(z(z(z(z(z(z(z({},"".concat(n,"-rtl"),u==="rtl"),"".concat(n,"-ping-left"),Ke),"".concat(n,"-ping-right"),_e),"".concat(n,"-layout-fixed"),v==="fixed"),"".concat(n,"-fixed-header"),Ze),"".concat(n,"-fixed-column"),et),"".concat(n,"-fixed-column-gapped"),et&&ve),"".concat(n,"-scroll-horizontal"),Ue),"".concat(n,"-has-fix-left"),H[0]&&H[0].fixed),"".concat(n,"-has-fix-right"),H[H.length-1]&&H[H.length-1].fixed==="right")),style:d,id:g,ref:je},Ja),m&&a.createElement(fo,{className:"".concat(n,"-title")},m($)),a.createElement("div",{ref:ye,className:"".concat(n,"-container")},Bn),f&&a.createElement(fo,{className:"".concat(n,"-footer")},f($)));Ue&&(_n=a.createElement(pr,{onResize:_t},_n));var jr=Mc(H,at,u),Ya=a.useMemo(function(){return{scrollX:fe,prefixCls:n,getComponent:j,scrollbarSize:ke,direction:u,fixedInfoList:jr,isSticky:lt,supportSticky:Ir,componentWidth:ae,fixHeader:Ze,fixColumn:et,horizonScroll:Ue,tableLayout:$n,rowClassName:l,expandedRowClassName:te.expandedRowClassName,expandIcon:W,expandableType:pe,expandRowByClick:te.expandRowByClick,expandedRowRender:te.expandedRowRender,expandedRowOffset:te.expandedRowOffset,onTriggerExpand:F,expandIconColumnIndex:te.expandIconColumnIndex,indentSize:te.indentSize,allColumnsFixedLeft:H.every(function(Se){return Se.fixed==="left"}),emptyNode:Tr,columns:B,flattenColumns:H,onColumnResize:st,colWidths:Ge,hoverStartRow:ee,hoverEndRow:se,onHover:ge,rowExpandable:te.rowExpandable,onRow:S,getRowKey:M,expandedKeys:ie,childrenColumnName:V,rowHoverable:N}},[fe,n,j,ke,u,jr,lt,Ir,ae,Ze,et,Ue,$n,l,te.expandedRowClassName,W,pe,te.expandRowByClick,te.expandedRowRender,te.expandedRowOffset,F,te.expandIconColumnIndex,te.indentSize,Tr,B,H,st,Ge,ee,se,ge,te.rowExpandable,S,M,ie,V,N]);return a.createElement(dt.Provider,{value:Ya},_n)}var Xc=a.forwardRef(qc);function Ka(e){return Ca(Xc,e)}var on=Ka();on.EXPAND_COLUMN=Et;on.INTERNAL_HOOKS=wn;on.Column=dc;on.ColumnGroup=uc;on.Summary=wa;var $r=Cr(null),Da=Cr(null);function Gc(e,t,r){var n=t||1;return r[e+n]-(r[e]||0)}function Uc(e){var t=e.rowInfo,r=e.column,n=e.colIndex,o=e.indent,l=e.index,d=e.component,i=e.renderIndex,s=e.record,c=e.style,v=e.className,u=e.inverse,m=e.getHeight,f=r.render,p=r.dataIndex,b=r.className,g=r.width,h=rt(Da,["columnsOffset"]),x=h.columnsOffset,C=Oa(t,r,n,o,l),S=C.key,w=C.fixedInfo,E=C.appendCellNode,k=C.additionalCellProps,R=k.style,y=k.colSpan,P=y===void 0?1:y,I=k.rowSpan,T=I===void 0?1:I,O=n-1,N=Gc(O,P,x),$=P>1?g-N:0,K=A(A(A({},R),c),{},{flex:"0 0 ".concat(N,"px"),width:"".concat(N,"px"),marginRight:$,pointerEvents:"auto"}),D=a.useMemo(function(){return u?T<=1:P===0||T===0||T>1},[T,P,u]);D?K.visibility="hidden":u&&(K.height=m==null?void 0:m(T));var j=D?function(){return null}:f,M={};return(T===0||P===0)&&(M.rowSpan=1,M.colSpan=1),a.createElement(rn,le({className:G(b,v),ellipsis:r.ellipsis,align:r.align,scope:r.rowScope,component:d,prefixCls:t.prefixCls,key:S,record:s,index:l,renderIndex:i,dataIndex:p,render:j,shouldCellUpdate:r.shouldCellUpdate},w,{appendNode:E,additionalProps:A(A({},k),{},{style:K},M)}))}var Jc=["data","index","className","rowKey","style","extra","getHeight"],Yc=a.forwardRef(function(e,t){var r=e.data,n=e.index,o=e.className,l=e.rowKey,d=e.style,i=e.extra,s=e.getHeight,c=ft(e,Jc),v=r.record,u=r.indent,m=r.index,f=rt(dt,["prefixCls","flattenColumns","fixColumn","componentWidth","scrollX"]),p=f.scrollX,b=f.flattenColumns,g=f.prefixCls,h=f.fixColumn,x=f.componentWidth,C=rt($r,["getComponent"]),S=C.getComponent,w=Na(v,l,n,u),E=S(["body","row"],"div"),k=S(["body","cell"],"div"),R=w.rowSupportExpand,y=w.expanded,P=w.rowProps,I=w.expandedRowRender,T=w.expandedRowClassName,O;if(R&&y){var N=I(v,n,u+1,y),$=Ra(T,v,n,u),K={};h&&(K={style:z({},"--virtual-width","".concat(x,"px"))});var D="".concat(g,"-expanded-row-cell");O=a.createElement(E,{className:G("".concat(g,"-expanded-row"),"".concat(g,"-expanded-row-level-").concat(u+1),$)},a.createElement(rn,{component:k,prefixCls:g,className:G(D,z({},"".concat(D,"-fixed"),h)),additionalProps:K},N))}var j=A(A({},d),{},{width:p});i&&(j.position="absolute",j.pointerEvents="none");var M=a.createElement(E,le({},P,c,{"data-row-key":l,ref:R?null:t,className:G(o,"".concat(g,"-row"),P==null?void 0:P.className,z({},"".concat(g,"-row-extra"),i)),style:A(A({},j),P==null?void 0:P.style)}),b.map(function(q,X){return a.createElement(Uc,{key:X,component:k,rowInfo:w,column:q,colIndex:X,indent:u,index:n,renderIndex:m,record:v,inverse:i,getHeight:s})}));return R?a.createElement("div",{ref:t},M,O):M}),mo=nn(Yc),Qc=a.forwardRef(function(e,t){var r=e.data,n=e.onScroll,o=rt(dt,["flattenColumns","onColumnResize","getRowKey","prefixCls","expandedKeys","childrenColumnName","scrollX","direction"]),l=o.flattenColumns,d=o.onColumnResize,i=o.getRowKey,s=o.expandedKeys,c=o.prefixCls,v=o.childrenColumnName,u=o.scrollX,m=o.direction,f=rt($r),p=f.sticky,b=f.scrollY,g=f.listItemHeight,h=f.getComponent,x=f.onScroll,C=a.useRef(),S=Ea(r,v,s,i),w=a.useMemo(function(){var O=0;return l.map(function(N){var $=N.width,K=N.key;return O+=$,[K,$,O]})},[l]),E=a.useMemo(function(){return w.map(function(O){return O[2]})},[w]);a.useEffect(function(){w.forEach(function(O){var N=Ce(O,2),$=N[0],K=N[1];d($,K)})},[w]),a.useImperativeHandle(t,function(){var O,N={scrollTo:function(K){var D;(D=C.current)===null||D===void 0||D.scrollTo(K)},nativeElement:(O=C.current)===null||O===void 0?void 0:O.nativeElement};return Object.defineProperty(N,"scrollLeft",{get:function(){var K;return((K=C.current)===null||K===void 0?void 0:K.getScrollInfo().x)||0},set:function(K){var D;(D=C.current)===null||D===void 0||D.scrollTo({left:K})}}),N});var k=function(N,$){var K,D=(K=S[$])===null||K===void 0?void 0:K.record,j=N.onCell;if(j){var M,q=j(D,$);return(M=q==null?void 0:q.rowSpan)!==null&&M!==void 0?M:1}return 1},R=function(N){var $=N.start,K=N.end,D=N.getSize,j=N.offsetY;if(K<0)return null;for(var M=l.filter(function(W){return k(W,$)===0}),q=$,X=function(V){if(M=M.filter(function(F){return k(F,V)===0}),!M.length)return q=V,1},U=$;U>=0&&!X(U);U-=1);for(var ee=l.filter(function(W){return k(W,K)!==1}),se=K,ge=function(V){if(ee=ee.filter(function(F){return k(F,V)!==1}),!ee.length)return se=Math.max(V-1,K),1},ce=K;ce<S.length&&!ge(ce);ce+=1);for(var Z=[],te=function(V){var F=S[V];if(!F)return 1;l.some(function(Q){return k(Q,V)>1})&&Z.push(V)},pe=q;pe<=se;pe+=1)te(pe);var ie=Z.map(function(W){var V=S[W],F=i(V.record,W),Q=function(ae){var Oe=W+ae-1,Ae=i(S[Oe].record,Oe),Ee=D(F,Ae);return Ee.bottom-Ee.top},oe=D(F);return a.createElement(mo,{key:W,data:V,rowKey:F,index:W,style:{top:-j+oe.top},extra:!0,getHeight:Q})});return ie},y=a.useMemo(function(){return{columnsOffset:E}},[E]),P="".concat(c,"-tbody"),I=h(["body","wrapper"]),T={};return p&&(T.position="sticky",T.bottom=0,pt(p)==="object"&&p.offsetScroll&&(T.bottom=p.offsetScroll)),a.createElement(Da.Provider,{value:y},a.createElement(Xo,{fullHeight:!1,ref:C,prefixCls:"".concat(P,"-virtual"),styles:{horizontalScrollBar:T},className:P,height:b,itemHeight:g||24,data:S,itemKey:function(N){return i(N.record)},component:I,scrollWidth:u,direction:m,onVirtualScroll:function(N){var $,K=N.x;n({currentTarget:($=C.current)===null||$===void 0?void 0:$.nativeElement,scrollLeft:K})},onScroll:x,extraRender:R},function(O,N,$){var K=i(O.record,N);return a.createElement(mo,{data:O,rowKey:K,index:N,style:$.style})}))}),Zc=nn(Qc),ed=function(t,r){var n=r.ref,o=r.onScroll;return a.createElement(Zc,{ref:n,data:t,onScroll:o})};function td(e,t){var r=e.data,n=e.columns,o=e.scroll,l=e.sticky,d=e.prefixCls,i=d===void 0?Ta:d,s=e.className,c=e.listItemHeight,v=e.components,u=e.onScroll,m=o||{},f=m.x,p=m.y;typeof f!="number"&&(f=1),typeof p!="number"&&(p=500);var b=Mt(function(x,C){return mr(v,x)||C}),g=Mt(u),h=a.useMemo(function(){return{sticky:l,scrollY:p,listItemHeight:c,getComponent:b,onScroll:g}},[l,p,c,b,g]);return a.createElement($r.Provider,{value:h},a.createElement(on,le({},e,{className:G(s,"".concat(i,"-virtual")),scroll:A(A({},o),{},{x:f}),components:A(A({},v),{},{body:r!=null&&r.length?ed:void 0}),columns:n,internalHooks:wn,tailor:!0,ref:t})))}var nd=a.forwardRef(td);function Ma(e){return Ca(nd,e)}Ma();const rd=e=>null,od=e=>null;var Er=a.createContext(null),ad=a.createContext({}),ld=function(t){for(var r=t.prefixCls,n=t.level,o=t.isStart,l=t.isEnd,d="".concat(r,"-indent-unit"),i=[],s=0;s<n;s+=1)i.push(a.createElement("span",{key:s,className:G(d,z(z({},"".concat(d,"-start"),o[s]),"".concat(d,"-end"),l[s]))}));return a.createElement("span",{"aria-hidden":"true",className:"".concat(r,"-indent")},i)};const id=a.memo(ld);var sd=["eventKey","className","style","dragOver","dragOverGapTop","dragOverGapBottom","isLeaf","isStart","isEnd","expanded","selected","checked","halfChecked","loading","domRef","active","data","onMouseMove","selectable"],po="open",go="close",cd="---",yn=function(t){var r,n,o,l=t.eventKey,d=t.className,i=t.style,s=t.dragOver,c=t.dragOverGapTop,v=t.dragOverGapBottom,u=t.isLeaf,m=t.isStart,f=t.isEnd,p=t.expanded,b=t.selected,g=t.checked,h=t.halfChecked,x=t.loading,C=t.domRef,S=t.active,w=t.data,E=t.onMouseMove,k=t.selectable,R=ft(t,sd),y=J.useContext(Er),P=J.useContext(ad),I=J.useRef(null),T=J.useState(!1),O=Ce(T,2),N=O[0],$=O[1],K=!!(y.disabled||t.disabled||(r=P.nodeDisabled)!==null&&r!==void 0&&r.call(P,w)),D=J.useMemo(function(){return!y.checkable||t.checkable===!1?!1:y.checkable},[y.checkable,t.checkable]),j=function(_){K||y.onNodeSelect(_,Ve(t))},M=function(_){K||!D||t.disableCheckbox||y.onNodeCheck(_,Ve(t),!g)},q=J.useMemo(function(){return typeof k=="boolean"?k:y.selectable},[k,y.selectable]),X=function(_){y.onNodeClick(_,Ve(t)),q?j(_):M(_)},U=function(_){y.onNodeDoubleClick(_,Ve(t))},ee=function(_){y.onNodeMouseEnter(_,Ve(t))},se=function(_){y.onNodeMouseLeave(_,Ve(t))},ge=function(_){y.onNodeContextMenu(_,Ve(t))},ce=J.useMemo(function(){return!!(y.draggable&&(!y.draggable.nodeDraggable||y.draggable.nodeDraggable(w)))},[y.draggable,w]),Z=function(_){_.stopPropagation(),$(!0),y.onNodeDragStart(_,t);try{_.dataTransfer.setData("text/plain","")}catch{}},te=function(_){_.preventDefault(),_.stopPropagation(),y.onNodeDragEnter(_,t)},pe=function(_){_.preventDefault(),_.stopPropagation(),y.onNodeDragOver(_,t)},ie=function(_){_.stopPropagation(),y.onNodeDragLeave(_,t)},W=function(_){_.stopPropagation(),$(!1),y.onNodeDragEnd(_,t)},V=function(_){_.preventDefault(),_.stopPropagation(),$(!1),y.onNodeDrop(_,t)},F=function(_){x||y.onNodeExpand(_,Ve(t))},Q=J.useMemo(function(){var re=ut(y.keyEntities,l)||{},_=re.children;return!!(_||[]).length},[y.keyEntities,l]),oe=J.useMemo(function(){return u===!1?!1:u||!y.loadData&&!Q||y.loadData&&t.loaded&&!Q},[u,y.loadData,Q,t.loaded]);J.useEffect(function(){x||typeof y.loadData=="function"&&p&&!oe&&!t.loaded&&y.onNodeLoad(Ve(t))},[x,y.loadData,y.onNodeLoad,p,oe,t]);var Y=J.useMemo(function(){var re;return(re=y.draggable)!==null&&re!==void 0&&re.icon?J.createElement("span",{className:"".concat(y.prefixCls,"-draggable-icon")},y.draggable.icon):null},[y.draggable]),ae=function(_){var Ie=t.switcherIcon||y.switcherIcon;return typeof Ie=="function"?Ie(A(A({},t),{},{isLeaf:_})):Ie},Oe=function(){if(oe){var _=ae(!0);return _!==!1?J.createElement("span",{className:G("".concat(y.prefixCls,"-switcher"),"".concat(y.prefixCls,"-switcher-noop"))},_):null}var Ie=ae(!1);return Ie!==!1?J.createElement("span",{onClick:F,className:G("".concat(y.prefixCls,"-switcher"),"".concat(y.prefixCls,"-switcher_").concat(p?po:go))},Ie):null},Ae=J.useMemo(function(){if(!D)return null;var re=typeof D!="boolean"?D:null;return J.createElement("span",{className:G("".concat(y.prefixCls,"-checkbox"),z(z(z({},"".concat(y.prefixCls,"-checkbox-checked"),g),"".concat(y.prefixCls,"-checkbox-indeterminate"),!g&&h),"".concat(y.prefixCls,"-checkbox-disabled"),K||t.disableCheckbox)),onClick:M,role:"checkbox","aria-checked":h?"mixed":g,"aria-disabled":K||t.disableCheckbox,"aria-label":"Select ".concat(typeof t.title=="string"?t.title:"tree node")},re)},[D,g,h,K,t.disableCheckbox,t.title]),Ee=J.useMemo(function(){return oe?null:p?po:go},[oe,p]),B=J.useMemo(function(){return J.createElement("span",{className:G("".concat(y.prefixCls,"-iconEle"),"".concat(y.prefixCls,"-icon__").concat(Ee||"docu"),z({},"".concat(y.prefixCls,"-icon_loading"),x))})},[y.prefixCls,Ee,x]),H=J.useMemo(function(){var re=!!y.draggable,_=!t.disabled&&re&&y.dragOverNodeKey===l;return _?y.dropIndicatorRender({dropPosition:y.dropPosition,dropLevelOffset:y.dropLevelOffset,indent:y.indent,prefixCls:y.prefixCls,direction:y.direction}):null},[y.dropPosition,y.dropLevelOffset,y.indent,y.prefixCls,y.direction,y.draggable,y.dragOverNodeKey,y.dropIndicatorRender]),de=J.useMemo(function(){var re=t.title,_=re===void 0?cd:re,Ie="".concat(y.prefixCls,"-node-content-wrapper"),Ke;if(y.showIcon){var he=t.icon||y.icon;Ke=he?J.createElement("span",{className:G("".concat(y.prefixCls,"-iconEle"),"".concat(y.prefixCls,"-icon__customize"))},typeof he=="function"?he(t):he):B}else y.loadData&&x&&(Ke=B);var Be;return typeof _=="function"?Be=_(w):y.titleRender?Be=y.titleRender(w):Be=_,J.createElement("span",{ref:I,title:typeof _=="string"?_:"",className:G(Ie,"".concat(Ie,"-").concat(Ee||"normal"),z({},"".concat(y.prefixCls,"-node-selected"),!K&&(b||N))),onMouseEnter:ee,onMouseLeave:se,onContextMenu:ge,onClick:X,onDoubleClick:U},Ke,J.createElement("span",{className:"".concat(y.prefixCls,"-title")},Be),H)},[y.prefixCls,y.showIcon,t,y.icon,B,y.titleRender,w,Ee,ee,se,ge,X,U]),ve=kt(R,{aria:!0,data:!0}),fe=ut(y.keyEntities,l)||{},Pe=fe.level,je=f[f.length-1],Fe=!K&&ce,xe=y.draggingNodeKey===l,ye=k!==void 0?{"aria-selected":!!k}:void 0;return J.createElement("div",le({ref:C,role:"treeitem","aria-expanded":u?void 0:p,className:G(d,"".concat(y.prefixCls,"-treenode"),(o={},z(z(z(z(z(z(z(z(z(z(o,"".concat(y.prefixCls,"-treenode-disabled"),K),"".concat(y.prefixCls,"-treenode-switcher-").concat(p?"open":"close"),!u),"".concat(y.prefixCls,"-treenode-checkbox-checked"),g),"".concat(y.prefixCls,"-treenode-checkbox-indeterminate"),h),"".concat(y.prefixCls,"-treenode-selected"),b),"".concat(y.prefixCls,"-treenode-loading"),x),"".concat(y.prefixCls,"-treenode-active"),S),"".concat(y.prefixCls,"-treenode-leaf-last"),je),"".concat(y.prefixCls,"-treenode-draggable"),ce),"dragging",xe),z(z(z(z(z(z(z(o,"drop-target",y.dropTargetKey===l),"drop-container",y.dropContainerKey===l),"drag-over",!K&&s),"drag-over-gap-top",!K&&c),"drag-over-gap-bottom",!K&&v),"filter-node",(n=y.filterTreeNode)===null||n===void 0?void 0:n.call(y,Ve(t))),"".concat(y.prefixCls,"-treenode-leaf"),oe))),style:i,draggable:Fe,onDragStart:Fe?Z:void 0,onDragEnter:ce?te:void 0,onDragOver:ce?pe:void 0,onDragLeave:ce?ie:void 0,onDrop:ce?V:void 0,onDragEnd:ce?W:void 0,onMouseMove:E},ye,ve),J.createElement(id,{prefixCls:y.prefixCls,level:Pe,isStart:m,isEnd:f}),Y,Oe(),Ae,de)};yn.isTreeNode=1;function bt(e,t){if(!e)return[];var r=e.slice(),n=r.indexOf(t);return n>=0&&r.splice(n,1),r}function $t(e,t){var r=(e||[]).slice();return r.indexOf(t)===-1&&r.push(t),r}function Nr(e){return e.split("-")}function dd(e,t){var r=[],n=ut(t,e);function o(){var l=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];l.forEach(function(d){var i=d.key,s=d.children;r.push(i),o(s)})}return o(n.children),r}function ud(e){if(e.parent){var t=Nr(e.pos);return Number(t[t.length-1])===e.parent.children.length-1}return!1}function fd(e){var t=Nr(e.pos);return Number(t[t.length-1])===0}function ho(e,t,r,n,o,l,d,i,s,c){var v,u=e.clientX,m=e.clientY,f=e.target.getBoundingClientRect(),p=f.top,b=f.height,g=(c==="rtl"?-1:1)*(((o==null?void 0:o.x)||0)-u),h=(g-12)/n,x=s.filter(function(K){var D;return(D=i[K])===null||D===void 0||(D=D.children)===null||D===void 0?void 0:D.length}),C=ut(i,r.eventKey);if(m<p+b/2){var S=d.findIndex(function(K){return K.key===C.key}),w=S<=0?0:S-1,E=d[w].key;C=ut(i,E)}var k=C.key,R=C,y=C.key,P=0,I=0;if(!x.includes(k))for(var T=0;T<h&&ud(C);T+=1)C=C.parent,I+=1;var O=t.data,N=C.node,$=!0;return fd(C)&&C.level===0&&m<p+b/2&&l({dragNode:O,dropNode:N,dropPosition:-1})&&C.key===r.eventKey?P=-1:(R.children||[]).length&&x.includes(y)?l({dragNode:O,dropNode:N,dropPosition:0})?P=0:$=!1:I===0?h>-1.5?l({dragNode:O,dropNode:N,dropPosition:1})?P=1:$=!1:l({dragNode:O,dropNode:N,dropPosition:0})?P=0:l({dragNode:O,dropNode:N,dropPosition:1})?P=1:$=!1:l({dragNode:O,dropNode:N,dropPosition:1})?P=1:$=!1,{dropPosition:P,dropLevelOffset:I,dropTargetKey:C.key,dropTargetPos:C.pos,dragOverNodeKey:y,dropContainerKey:P===0?null:((v=C.parent)===null||v===void 0?void 0:v.key)||null,dropAllowed:$}}function bo(e,t){if(e){var r=t.multiple;return r?e.slice():e.length?[e[0]]:e}}function Fn(e){if(!e)return null;var t;if(Array.isArray(e))t={checkedKeys:e,halfCheckedKeys:void 0};else if(pt(e)==="object")t={checkedKeys:e.checked||void 0,halfCheckedKeys:e.halfChecked||void 0};else return Nt(!1,"`checkedKeys` is not an array or an object"),null;return t}function er(e,t){var r=new Set;function n(o){if(!r.has(o)){var l=ut(t,o);if(l){r.add(o);var d=l.parent,i=l.node;i.disabled||d&&n(d.key)}}}return(e||[]).forEach(function(o){n(o)}),we(r)}function vd(e){const[t,r]=a.useState(null);return[a.useCallback((l,d,i)=>{const s=t??l,c=Math.min(s||0,l),v=Math.max(s||0,l),u=d.slice(c,v+1).map(p=>e(p)),m=u.some(p=>!i.has(p)),f=[];return u.forEach(p=>{m?(i.has(p)||f.push(p),i.add(p)):(i.delete(p),f.push(p))}),r(m?v:null),f},[t]),l=>{r(l)}]}const Kt={},tr="SELECT_ALL",nr="SELECT_INVERT",rr="SELECT_NONE",yo=[],Ba=(e,t)=>{let r=[];return(t||[]).forEach(n=>{r.push(n),n&&typeof n=="object"&&e in n&&(r=[].concat(we(r),we(Ba(e,n[e]))))}),r},md=(e,t)=>{const{preserveSelectedRowKeys:r,selectedRowKeys:n,defaultSelectedRowKeys:o,getCheckboxProps:l,onChange:d,onSelect:i,onSelectAll:s,onSelectInvert:c,onSelectNone:v,onSelectMultiple:u,columnWidth:m,type:f,selections:p,fixed:b,renderCell:g,hideSelectAll:h,checkStrictly:x=!0}=t||{},{prefixCls:C,data:S,pageData:w,getRecordByKey:E,getRowKey:k,expandType:R,childrenColumnName:y,locale:P,getPopupContainer:I}=e,T=hr(),[O,N]=vd(W=>W),[$,K]=hn(n||o||yo,{value:n}),D=a.useRef(new Map),j=a.useCallback(W=>{if(r){const V=new Map;W.forEach(F=>{let Q=E(F);!Q&&D.current.has(F)&&(Q=D.current.get(F)),V.set(F,Q)}),D.current=V}},[E,r]);a.useEffect(()=>{j($)},[$]);const M=a.useMemo(()=>Ba(y,w),[y,w]),{keyEntities:q}=a.useMemo(()=>{if(x)return{keyEntities:null};let W=S;if(r){const V=new Set(M.map((Q,oe)=>k(Q,oe))),F=Array.from(D.current).reduce((Q,[oe,Y])=>V.has(oe)?Q:Q.concat(Y),[]);W=[].concat(we(W),we(F))}return yr(W,{externalGetKey:k,childrenPropName:y})},[S,k,x,y,r,M]),X=a.useMemo(()=>{const W=new Map;return M.forEach((V,F)=>{const Q=k(V,F),oe=(l?l(V):null)||{};W.set(Q,oe)}),W},[M,k,l]),U=a.useCallback(W=>{const V=k(W);let F;return X.has(V)?F=X.get(k(W)):F=l?l(W):void 0,!!(F!=null&&F.disabled)},[X,k]),[ee,se]=a.useMemo(()=>{if(x)return[$||[],[]];const{checkedKeys:W,halfCheckedKeys:V}=Ut($,!0,q,U);return[W||[],V]},[$,x,q,U]),ge=a.useMemo(()=>{const W=f==="radio"?ee.slice(0,1):ee;return new Set(W)},[ee,f]),ce=a.useMemo(()=>f==="radio"?new Set:new Set(se),[se,f]);a.useEffect(()=>{t||K(yo)},[!!t]);const Z=a.useCallback((W,V)=>{let F,Q;j(W),r?(F=W,Q=W.map(oe=>D.current.get(oe))):(F=[],Q=[],W.forEach(oe=>{const Y=E(oe);Y!==void 0&&(F.push(oe),Q.push(Y))})),K(F),d==null||d(F,Q,{type:V})},[K,E,d,r]),te=a.useCallback((W,V,F,Q)=>{if(i){const oe=F.map(Y=>E(Y));i(E(W),V,oe,Q)}Z(F,"single")},[i,E,Z]),pe=a.useMemo(()=>!p||h?null:(p===!0?[tr,nr,rr]:p).map(V=>V===tr?{key:"all",text:P.selectionAll,onSelect(){Z(S.map((F,Q)=>k(F,Q)).filter(F=>{const Q=X.get(F);return!(Q!=null&&Q.disabled)||ge.has(F)}),"all")}}:V===nr?{key:"invert",text:P.selectInvert,onSelect(){const F=new Set(ge);w.forEach((oe,Y)=>{const ae=k(oe,Y),Oe=X.get(ae);Oe!=null&&Oe.disabled||(F.has(ae)?F.delete(ae):F.add(ae))});const Q=Array.from(F);c&&(T.deprecated(!1,"onSelectInvert","onChange"),c(Q)),Z(Q,"invert")}}:V===rr?{key:"none",text:P.selectNone,onSelect(){v==null||v(),Z(Array.from(ge).filter(F=>{const Q=X.get(F);return Q==null?void 0:Q.disabled}),"none")}}:V).map(V=>Object.assign(Object.assign({},V),{onSelect:(...F)=>{var Q,oe;(oe=V.onSelect)===null||oe===void 0||(Q=oe).call.apply(Q,[V].concat(F)),N(null)}})),[p,ge,w,k,c,Z]);return[a.useCallback(W=>{var V;if(!t)return W.filter(ye=>ye!==Kt);let F=we(W);const Q=new Set(ge),oe=M.map(k).filter(ye=>!X.get(ye).disabled),Y=oe.every(ye=>Q.has(ye)),ae=oe.some(ye=>Q.has(ye)),Oe=()=>{const ye=[];Y?oe.forEach(_=>{Q.delete(_),ye.push(_)}):oe.forEach(_=>{Q.has(_)||(Q.add(_),ye.push(_))});const re=Array.from(Q);s==null||s(!Y,re.map(_=>E(_)),ye.map(_=>E(_))),Z(re,"all"),N(null)};let Ae,Ee;if(f!=="radio"){let ye;if(pe){const he={getPopupContainer:I,items:pe.map((Be,Ne)=>{const{key:_e,text:We,onSelect:Ye}=Be;return{key:_e??Ne,onClick:()=>{Ye==null||Ye(oe)},label:We}})};ye=a.createElement("div",{className:`${C}-selection-extra`},a.createElement(br,{menu:he,getPopupContainer:I},a.createElement("span",null,a.createElement(Cl,null))))}const re=M.map((he,Be)=>{const Ne=k(he,Be),_e=X.get(Ne)||{};return Object.assign({checked:Q.has(Ne)},_e)}).filter(({disabled:he})=>he),_=!!re.length&&re.length===M.length,Ie=_&&re.every(({checked:he})=>he),Ke=_&&re.some(({checked:he})=>he);Ee=a.createElement(Zt,{checked:_?Ie:!!M.length&&Y,indeterminate:_?!Ie&&Ke:!Y&&ae,onChange:Oe,disabled:M.length===0||_,"aria-label":ye?"Custom selection":"Select all",skipGroup:!0}),Ae=!h&&a.createElement("div",{className:`${C}-selection`},Ee,ye)}let B;f==="radio"?B=(ye,re,_)=>{const Ie=k(re,_),Ke=Q.has(Ie),he=X.get(Ie);return{node:a.createElement(xn,Object.assign({},he,{checked:Ke,onClick:Be=>{var Ne;Be.stopPropagation(),(Ne=he==null?void 0:he.onClick)===null||Ne===void 0||Ne.call(he,Be)},onChange:Be=>{var Ne;Q.has(Ie)||te(Ie,!0,[Ie],Be.nativeEvent),(Ne=he==null?void 0:he.onChange)===null||Ne===void 0||Ne.call(he,Be)}})),checked:Ke}}:B=(ye,re,_)=>{var Ie;const Ke=k(re,_),he=Q.has(Ke),Be=ce.has(Ke),Ne=X.get(Ke);let _e;return R==="nest"?_e=Be:_e=(Ie=Ne==null?void 0:Ne.indeterminate)!==null&&Ie!==void 0?Ie:Be,{node:a.createElement(Zt,Object.assign({},Ne,{indeterminate:_e,checked:he,skipGroup:!0,onClick:We=>{var Ye;We.stopPropagation(),(Ye=Ne==null?void 0:Ne.onClick)===null||Ye===void 0||Ye.call(Ne,We)},onChange:We=>{var Ye;const{nativeEvent:gt}=We,{shiftKey:De}=gt,Ot=oe.findIndex(Qe=>Qe===Ke),It=ee.some(Qe=>oe.includes(Qe));if(De&&x&&It){const Qe=O(Ot,oe,Q),Ge=Array.from(Q);u==null||u(!he,Ge.map(at=>E(at)),Qe.map(at=>E(at))),Z(Ge,"multiple")}else{const Qe=ee;if(x){const Ge=he?bt(Qe,Ke):$t(Qe,Ke);te(Ke,!he,Ge,gt)}else{const Ge=Ut([].concat(we(Qe),[Ke]),!0,q,U),{checkedKeys:at,halfCheckedKeys:Ze}=Ge;let Ue=at;if(he){const et=new Set(at);et.delete(Ke),Ue=Ut(Array.from(et),{halfCheckedKeys:Ze},q,U).checkedKeys}te(Ke,!he,Ue,gt)}}N(he?null:Ot),(Ye=Ne==null?void 0:Ne.onChange)===null||Ye===void 0||Ye.call(Ne,We)}})),checked:he}};const H=(ye,re,_)=>{const{node:Ie,checked:Ke}=B(ye,re,_);return g?g(Ke,re,_,Ie):Ie};if(!F.includes(Kt))if(F.findIndex(ye=>{var re;return((re=ye[gn])===null||re===void 0?void 0:re.columnType)==="EXPAND_COLUMN"})===0){const[ye,...re]=F;F=[ye,Kt].concat(we(re))}else F=[Kt].concat(we(F));const de=F.indexOf(Kt);F=F.filter((ye,re)=>ye!==Kt||re===de);const ve=F[de-1],fe=F[de+1];let Pe=b;Pe===void 0&&((fe==null?void 0:fe.fixed)!==void 0?Pe=fe.fixed:(ve==null?void 0:ve.fixed)!==void 0&&(Pe=ve.fixed)),Pe&&ve&&((V=ve[gn])===null||V===void 0?void 0:V.columnType)==="EXPAND_COLUMN"&&ve.fixed===void 0&&(ve.fixed=Pe);const je=G(`${C}-selection-col`,{[`${C}-selection-col-with-dropdown`]:p&&f==="checkbox"}),Fe=()=>t!=null&&t.columnTitle?typeof t.columnTitle=="function"?t.columnTitle(Ee):t.columnTitle:Ae,xe={fixed:Pe,width:m,className:`${C}-selection-column`,title:Fe(),render:H,onCell:t.onCell,align:t.align,[gn]:{className:je}};return F.map(ye=>ye===Kt?xe:ye)},[k,M,t,ee,ge,ce,m,pe,R,X,u,te,U]),ge]};function pd(e,t){return e._antProxy=e._antProxy||{},Object.keys(t).forEach(r=>{if(!(r in e._antProxy)){const n=e[r];e._antProxy[r]=n,e[r]=t[r]}}),e}function gd(e,t){return a.useImperativeHandle(e,()=>{const r=t(),{nativeElement:n}=r;return typeof Proxy<"u"?new Proxy(n,{get(o,l){return r[l]?r[l]:Reflect.get(o,l)}}):pd(n,r)})}function hd(e){return t=>{const{prefixCls:r,onExpand:n,record:o,expanded:l,expandable:d}=t,i=`${r}-row-expand-icon`;return a.createElement("button",{type:"button",onClick:s=>{n(o,s),s.stopPropagation()},className:G(i,{[`${i}-spaced`]:!d,[`${i}-expanded`]:d&&l,[`${i}-collapsed`]:d&&!l}),"aria-label":l?e.collapse:e.expand,"aria-expanded":l})}}function bd(e){return(r,n)=>{const o=r.querySelector(`.${e}-container`);let l=n;if(o){const d=getComputedStyle(o),i=parseInt(d.borderLeftWidth,10),s=parseInt(d.borderRightWidth,10);l=n-i-s}return l}}const Bt=(e,t)=>"key"in e&&e.key!==void 0&&e.key!==null?e.key:e.dataIndex?Array.isArray(e.dataIndex)?e.dataIndex.join("."):e.dataIndex:t;function an(e,t){return t?`${t}-${e}`:`${e}`}const Dn=(e,t)=>typeof e=="function"?e(t):e,yd=(e,t)=>{const r=Dn(e,t);return Object.prototype.toString.call(r)==="[object Object]"?"":r};function Cd(e){const t=a.useRef(e),r=xl();return[()=>t.current,n=>{t.current=n,r()}]}var xd=function(t){var r=t.dropPosition,n=t.dropLevelOffset,o=t.indent,l={pointerEvents:"none",position:"absolute",right:0,backgroundColor:"red",height:2};switch(r){case-1:l.top=0,l.left=-n*o;break;case 1:l.bottom=0,l.left=-n*o;break;case 0:l.bottom=0,l.left=o;break}return J.createElement("div",{style:l})};function za(e){if(e==null)throw new TypeError("Cannot destructure "+e)}function Sd(e,t){var r=a.useState(!1),n=Ce(r,2),o=n[0],l=n[1];xt(function(){if(o)return e(),function(){t()}},[o]),xt(function(){return l(!0),function(){l(!1)}},[])}var wd=["className","style","motion","motionNodes","motionType","onMotionStart","onMotionEnd","active","treeNodeRequiredProps"],$d=a.forwardRef(function(e,t){var r=e.className,n=e.style,o=e.motion,l=e.motionNodes,d=e.motionType,i=e.onMotionStart,s=e.onMotionEnd,c=e.active,v=e.treeNodeRequiredProps,u=ft(e,wd),m=a.useState(!0),f=Ce(m,2),p=f[0],b=f[1],g=a.useContext(Er),h=g.prefixCls,x=l&&d!=="hide";xt(function(){l&&x!==p&&b(x)},[l]);var C=function(){l&&i()},S=a.useRef(!1),w=function(){l&&!S.current&&(S.current=!0,s())};Sd(C,w);var E=function(R){x===R&&w()};return l?a.createElement(Un,le({ref:t,visible:p},o,{motionAppear:d==="show",onVisibleChanged:E}),function(k,R){var y=k.className,P=k.style;return a.createElement("div",{ref:R,className:G("".concat(h,"-treenode-motion"),y),style:P},l.map(function(I){var T=Object.assign({},(za(I.data),I.data)),O=I.title,N=I.key,$=I.isStart,K=I.isEnd;delete T.children;var D=pn(N,v);return a.createElement(yn,le({},T,D,{title:O,active:c,data:I.data,key:N,isStart:$,isEnd:K}))}))}):a.createElement(yn,le({domRef:t,className:r,style:n},u,{active:c}))});function Ed(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],r=e.length,n=t.length;if(Math.abs(r-n)!==1)return{add:!1,key:null};function o(l,d){var i=new Map;l.forEach(function(c){i.set(c,!0)});var s=d.filter(function(c){return!i.has(c)});return s.length===1?s[0]:null}return r<n?{add:!0,key:o(e,t)}:{add:!1,key:o(t,e)}}function Co(e,t,r){var n=e.findIndex(function(i){return i.key===r}),o=e[n+1],l=t.findIndex(function(i){return i.key===r});if(o){var d=t.findIndex(function(i){return i.key===o.key});return t.slice(l+1,d)}return t.slice(l+1)}var Nd=["prefixCls","data","selectable","checkable","expandedKeys","selectedKeys","checkedKeys","loadedKeys","loadingKeys","halfCheckedKeys","keyEntities","disabled","dragging","dragOverNodeKey","dropPosition","motion","height","itemHeight","virtual","scrollWidth","focusable","activeItem","focused","tabIndex","onKeyDown","onFocus","onBlur","onActiveChange","onListChangeStart","onListChangeEnd"],xo={width:0,height:0,display:"flex",overflow:"hidden",opacity:0,border:0,padding:0,margin:0},kd=function(){},Vt="RC_TREE_MOTION_".concat(Math.random()),or={key:Vt},ja={key:Vt,level:0,index:0,pos:"0",node:or,nodes:[or]},So={parent:null,children:[],pos:ja.pos,data:or,title:null,key:Vt,isStart:[],isEnd:[]};function wo(e,t,r,n){return t===!1||!r?e:e.slice(0,Math.ceil(r/n)+1)}function $o(e){var t=e.key,r=e.pos;return Sn(t,r)}function Rd(e){for(var t=String(e.data.key),r=e;r.parent;)r=r.parent,t="".concat(r.data.key," > ").concat(t);return t}var Od=a.forwardRef(function(e,t){var r=e.prefixCls,n=e.data;e.selectable,e.checkable;var o=e.expandedKeys,l=e.selectedKeys,d=e.checkedKeys,i=e.loadedKeys,s=e.loadingKeys,c=e.halfCheckedKeys,v=e.keyEntities,u=e.disabled,m=e.dragging,f=e.dragOverNodeKey,p=e.dropPosition,b=e.motion,g=e.height,h=e.itemHeight,x=e.virtual,C=e.scrollWidth,S=e.focusable,w=e.activeItem,E=e.focused,k=e.tabIndex,R=e.onKeyDown,y=e.onFocus,P=e.onBlur,I=e.onActiveChange,T=e.onListChangeStart,O=e.onListChangeEnd,N=ft(e,Nd),$=a.useRef(null),K=a.useRef(null);a.useImperativeHandle(t,function(){return{scrollTo:function(H){$.current.scrollTo(H)},getIndentWidth:function(){return K.current.offsetWidth}}});var D=a.useState(o),j=Ce(D,2),M=j[0],q=j[1],X=a.useState(n),U=Ce(X,2),ee=U[0],se=U[1],ge=a.useState(n),ce=Ce(ge,2),Z=ce[0],te=ce[1],pe=a.useState([]),ie=Ce(pe,2),W=ie[0],V=ie[1],F=a.useState(null),Q=Ce(F,2),oe=Q[0],Y=Q[1],ae=a.useRef(n);ae.current=n;function Oe(){var B=ae.current;se(B),te(B),V([]),Y(null),O()}xt(function(){q(o);var B=Ed(M,o);if(B.key!==null)if(B.add){var H=ee.findIndex(function(Fe){var xe=Fe.key;return xe===B.key}),de=wo(Co(ee,n,B.key),x,g,h),ve=ee.slice();ve.splice(H+1,0,So),te(ve),V(de),Y("show")}else{var fe=n.findIndex(function(Fe){var xe=Fe.key;return xe===B.key}),Pe=wo(Co(n,ee,B.key),x,g,h),je=n.slice();je.splice(fe+1,0,So),te(je),V(Pe),Y("hide")}else ee!==n&&(se(n),te(n))},[o,n]),a.useEffect(function(){m||Oe()},[m]);var Ae=b?Z:n,Ee={expandedKeys:o,selectedKeys:l,loadedKeys:i,loadingKeys:s,checkedKeys:d,halfCheckedKeys:c,dragOverNodeKey:f,dropPosition:p,keyEntities:v};return a.createElement(a.Fragment,null,E&&w&&a.createElement("span",{style:xo,"aria-live":"assertive"},Rd(w)),a.createElement("div",null,a.createElement("input",{style:xo,disabled:S===!1||u,tabIndex:S!==!1?k:null,onKeyDown:R,onFocus:y,onBlur:P,value:"",onChange:kd,"aria-label":"for screen reader"})),a.createElement("div",{className:"".concat(r,"-treenode"),"aria-hidden":!0,style:{position:"absolute",pointerEvents:"none",visibility:"hidden",height:0,overflow:"hidden",border:0,padding:0}},a.createElement("div",{className:"".concat(r,"-indent")},a.createElement("div",{ref:K,className:"".concat(r,"-indent-unit")}))),a.createElement(Xo,le({},N,{data:Ae,itemKey:$o,height:g,fullHeight:!1,virtual:x,itemHeight:h,scrollWidth:C,prefixCls:"".concat(r,"-list"),ref:$,role:"tree",onVisibleChange:function(H){H.every(function(de){return $o(de)!==Vt})&&Oe()}}),function(B){var H=B.pos,de=Object.assign({},(za(B.data),B.data)),ve=B.title,fe=B.key,Pe=B.isStart,je=B.isEnd,Fe=Sn(fe,H);delete de.key,delete de.children;var xe=pn(Fe,Ee);return a.createElement($d,le({},de,xe,{title:ve,active:!!w&&fe===w.key,pos:H,data:B.data,isStart:Pe,isEnd:je,motion:b,motionNodes:fe===Vt?W:null,motionType:oe,onMotionStart:T,onMotionEnd:Oe,treeNodeRequiredProps:Ee,onMouseMove:function(){I(null)}}))}))}),Id=10,kr=function(e){Sl(r,e);var t=wl(r);function r(){var n;$l(this,r);for(var o=arguments.length,l=new Array(o),d=0;d<o;d++)l[d]=arguments[d];return n=t.call.apply(t,[this].concat(l)),z($e(n),"destroyed",!1),z($e(n),"delayedDragEnterLogic",void 0),z($e(n),"loadingRetryTimes",{}),z($e(n),"state",{keyEntities:{},indent:null,selectedKeys:[],checkedKeys:[],halfCheckedKeys:[],loadedKeys:[],loadingKeys:[],expandedKeys:[],draggingNodeKey:null,dragChildrenKeys:[],dropTargetKey:null,dropPosition:null,dropContainerKey:null,dropLevelOffset:null,dropTargetPos:null,dropAllowed:!0,dragOverNodeKey:null,treeData:[],flattenNodes:[],focused:!1,activeKey:null,listChanging:!1,prevProps:null,fieldNames:Qt()}),z($e(n),"dragStartMousePosition",null),z($e(n),"dragNodeProps",null),z($e(n),"currentMouseOverDroppableNodeKey",null),z($e(n),"listRef",a.createRef()),z($e(n),"onNodeDragStart",function(i,s){var c=n.state,v=c.expandedKeys,u=c.keyEntities,m=n.props.onDragStart,f=s.eventKey;n.dragNodeProps=s,n.dragStartMousePosition={x:i.clientX,y:i.clientY};var p=bt(v,f);n.setState({draggingNodeKey:f,dragChildrenKeys:dd(f,u),indent:n.listRef.current.getIndentWidth()}),n.setExpandedKeys(p),window.addEventListener("dragend",n.onWindowDragEnd),m==null||m({event:i,node:Ve(s)})}),z($e(n),"onNodeDragEnter",function(i,s){var c=n.state,v=c.expandedKeys,u=c.keyEntities,m=c.dragChildrenKeys,f=c.flattenNodes,p=c.indent,b=n.props,g=b.onDragEnter,h=b.onExpand,x=b.allowDrop,C=b.direction,S=s.pos,w=s.eventKey;if(n.currentMouseOverDroppableNodeKey!==w&&(n.currentMouseOverDroppableNodeKey=w),!n.dragNodeProps){n.resetDragState();return}var E=ho(i,n.dragNodeProps,s,p,n.dragStartMousePosition,x,f,u,v,C),k=E.dropPosition,R=E.dropLevelOffset,y=E.dropTargetKey,P=E.dropContainerKey,I=E.dropTargetPos,T=E.dropAllowed,O=E.dragOverNodeKey;if(m.includes(y)||!T){n.resetDragState();return}if(n.delayedDragEnterLogic||(n.delayedDragEnterLogic={}),Object.keys(n.delayedDragEnterLogic).forEach(function(N){clearTimeout(n.delayedDragEnterLogic[N])}),n.dragNodeProps.eventKey!==s.eventKey&&(i.persist(),n.delayedDragEnterLogic[S]=window.setTimeout(function(){if(n.state.draggingNodeKey!==null){var N=we(v),$=ut(u,s.eventKey);$&&($.children||[]).length&&(N=$t(v,s.eventKey)),n.props.hasOwnProperty("expandedKeys")||n.setExpandedKeys(N),h==null||h(N,{node:Ve(s),expanded:!0,nativeEvent:i.nativeEvent})}},800)),n.dragNodeProps.eventKey===y&&R===0){n.resetDragState();return}n.setState({dragOverNodeKey:O,dropPosition:k,dropLevelOffset:R,dropTargetKey:y,dropContainerKey:P,dropTargetPos:I,dropAllowed:T}),g==null||g({event:i,node:Ve(s),expandedKeys:v})}),z($e(n),"onNodeDragOver",function(i,s){var c=n.state,v=c.dragChildrenKeys,u=c.flattenNodes,m=c.keyEntities,f=c.expandedKeys,p=c.indent,b=n.props,g=b.onDragOver,h=b.allowDrop,x=b.direction;if(n.dragNodeProps){var C=ho(i,n.dragNodeProps,s,p,n.dragStartMousePosition,h,u,m,f,x),S=C.dropPosition,w=C.dropLevelOffset,E=C.dropTargetKey,k=C.dropContainerKey,R=C.dropTargetPos,y=C.dropAllowed,P=C.dragOverNodeKey;v.includes(E)||!y||(n.dragNodeProps.eventKey===E&&w===0?n.state.dropPosition===null&&n.state.dropLevelOffset===null&&n.state.dropTargetKey===null&&n.state.dropContainerKey===null&&n.state.dropTargetPos===null&&n.state.dropAllowed===!1&&n.state.dragOverNodeKey===null||n.resetDragState():S===n.state.dropPosition&&w===n.state.dropLevelOffset&&E===n.state.dropTargetKey&&k===n.state.dropContainerKey&&R===n.state.dropTargetPos&&y===n.state.dropAllowed&&P===n.state.dragOverNodeKey||n.setState({dropPosition:S,dropLevelOffset:w,dropTargetKey:E,dropContainerKey:k,dropTargetPos:R,dropAllowed:y,dragOverNodeKey:P}),g==null||g({event:i,node:Ve(s)}))}}),z($e(n),"onNodeDragLeave",function(i,s){n.currentMouseOverDroppableNodeKey===s.eventKey&&!i.currentTarget.contains(i.relatedTarget)&&(n.resetDragState(),n.currentMouseOverDroppableNodeKey=null);var c=n.props.onDragLeave;c==null||c({event:i,node:Ve(s)})}),z($e(n),"onWindowDragEnd",function(i){n.onNodeDragEnd(i,null,!0),window.removeEventListener("dragend",n.onWindowDragEnd)}),z($e(n),"onNodeDragEnd",function(i,s){var c=n.props.onDragEnd;n.setState({dragOverNodeKey:null}),n.cleanDragState(),c==null||c({event:i,node:Ve(s)}),n.dragNodeProps=null,window.removeEventListener("dragend",n.onWindowDragEnd)}),z($e(n),"onNodeDrop",function(i,s){var c,v=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,u=n.state,m=u.dragChildrenKeys,f=u.dropPosition,p=u.dropTargetKey,b=u.dropTargetPos,g=u.dropAllowed;if(g){var h=n.props.onDrop;if(n.setState({dragOverNodeKey:null}),n.cleanDragState(),p!==null){var x=A(A({},pn(p,n.getTreeNodeRequiredProps())),{},{active:((c=n.getActiveItem())===null||c===void 0?void 0:c.key)===p,data:ut(n.state.keyEntities,p).node}),C=m.includes(p);Nt(!C,"Can not drop to dragNode's children node. This is a bug of rc-tree. Please report an issue.");var S=Nr(b),w={event:i,node:Ve(x),dragNode:n.dragNodeProps?Ve(n.dragNodeProps):null,dragNodesKeys:[n.dragNodeProps.eventKey].concat(m),dropToGap:f!==0,dropPosition:f+Number(S[S.length-1])};v||h==null||h(w),n.dragNodeProps=null}}}),z($e(n),"cleanDragState",function(){var i=n.state.draggingNodeKey;i!==null&&n.setState({draggingNodeKey:null,dropPosition:null,dropContainerKey:null,dropTargetKey:null,dropLevelOffset:null,dropAllowed:!0,dragOverNodeKey:null}),n.dragStartMousePosition=null,n.currentMouseOverDroppableNodeKey=null}),z($e(n),"triggerExpandActionExpand",function(i,s){var c=n.state,v=c.expandedKeys,u=c.flattenNodes,m=s.expanded,f=s.key,p=s.isLeaf;if(!(p||i.shiftKey||i.metaKey||i.ctrlKey)){var b=u.filter(function(h){return h.key===f})[0],g=Ve(A(A({},pn(f,n.getTreeNodeRequiredProps())),{},{data:b.data}));n.setExpandedKeys(m?bt(v,f):$t(v,f)),n.onNodeExpand(i,g)}}),z($e(n),"onNodeClick",function(i,s){var c=n.props,v=c.onClick,u=c.expandAction;u==="click"&&n.triggerExpandActionExpand(i,s),v==null||v(i,s)}),z($e(n),"onNodeDoubleClick",function(i,s){var c=n.props,v=c.onDoubleClick,u=c.expandAction;u==="doubleClick"&&n.triggerExpandActionExpand(i,s),v==null||v(i,s)}),z($e(n),"onNodeSelect",function(i,s){var c=n.state.selectedKeys,v=n.state,u=v.keyEntities,m=v.fieldNames,f=n.props,p=f.onSelect,b=f.multiple,g=s.selected,h=s[m.key],x=!g;x?b?c=$t(c,h):c=[h]:c=bt(c,h);var C=c.map(function(S){var w=ut(u,S);return w?w.node:null}).filter(Boolean);n.setUncontrolledState({selectedKeys:c}),p==null||p(c,{event:"select",selected:x,node:s,selectedNodes:C,nativeEvent:i.nativeEvent})}),z($e(n),"onNodeCheck",function(i,s,c){var v=n.state,u=v.keyEntities,m=v.checkedKeys,f=v.halfCheckedKeys,p=n.props,b=p.checkStrictly,g=p.onCheck,h=s.key,x,C={event:"check",node:s,checked:c,nativeEvent:i.nativeEvent};if(b){var S=c?$t(m,h):bt(m,h),w=bt(f,h);x={checked:S,halfChecked:w},C.checkedNodes=S.map(function(I){return ut(u,I)}).filter(Boolean).map(function(I){return I.node}),n.setUncontrolledState({checkedKeys:S})}else{var E=Ut([].concat(we(m),[h]),!0,u),k=E.checkedKeys,R=E.halfCheckedKeys;if(!c){var y=new Set(k);y.delete(h);var P=Ut(Array.from(y),{halfCheckedKeys:R},u);k=P.checkedKeys,R=P.halfCheckedKeys}x=k,C.checkedNodes=[],C.checkedNodesPositions=[],C.halfCheckedKeys=R,k.forEach(function(I){var T=ut(u,I);if(T){var O=T.node,N=T.pos;C.checkedNodes.push(O),C.checkedNodesPositions.push({node:O,pos:N})}}),n.setUncontrolledState({checkedKeys:k},!1,{halfCheckedKeys:R})}g==null||g(x,C)}),z($e(n),"onNodeLoad",function(i){var s,c=i.key,v=n.state.keyEntities,u=ut(v,c);if(!(u!=null&&(s=u.children)!==null&&s!==void 0&&s.length)){var m=new Promise(function(f,p){n.setState(function(b){var g=b.loadedKeys,h=g===void 0?[]:g,x=b.loadingKeys,C=x===void 0?[]:x,S=n.props,w=S.loadData,E=S.onLoad;if(!w||h.includes(c)||C.includes(c))return null;var k=w(i);return k.then(function(){var R=n.state.loadedKeys,y=$t(R,c);E==null||E(y,{event:"load",node:i}),n.setUncontrolledState({loadedKeys:y}),n.setState(function(P){return{loadingKeys:bt(P.loadingKeys,c)}}),f()}).catch(function(R){if(n.setState(function(P){return{loadingKeys:bt(P.loadingKeys,c)}}),n.loadingRetryTimes[c]=(n.loadingRetryTimes[c]||0)+1,n.loadingRetryTimes[c]>=Id){var y=n.state.loadedKeys;Nt(!1,"Retry for `loadData` many times but still failed. No more retry."),n.setUncontrolledState({loadedKeys:$t(y,c)}),f()}p(R)}),{loadingKeys:$t(C,c)}})});return m.catch(function(){}),m}}),z($e(n),"onNodeMouseEnter",function(i,s){var c=n.props.onMouseEnter;c==null||c({event:i,node:s})}),z($e(n),"onNodeMouseLeave",function(i,s){var c=n.props.onMouseLeave;c==null||c({event:i,node:s})}),z($e(n),"onNodeContextMenu",function(i,s){var c=n.props.onRightClick;c&&(i.preventDefault(),c({event:i,node:s}))}),z($e(n),"onFocus",function(){var i=n.props.onFocus;n.setState({focused:!0});for(var s=arguments.length,c=new Array(s),v=0;v<s;v++)c[v]=arguments[v];i==null||i.apply(void 0,c)}),z($e(n),"onBlur",function(){var i=n.props.onBlur;n.setState({focused:!1}),n.onActiveChange(null);for(var s=arguments.length,c=new Array(s),v=0;v<s;v++)c[v]=arguments[v];i==null||i.apply(void 0,c)}),z($e(n),"getTreeNodeRequiredProps",function(){var i=n.state,s=i.expandedKeys,c=i.selectedKeys,v=i.loadedKeys,u=i.loadingKeys,m=i.checkedKeys,f=i.halfCheckedKeys,p=i.dragOverNodeKey,b=i.dropPosition,g=i.keyEntities;return{expandedKeys:s||[],selectedKeys:c||[],loadedKeys:v||[],loadingKeys:u||[],checkedKeys:m||[],halfCheckedKeys:f||[],dragOverNodeKey:p,dropPosition:b,keyEntities:g}}),z($e(n),"setExpandedKeys",function(i){var s=n.state,c=s.treeData,v=s.fieldNames,u=An(c,i,v);n.setUncontrolledState({expandedKeys:i,flattenNodes:u},!0)}),z($e(n),"onNodeExpand",function(i,s){var c=n.state.expandedKeys,v=n.state,u=v.listChanging,m=v.fieldNames,f=n.props,p=f.onExpand,b=f.loadData,g=s.expanded,h=s[m.key];if(!u){var x=c.includes(h),C=!g;if(Nt(g&&x||!g&&!x,"Expand state not sync with index check"),c=C?$t(c,h):bt(c,h),n.setExpandedKeys(c),p==null||p(c,{node:s,expanded:C,nativeEvent:i.nativeEvent}),C&&b){var S=n.onNodeLoad(s);S&&S.then(function(){var w=An(n.state.treeData,c,m);n.setUncontrolledState({flattenNodes:w})}).catch(function(){var w=n.state.expandedKeys,E=bt(w,h);n.setExpandedKeys(E)})}}}),z($e(n),"onListChangeStart",function(){n.setUncontrolledState({listChanging:!0})}),z($e(n),"onListChangeEnd",function(){setTimeout(function(){n.setUncontrolledState({listChanging:!1})})}),z($e(n),"onActiveChange",function(i){var s=n.state.activeKey,c=n.props,v=c.onActiveChange,u=c.itemScrollOffset,m=u===void 0?0:u;s!==i&&(n.setState({activeKey:i}),i!==null&&n.scrollTo({key:i,offset:m}),v==null||v(i))}),z($e(n),"getActiveItem",function(){var i=n.state,s=i.activeKey,c=i.flattenNodes;return s===null?null:c.find(function(v){var u=v.key;return u===s})||null}),z($e(n),"offsetActiveKey",function(i){var s=n.state,c=s.flattenNodes,v=s.activeKey,u=c.findIndex(function(p){var b=p.key;return b===v});u===-1&&i<0&&(u=c.length),u=(u+i+c.length)%c.length;var m=c[u];if(m){var f=m.key;n.onActiveChange(f)}else n.onActiveChange(null)}),z($e(n),"onKeyDown",function(i){var s=n.state,c=s.activeKey,v=s.expandedKeys,u=s.checkedKeys,m=s.fieldNames,f=n.props,p=f.onKeyDown,b=f.checkable,g=f.selectable;switch(i.which){case qe.UP:{n.offsetActiveKey(-1),i.preventDefault();break}case qe.DOWN:{n.offsetActiveKey(1),i.preventDefault();break}}var h=n.getActiveItem();if(h&&h.data){var x=n.getTreeNodeRequiredProps(),C=h.data.isLeaf===!1||!!(h.data[m.children]||[]).length,S=Ve(A(A({},pn(c,x)),{},{data:h.data,active:!0}));switch(i.which){case qe.LEFT:{C&&v.includes(c)?n.onNodeExpand({},S):h.parent&&n.onActiveChange(h.parent.key),i.preventDefault();break}case qe.RIGHT:{C&&!v.includes(c)?n.onNodeExpand({},S):h.children&&h.children.length&&n.onActiveChange(h.children[0].key),i.preventDefault();break}case qe.ENTER:case qe.SPACE:{b&&!S.disabled&&S.checkable!==!1&&!S.disableCheckbox?n.onNodeCheck({},S,!u.includes(c)):!b&&g&&!S.disabled&&S.selectable!==!1&&n.onNodeSelect({},S);break}}}p==null||p(i)}),z($e(n),"setUncontrolledState",function(i){var s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,c=arguments.length>2&&arguments[2]!==void 0?arguments[2]:null;if(!n.destroyed){var v=!1,u=!0,m={};Object.keys(i).forEach(function(f){if(n.props.hasOwnProperty(f)){u=!1;return}v=!0,m[f]=i[f]}),v&&(!s||u)&&n.setState(A(A({},m),c))}}),z($e(n),"scrollTo",function(i){n.listRef.current.scrollTo(i)}),n}return El(r,[{key:"componentDidMount",value:function(){this.destroyed=!1,this.onUpdated()}},{key:"componentDidUpdate",value:function(){this.onUpdated()}},{key:"onUpdated",value:function(){var o=this.props,l=o.activeKey,d=o.itemScrollOffset,i=d===void 0?0:d;l!==void 0&&l!==this.state.activeKey&&(this.setState({activeKey:l}),l!==null&&this.scrollTo({key:l,offset:i}))}},{key:"componentWillUnmount",value:function(){window.removeEventListener("dragend",this.onWindowDragEnd),this.destroyed=!0}},{key:"resetDragState",value:function(){this.setState({dragOverNodeKey:null,dropPosition:null,dropLevelOffset:null,dropTargetKey:null,dropContainerKey:null,dropTargetPos:null,dropAllowed:!1})}},{key:"render",value:function(){var o=this.state,l=o.focused,d=o.flattenNodes,i=o.keyEntities,s=o.draggingNodeKey,c=o.activeKey,v=o.dropLevelOffset,u=o.dropContainerKey,m=o.dropTargetKey,f=o.dropPosition,p=o.dragOverNodeKey,b=o.indent,g=this.props,h=g.prefixCls,x=g.className,C=g.style,S=g.showLine,w=g.focusable,E=g.tabIndex,k=E===void 0?0:E,R=g.selectable,y=g.showIcon,P=g.icon,I=g.switcherIcon,T=g.draggable,O=g.checkable,N=g.checkStrictly,$=g.disabled,K=g.motion,D=g.loadData,j=g.filterTreeNode,M=g.height,q=g.itemHeight,X=g.scrollWidth,U=g.virtual,ee=g.titleRender,se=g.dropIndicatorRender,ge=g.onContextMenu,ce=g.onScroll,Z=g.direction,te=g.rootClassName,pe=g.rootStyle,ie=kt(this.props,{aria:!0,data:!0}),W;T&&(pt(T)==="object"?W=T:typeof T=="function"?W={nodeDraggable:T}:W={});var V={prefixCls:h,selectable:R,showIcon:y,icon:P,switcherIcon:I,draggable:W,draggingNodeKey:s,checkable:O,checkStrictly:N,disabled:$,keyEntities:i,dropLevelOffset:v,dropContainerKey:u,dropTargetKey:m,dropPosition:f,dragOverNodeKey:p,indent:b,direction:Z,dropIndicatorRender:se,loadData:D,filterTreeNode:j,titleRender:ee,onNodeClick:this.onNodeClick,onNodeDoubleClick:this.onNodeDoubleClick,onNodeExpand:this.onNodeExpand,onNodeSelect:this.onNodeSelect,onNodeCheck:this.onNodeCheck,onNodeLoad:this.onNodeLoad,onNodeMouseEnter:this.onNodeMouseEnter,onNodeMouseLeave:this.onNodeMouseLeave,onNodeContextMenu:this.onNodeContextMenu,onNodeDragStart:this.onNodeDragStart,onNodeDragEnter:this.onNodeDragEnter,onNodeDragOver:this.onNodeDragOver,onNodeDragLeave:this.onNodeDragLeave,onNodeDragEnd:this.onNodeDragEnd,onNodeDrop:this.onNodeDrop};return a.createElement(Er.Provider,{value:V},a.createElement("div",{className:G(h,x,te,z(z(z({},"".concat(h,"-show-line"),S),"".concat(h,"-focused"),l),"".concat(h,"-active-focused"),c!==null)),style:pe},a.createElement(Od,le({ref:this.listRef,prefixCls:h,style:C,data:d,disabled:$,selectable:R,checkable:!!O,motion:K,dragging:s!==null,height:M,itemHeight:q,virtual:U,focusable:w,focused:l,tabIndex:k,activeItem:this.getActiveItem(),onFocus:this.onFocus,onBlur:this.onBlur,onKeyDown:this.onKeyDown,onActiveChange:this.onActiveChange,onListChangeStart:this.onListChangeStart,onListChangeEnd:this.onListChangeEnd,onContextMenu:ge,onScroll:ce,scrollWidth:X},this.getTreeNodeRequiredProps(),ie))))}}],[{key:"getDerivedStateFromProps",value:function(o,l){var d=l.prevProps,i={prevProps:o};function s(k){return!d&&o.hasOwnProperty(k)||d&&d[k]!==o[k]}var c,v=l.fieldNames;if(s("fieldNames")&&(v=Qt(o.fieldNames),i.fieldNames=v),s("treeData")?c=o.treeData:s("children")&&(Nt(!1,"`children` of Tree is deprecated. Please use `treeData` instead."),c=aa(o.children)),c){i.treeData=c;var u=yr(c,{fieldNames:v});i.keyEntities=A(z({},Vt,ja),u.keyEntities)}var m=i.keyEntities||l.keyEntities;if(s("expandedKeys")||d&&s("autoExpandParent"))i.expandedKeys=o.autoExpandParent||!d&&o.defaultExpandParent?er(o.expandedKeys,m):o.expandedKeys;else if(!d&&o.defaultExpandAll){var f=A({},m);delete f[Vt];var p=[];Object.keys(f).forEach(function(k){var R=f[k];R.children&&R.children.length&&p.push(R.key)}),i.expandedKeys=p}else!d&&o.defaultExpandedKeys&&(i.expandedKeys=o.autoExpandParent||o.defaultExpandParent?er(o.defaultExpandedKeys,m):o.defaultExpandedKeys);if(i.expandedKeys||delete i.expandedKeys,c||i.expandedKeys){var b=An(c||l.treeData,i.expandedKeys||l.expandedKeys,v);i.flattenNodes=b}if(o.selectable&&(s("selectedKeys")?i.selectedKeys=bo(o.selectedKeys,o):!d&&o.defaultSelectedKeys&&(i.selectedKeys=bo(o.defaultSelectedKeys,o))),o.checkable){var g;if(s("checkedKeys")?g=Fn(o.checkedKeys)||{}:!d&&o.defaultCheckedKeys?g=Fn(o.defaultCheckedKeys)||{}:c&&(g=Fn(o.checkedKeys)||{checkedKeys:l.checkedKeys,halfCheckedKeys:l.halfCheckedKeys}),g){var h=g,x=h.checkedKeys,C=x===void 0?[]:x,S=h.halfCheckedKeys,w=S===void 0?[]:S;if(!o.checkStrictly){var E=Ut(C,!0,m);C=E.checkedKeys,w=E.halfCheckedKeys}i.checkedKeys=C,i.halfCheckedKeys=w}}return s("loadedKeys")&&(i.loadedKeys=o.loadedKeys),i}}]),r}(a.Component);z(kr,"defaultProps",{prefixCls:"rc-tree",showLine:!1,showIcon:!0,selectable:!0,multiple:!1,checkable:!1,disabled:!1,checkStrictly:!1,draggable:!1,defaultExpandParent:!0,autoExpandParent:!1,defaultExpandAll:!1,defaultExpandedKeys:[],defaultCheckedKeys:[],defaultSelectedKeys:[],dropIndicatorRender:xd,allowDrop:function(){return!0},expandAction:!1});z(kr,"TreeNode",yn);const Pd=({treeCls:e,treeNodeCls:t,directoryNodeSelectedBg:r,directoryNodeSelectedColor:n,motionDurationMid:o,borderRadius:l,controlItemBgHover:d})=>({[`${e}${e}-directory ${t}`]:{[`${e}-node-content-wrapper`]:{position:"static",[`&:has(${e}-drop-indicator)`]:{position:"relative"},[`> *:not(${e}-drop-indicator)`]:{position:"relative"},"&:hover":{background:"transparent"},"&:before":{position:"absolute",inset:0,transition:`background-color ${o}`,content:'""',borderRadius:l},"&:hover:before":{background:d}},[`${e}-switcher, ${e}-checkbox, ${e}-draggable-icon`]:{zIndex:1},"&-selected":{background:r,borderRadius:l,[`${e}-switcher, ${e}-draggable-icon`]:{color:n},[`${e}-node-content-wrapper`]:{color:n,background:"transparent","&:before, &:hover:before":{background:r}}}}}),Td=new kl("ant-tree-node-fx-do-not-use",{"0%":{opacity:0},"100%":{opacity:1}}),Kd=(e,t)=>({[`.${e}-switcher-icon`]:{display:"inline-block",fontSize:10,verticalAlign:"baseline",svg:{transition:`transform ${t.motionDurationSlow}`}}}),Dd=(e,t)=>({[`.${e}-drop-indicator`]:{position:"absolute",zIndex:1,height:2,backgroundColor:t.colorPrimary,borderRadius:1,pointerEvents:"none","&:after":{position:"absolute",top:-3,insetInlineStart:-6,width:8,height:8,backgroundColor:"transparent",border:`${L(t.lineWidthBold)} solid ${t.colorPrimary}`,borderRadius:"50%",content:'""'}}}),Md=(e,t)=>{const{treeCls:r,treeNodeCls:n,treeNodePadding:o,titleHeight:l,indentSize:d,nodeSelectedBg:i,nodeHoverBg:s,colorTextQuaternary:c,controlItemBgActiveDisabled:v}=t;return{[r]:Object.assign(Object.assign({},Ct(t)),{"--rc-virtual-list-scrollbar-bg":t.colorSplit,background:t.colorBgContainer,borderRadius:t.borderRadius,transition:`background-color ${t.motionDurationSlow}`,"&-rtl":{direction:"rtl"},[`&${r}-rtl ${r}-switcher_close ${r}-switcher-icon svg`]:{transform:"rotate(90deg)"},[`&-focused:not(:hover):not(${r}-active-focused)`]:Object.assign({},Yt(t)),[`${r}-list-holder-inner`]:{alignItems:"flex-start"},[`&${r}-block-node`]:{[`${r}-list-holder-inner`]:{alignItems:"stretch",[`${r}-node-content-wrapper`]:{flex:"auto"},[`${n}.dragging:after`]:{position:"absolute",inset:0,border:`1px solid ${t.colorPrimary}`,opacity:0,animationName:Td,animationDuration:t.motionDurationSlow,animationPlayState:"running",animationFillMode:"forwards",content:'""',pointerEvents:"none",borderRadius:t.borderRadius}}},[n]:{display:"flex",alignItems:"flex-start",marginBottom:o,lineHeight:L(l),position:"relative","&:before":{content:'""',position:"absolute",zIndex:1,insetInlineStart:0,width:"100%",top:"100%",height:o},[`&-disabled ${r}-node-content-wrapper`]:{color:t.colorTextDisabled,cursor:"not-allowed","&:hover":{background:"transparent"}},[`${r}-checkbox-disabled + ${r}-node-selected,&${n}-disabled${n}-selected ${r}-node-content-wrapper`]:{backgroundColor:v},[`${r}-checkbox-disabled`]:{pointerEvents:"unset"},[`&:not(${n}-disabled)`]:{[`${r}-node-content-wrapper`]:{"&:hover":{color:t.nodeHoverColor}}},[`&-active ${r}-node-content-wrapper`]:{background:t.controlItemBgHover},[`&:not(${n}-disabled).filter-node ${r}-title`]:{color:t.colorPrimary,fontWeight:t.fontWeightStrong},"&-draggable":{cursor:"grab",[`${r}-draggable-icon`]:{flexShrink:0,width:l,textAlign:"center",visibility:"visible",color:c},[`&${n}-disabled ${r}-draggable-icon`]:{visibility:"hidden"}}},[`${r}-indent`]:{alignSelf:"stretch",whiteSpace:"nowrap",userSelect:"none","&-unit":{display:"inline-block",width:d}},[`${r}-draggable-icon`]:{visibility:"hidden"},[`${r}-switcher, ${r}-checkbox`]:{marginInlineEnd:t.calc(t.calc(l).sub(t.controlInteractiveSize)).div(2).equal()},[`${r}-switcher`]:Object.assign(Object.assign({},Kd(e,t)),{position:"relative",flex:"none",alignSelf:"stretch",width:l,textAlign:"center",cursor:"pointer",userSelect:"none",transition:`all ${t.motionDurationSlow}`,"&-noop":{cursor:"unset"},"&:before":{pointerEvents:"none",content:'""',width:l,height:l,position:"absolute",left:{_skip_check_:!0,value:0},top:0,borderRadius:t.borderRadius,transition:`all ${t.motionDurationSlow}`},[`&:not(${r}-switcher-noop):hover:before`]:{backgroundColor:t.colorBgTextHover},[`&_close ${r}-switcher-icon svg`]:{transform:"rotate(-90deg)"},"&-loading-icon":{color:t.colorPrimary},"&-leaf-line":{position:"relative",zIndex:1,display:"inline-block",width:"100%",height:"100%","&:before":{position:"absolute",top:0,insetInlineEnd:t.calc(l).div(2).equal(),bottom:t.calc(o).mul(-1).equal(),marginInlineStart:-1,borderInlineEnd:`1px solid ${t.colorBorder}`,content:'""'},"&:after":{position:"absolute",width:t.calc(t.calc(l).div(2).equal()).mul(.8).equal(),height:t.calc(l).div(2).equal(),borderBottom:`1px solid ${t.colorBorder}`,content:'""'}}}),[`${r}-node-content-wrapper`]:Object.assign(Object.assign({position:"relative",minHeight:l,paddingBlock:0,paddingInline:t.paddingXS,background:"transparent",borderRadius:t.borderRadius,cursor:"pointer",transition:`all ${t.motionDurationMid}, border 0s, line-height 0s, box-shadow 0s`},Dd(e,t)),{"&:hover":{backgroundColor:s},[`&${r}-node-selected`]:{color:t.nodeSelectedColor,backgroundColor:i},[`${r}-iconEle`]:{display:"inline-block",width:l,height:l,textAlign:"center",verticalAlign:"top","&:empty":{display:"none"}}}),[`${r}-unselectable ${r}-node-content-wrapper:hover`]:{backgroundColor:"transparent"},[`${n}.drop-container > [draggable]`]:{boxShadow:`0 0 0 2px ${t.colorPrimary}`},"&-show-line":{[`${r}-indent-unit`]:{position:"relative",height:"100%","&:before":{position:"absolute",top:0,insetInlineEnd:t.calc(l).div(2).equal(),bottom:t.calc(o).mul(-1).equal(),borderInlineEnd:`1px solid ${t.colorBorder}`,content:'""'},"&-end:before":{display:"none"}},[`${r}-switcher`]:{background:"transparent","&-line-icon":{verticalAlign:"-0.15em"}}},[`${n}-leaf-last ${r}-switcher-leaf-line:before`]:{top:"auto !important",bottom:"auto !important",height:`${L(t.calc(l).div(2).equal())} !important`}})}},Bd=(e,t,r=!0)=>{const n=`.${e}`,o=`${n}-treenode`,l=t.calc(t.paddingXS).div(2).equal(),d=tn(t,{treeCls:n,treeNodeCls:o,treeNodePadding:l});return[Md(e,d),r&&Pd(d)].filter(Boolean)},zd=e=>{const{controlHeightSM:t,controlItemBgHover:r,controlItemBgActive:n}=e,o=t;return{titleHeight:o,indentSize:o,nodeHoverBg:r,nodeHoverColor:e.colorText,nodeSelectedBg:n,nodeSelectedColor:e.colorText}},jd=e=>{const{colorTextLightSolid:t,colorPrimary:r}=e;return Object.assign(Object.assign({},zd(e)),{directoryNodeSelectedColor:t,directoryNodeSelectedBg:r})},_d=en("Tree",(e,{prefixCls:t})=>[{[e.componentCls]:ia(`${t}-checkbox`,e)},Bd(t,e),Nl(e)],jd),Eo=4;function Ld(e){const{dropPosition:t,dropLevelOffset:r,prefixCls:n,indent:o,direction:l="ltr"}=e,d=l==="ltr"?"left":"right",i=l==="ltr"?"right":"left",s={[d]:-r*o+Eo,[i]:0};switch(t){case-1:s.top=-3;break;case 1:s.bottom=-3;break;default:s.bottom=-3,s[d]=o+Eo;break}return J.createElement("div",{style:s,className:`${n}-drop-indicator`})}const Hd=e=>{var t,r;const{prefixCls:n,switcherIcon:o,treeNodeProps:l,showLine:d,switcherLoadingIcon:i}=e,{isLeaf:s,expanded:c,loading:v}=l;if(v)return a.isValidElement(i)?i:a.createElement(Rl,{className:`${n}-switcher-loading-icon`});let u;if(d&&typeof d=="object"&&(u=d.showLeafIcon),s){if(!d)return null;if(typeof u!="boolean"&&u){const p=typeof u=="function"?u(l):u,b=`${n}-switcher-line-custom-icon`;return a.isValidElement(p)?Gr(p,{className:G((t=p.props)===null||t===void 0?void 0:t.className,b)}):p}return u?a.createElement(Yo,{className:`${n}-switcher-line-icon`}):a.createElement("span",{className:`${n}-switcher-leaf-line`})}const m=`${n}-switcher-icon`,f=typeof o=="function"?o(l):o;return a.isValidElement(f)?Gr(f,{className:G((r=f.props)===null||r===void 0?void 0:r.className,m)}):f!==void 0?f:d?c?a.createElement(Ii,{className:`${n}-switcher-line-icon`}):a.createElement(Ki,{className:`${n}-switcher-line-icon`}):a.createElement(ti,{className:m})},_a=J.forwardRef((e,t)=>{var r;const{getPrefixCls:n,direction:o,virtual:l,tree:d}=J.useContext(St),{prefixCls:i,className:s,showIcon:c=!1,showLine:v,switcherIcon:u,switcherLoadingIcon:m,blockNode:f=!1,children:p,checkable:b=!1,selectable:g=!0,draggable:h,motion:x,style:C}=e,S=n("tree",i),w=n(),E=x??Object.assign(Object.assign({},Ol(w)),{motionAppear:!1}),k=Object.assign(Object.assign({},e),{checkable:b,selectable:g,showIcon:c,motion:E,blockNode:f,showLine:!!v,dropIndicatorRender:Ld}),[R,y,P]=_d(S),[,I]=fr(),T=I.paddingXS/2+(((r=I.Tree)===null||r===void 0?void 0:r.titleHeight)||I.controlHeightSM),O=J.useMemo(()=>{if(!h)return!1;let $={};switch(typeof h){case"function":$.nodeDraggable=h;break;case"object":$=Object.assign({},h);break}return $.icon!==!1&&($.icon=$.icon||J.createElement(ki,null)),$},[h]),N=$=>J.createElement(Hd,{prefixCls:S,switcherIcon:u,switcherLoadingIcon:m,treeNodeProps:$,showLine:v});return R(J.createElement(kr,Object.assign({itemHeight:T,ref:t,virtual:l},k,{style:Object.assign(Object.assign({},d==null?void 0:d.style),C),prefixCls:S,className:G({[`${S}-icon-hide`]:!c,[`${S}-block-node`]:f,[`${S}-unselectable`]:!g,[`${S}-rtl`]:o==="rtl"},d==null?void 0:d.className,s,y,P),direction:o,checkable:b&&J.createElement("span",{className:`${S}-checkbox-inner`}),selectable:g,switcherIcon:N,draggable:O}),p))}),No=0,Wn=1,ko=2;function Rr(e,t,r){const{key:n,children:o}=r;function l(d){const i=d[n],s=d[o];t(i,d)!==!1&&Rr(s||[],t,r)}e.forEach(l)}function Ad({treeData:e,expandedKeys:t,startKey:r,endKey:n,fieldNames:o}){const l=[];let d=No;if(r&&r===n)return[r];if(!r||!n)return[];function i(s){return s===r||s===n}return Rr(e,s=>{if(d===ko)return!1;if(i(s)){if(l.push(s),d===No)d=Wn;else if(d===Wn)return d=ko,!1}else d===Wn&&l.push(s);return t.includes(s)},Qt(o)),l}function Vn(e,t,r){const n=we(t),o=[];return Rr(e,(l,d)=>{const i=n.indexOf(l);return i!==-1&&(o.push(d),n.splice(i,1)),!!n.length},Qt(r)),o}var Ro=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};function Fd(e){const{isLeaf:t,expanded:r}=e;return t?a.createElement(Yo,null):r?a.createElement(yi,null):a.createElement(Qo,null)}function Oo({treeData:e,children:t}){return e||aa(t)}const Wd=(e,t)=>{var{defaultExpandAll:r,defaultExpandParent:n,defaultExpandedKeys:o}=e,l=Ro(e,["defaultExpandAll","defaultExpandParent","defaultExpandedKeys"]);const d=a.useRef(null),i=a.useRef(null),s=()=>{const{keyEntities:R}=yr(Oo(l));let y;return r?y=Object.keys(R):n?y=er(l.expandedKeys||o||[],R):y=l.expandedKeys||o||[],y},[c,v]=a.useState(l.selectedKeys||l.defaultSelectedKeys||[]),[u,m]=a.useState(()=>s());a.useEffect(()=>{"selectedKeys"in l&&v(l.selectedKeys)},[l.selectedKeys]),a.useEffect(()=>{"expandedKeys"in l&&m(l.expandedKeys)},[l.expandedKeys]);const f=(R,y)=>{var P;return"expandedKeys"in l||m(R),(P=l.onExpand)===null||P===void 0?void 0:P.call(l,R,y)},p=(R,y)=>{var P;const{multiple:I,fieldNames:T}=l,{node:O,nativeEvent:N}=y,{key:$=""}=O,K=Oo(l),D=Object.assign(Object.assign({},y),{selected:!0}),j=(N==null?void 0:N.ctrlKey)||(N==null?void 0:N.metaKey),M=N==null?void 0:N.shiftKey;let q;I&&j?(q=R,d.current=$,i.current=q,D.selectedNodes=Vn(K,q,T)):I&&M?(q=Array.from(new Set([].concat(we(i.current||[]),we(Ad({treeData:K,expandedKeys:u,startKey:$,endKey:d.current,fieldNames:T}))))),D.selectedNodes=Vn(K,q,T)):(q=[$],d.current=$,i.current=q,D.selectedNodes=Vn(K,q,T)),(P=l.onSelect)===null||P===void 0||P.call(l,q,D),"selectedKeys"in l||v(q)},{getPrefixCls:b,direction:g}=a.useContext(St),{prefixCls:h,className:x,showIcon:C=!0,expandAction:S="click"}=l,w=Ro(l,["prefixCls","className","showIcon","expandAction"]),E=b("tree",h),k=G(`${E}-directory`,{[`${E}-directory-rtl`]:g==="rtl"},x);return a.createElement(_a,Object.assign({icon:Fd,ref:t,blockNode:!0},w,{showIcon:C,expandAction:S,prefixCls:E,className:k,expandedKeys:u,selectedKeys:c,onSelect:p,onExpand:f}))},Vd=a.forwardRef(Wd),Or=_a;Or.DirectoryTree=Vd;Or.TreeNode=yn;const Io=e=>{const{value:t,filterSearch:r,tablePrefixCls:n,locale:o,onChange:l}=e;return r?a.createElement("div",{className:`${n}-filter-dropdown-search`},a.createElement(Il,{prefix:a.createElement(Pl,null),placeholder:o.filterSearchPlaceholder,onChange:l,value:t,htmlSize:1,className:`${n}-filter-dropdown-search-input`})):null},qd=e=>{const{keyCode:t}=e;t===qe.ENTER&&e.stopPropagation()},Xd=a.forwardRef((e,t)=>a.createElement("div",{className:e.className,onClick:r=>r.stopPropagation(),onKeyDown:qd,ref:t},e.children));function Jt(e){let t=[];return(e||[]).forEach(({value:r,children:n})=>{t.push(r),n&&(t=[].concat(we(t),we(Jt(n))))}),t}function Gd(e){return e.some(({children:t})=>t)}function La(e,t){return typeof t=="string"||typeof t=="number"?t==null?void 0:t.toString().toLowerCase().includes(e.trim().toLowerCase()):!1}function Ha({filters:e,prefixCls:t,filteredKeys:r,filterMultiple:n,searchValue:o,filterSearch:l}){return e.map((d,i)=>{const s=String(d.value);if(d.children)return{key:s||i,label:d.text,popupClassName:`${t}-dropdown-submenu`,children:Ha({filters:d.children,prefixCls:t,filteredKeys:r,filterMultiple:n,searchValue:o,filterSearch:l})};const c=n?Zt:xn,v={key:d.value!==void 0?s:i,label:a.createElement(a.Fragment,null,a.createElement(c,{checked:r.includes(s)}),a.createElement("span",null,d.text))};return o.trim()?typeof l=="function"?l(o,d)?v:null:La(o,d.text)?v:null:v})}function qn(e){return e||[]}const Ud=e=>{var t,r,n,o;const{tablePrefixCls:l,prefixCls:d,column:i,dropdownPrefixCls:s,columnKey:c,filterOnClose:v,filterMultiple:u,filterMode:m="menu",filterSearch:f=!1,filterState:p,triggerFilter:b,locale:g,children:h,getPopupContainer:x,rootClassName:C}=e,{filterResetToDefaultFilteredValue:S,defaultFilteredValue:w,filterDropdownProps:E={},filterDropdownOpen:k,filterDropdownVisible:R,onFilterDropdownVisibleChange:y,onFilterDropdownOpenChange:P}=i,[I,T]=a.useState(!1),O=!!(p&&(!((t=p.filteredKeys)===null||t===void 0)&&t.length||p.forceFiltered)),N=B=>{var H;T(B),(H=E.onOpenChange)===null||H===void 0||H.call(E,B),P==null||P(B),y==null||y(B)},$=(o=(n=(r=E.open)!==null&&r!==void 0?r:k)!==null&&n!==void 0?n:R)!==null&&o!==void 0?o:I,K=p==null?void 0:p.filteredKeys,[D,j]=Cd(qn(K)),M=({selectedKeys:B})=>{j(B)},q=(B,{node:H,checked:de})=>{M(u?{selectedKeys:B}:{selectedKeys:de&&H.key?[H.key]:[]})};a.useEffect(()=>{I&&M({selectedKeys:qn(K)})},[K]);const[X,U]=a.useState([]),ee=B=>{U(B)},[se,ge]=a.useState(""),ce=B=>{const{value:H}=B.target;ge(H)};a.useEffect(()=>{I||ge("")},[I]);const Z=B=>{const H=B!=null&&B.length?B:null;if(H===null&&(!p||!p.filteredKeys)||bn(H,p==null?void 0:p.filteredKeys,!0))return null;b({column:i,key:c,filteredKeys:H})},te=()=>{N(!1),Z(D())},pe=({confirm:B,closeDropdown:H}={confirm:!1,closeDropdown:!1})=>{B&&Z([]),H&&N(!1),ge(""),j(S?(w||[]).map(de=>String(de)):[])},ie=({closeDropdown:B}={closeDropdown:!0})=>{B&&N(!1),Z(D())},W=(B,H)=>{H.source==="trigger"&&(B&&K!==void 0&&j(qn(K)),N(B),!B&&!i.filterDropdown&&v&&te())},V=G({[`${s}-menu-without-submenu`]:!Gd(i.filters||[])}),F=B=>{if(B.target.checked){const H=Jt(i==null?void 0:i.filters).map(de=>String(de));j(H)}else j([])},Q=({filters:B})=>(B||[]).map((H,de)=>{const ve=String(H.value),fe={title:H.text,key:H.value!==void 0?ve:String(de)};return H.children&&(fe.children=Q({filters:H.children})),fe}),oe=B=>{var H;return Object.assign(Object.assign({},B),{text:B.title,value:B.key,children:((H=B.children)===null||H===void 0?void 0:H.map(de=>oe(de)))||[]})};let Y;const{direction:ae,renderEmpty:Oe}=a.useContext(St);if(typeof i.filterDropdown=="function")Y=i.filterDropdown({prefixCls:`${s}-custom`,setSelectedKeys:B=>M({selectedKeys:B}),selectedKeys:D(),confirm:ie,clearFilters:pe,filters:i.filters,visible:$,close:()=>{N(!1)}});else if(i.filterDropdown)Y=i.filterDropdown;else{const B=D()||[],H=()=>{var ve,fe;const Pe=(ve=Oe==null?void 0:Oe("Table.filter"))!==null&&ve!==void 0?ve:a.createElement(Ur,{image:Ur.PRESENTED_IMAGE_SIMPLE,description:g.filterEmptyText,styles:{image:{height:24}},style:{margin:0,padding:"16px 0"}});if((i.filters||[]).length===0)return Pe;if(m==="tree")return a.createElement(a.Fragment,null,a.createElement(Io,{filterSearch:f,value:se,onChange:ce,tablePrefixCls:l,locale:g}),a.createElement("div",{className:`${l}-filter-dropdown-tree`},u?a.createElement(Zt,{checked:B.length===Jt(i.filters).length,indeterminate:B.length>0&&B.length<Jt(i.filters).length,className:`${l}-filter-dropdown-checkall`,onChange:F},(fe=g==null?void 0:g.filterCheckall)!==null&&fe!==void 0?fe:g==null?void 0:g.filterCheckAll):null,a.createElement(Or,{checkable:!0,selectable:!1,blockNode:!0,multiple:u,checkStrictly:!u,className:`${s}-menu`,onCheck:q,checkedKeys:B,selectedKeys:B,showIcon:!1,treeData:Q({filters:i.filters}),autoExpandParent:!0,defaultExpandAll:!0,filterTreeNode:se.trim()?xe=>typeof f=="function"?f(se,oe(xe)):La(se,xe.title):void 0})));const je=Ha({filters:i.filters||[],filterSearch:f,prefixCls:d,filteredKeys:D(),filterMultiple:u,searchValue:se}),Fe=je.every(xe=>xe===null);return a.createElement(a.Fragment,null,a.createElement(Io,{filterSearch:f,value:se,onChange:ce,tablePrefixCls:l,locale:g}),Fe?Pe:a.createElement(Tl,{selectable:!0,multiple:u,prefixCls:`${s}-menu`,className:V,onSelect:M,onDeselect:M,selectedKeys:B,getPopupContainer:x,openKeys:X,onOpenChange:ee,items:je}))},de=()=>S?bn((w||[]).map(ve=>String(ve)),B,!0):B.length===0;Y=a.createElement(a.Fragment,null,H(),a.createElement("div",{className:`${d}-dropdown-btns`},a.createElement(Rn,{type:"link",size:"small",disabled:de(),onClick:()=>pe()},g.filterReset),a.createElement(Rn,{type:"primary",size:"small",onClick:te},g.filterConfirm)))}i.filterDropdown&&(Y=a.createElement(Kl,{selectable:void 0},Y)),Y=a.createElement(Xd,{className:`${d}-dropdown`},Y);const Ee=Go({trigger:["click"],placement:ae==="rtl"?"bottomLeft":"bottomRight",children:(()=>{let B;return typeof i.filterIcon=="function"?B=i.filterIcon(O):i.filterIcon?B=i.filterIcon:B=a.createElement(gi,null),a.createElement("span",{role:"button",tabIndex:-1,className:G(`${d}-trigger`,{active:O}),onClick:H=>{H.stopPropagation()}},B)})(),getPopupContainer:x},Object.assign(Object.assign({},E),{rootClassName:G(C,E.rootClassName),open:$,onOpenChange:W,popupRender:()=>typeof(E==null?void 0:E.dropdownRender)=="function"?E.dropdownRender(Y):Y}));return a.createElement("div",{className:`${d}-column`},a.createElement("span",{className:`${l}-column-title`},h),a.createElement(br,Object.assign({},Ee)))},ar=(e,t,r)=>{let n=[];return(e||[]).forEach((o,l)=>{var d;const i=an(l,r),s=o.filterDropdown!==void 0;if(o.filters||s||"onFilter"in o)if("filteredValue"in o){let c=o.filteredValue;s||(c=(d=c==null?void 0:c.map(String))!==null&&d!==void 0?d:c),n.push({column:o,key:Bt(o,i),filteredKeys:c,forceFiltered:o.filtered})}else n.push({column:o,key:Bt(o,i),filteredKeys:t&&o.defaultFilteredValue?o.defaultFilteredValue:void 0,forceFiltered:o.filtered});"children"in o&&(n=[].concat(we(n),we(ar(o.children,t,i))))}),n};function Aa(e,t,r,n,o,l,d,i,s){return r.map((c,v)=>{const u=an(v,i),{filterOnClose:m=!0,filterMultiple:f=!0,filterMode:p,filterSearch:b}=c;let g=c;if(g.filters||g.filterDropdown){const h=Bt(g,u),x=n.find(({key:C})=>h===C);g=Object.assign(Object.assign({},g),{title:C=>a.createElement(Ud,{tablePrefixCls:e,prefixCls:`${e}-filter`,dropdownPrefixCls:t,column:g,columnKey:h,filterState:x,filterOnClose:m,filterMultiple:f,filterMode:p,filterSearch:b,triggerFilter:l,locale:o,getPopupContainer:d,rootClassName:s},Dn(c.title,C))})}return"children"in g&&(g=Object.assign(Object.assign({},g),{children:Aa(e,t,g.children,n,o,l,d,u,s)})),g})}const Po=e=>{const t={};return e.forEach(({key:r,filteredKeys:n,column:o})=>{const l=r,{filters:d,filterDropdown:i}=o;if(i)t[l]=n||null;else if(Array.isArray(n)){const s=Jt(d);t[l]=s.filter(c=>n.includes(String(c)))}else t[l]=null}),t},lr=(e,t,r)=>t.reduce((o,l)=>{const{column:{onFilter:d,filters:i},filteredKeys:s}=l;return d&&s&&s.length?o.map(c=>Object.assign({},c)).filter(c=>s.some(v=>{const u=Jt(i),m=u.findIndex(p=>String(p)===String(v)),f=m!==-1?u[m]:v;return c[r]&&(c[r]=lr(c[r],t,r)),d(f,c)})):o},e),Fa=e=>e.flatMap(t=>"children"in t?[t].concat(we(Fa(t.children||[]))):[t]),Jd=e=>{const{prefixCls:t,dropdownPrefixCls:r,mergedColumns:n,onFilterChange:o,getPopupContainer:l,locale:d,rootClassName:i}=e;hr();const s=a.useMemo(()=>Fa(n||[]),[n]),[c,v]=a.useState(()=>ar(s,!0)),u=a.useMemo(()=>{const b=ar(s,!1);if(b.length===0)return b;let g=!0;if(b.forEach(({filteredKeys:h})=>{h!==void 0&&(g=!1)}),g){const h=(s||[]).map((x,C)=>Bt(x,an(C)));return c.filter(({key:x})=>h.includes(x)).map(x=>{const C=s[h.findIndex(S=>S===x.key)];return Object.assign(Object.assign({},x),{column:Object.assign(Object.assign({},x.column),C),forceFiltered:C.filtered})})}return b},[s,c]),m=a.useMemo(()=>Po(u),[u]),f=b=>{const g=u.filter(({key:h})=>h!==b.key);g.push(b),v(g),o(Po(g),g)};return[b=>Aa(t,r,b,u,d,f,l,void 0,i),u,m]},Yd=(e,t,r)=>{const n=a.useRef({});function o(l){var d;if(!n.current||n.current.data!==e||n.current.childrenColumnName!==t||n.current.getRowKey!==r){let s=function(c){c.forEach((v,u)=>{const m=r(v,u);i.set(m,v),v&&typeof v=="object"&&t in v&&s(v[t]||[])})};const i=new Map;s(e),n.current={data:e,childrenColumnName:t,kvMap:i,getRowKey:r}}return(d=n.current.kvMap)===null||d===void 0?void 0:d.get(l)}return[o]};var Qd=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const Wa=10;function Zd(e,t){const r={current:e.current,pageSize:e.pageSize};return Object.keys(t&&typeof t=="object"?t:{}).forEach(o=>{const l=e[o];typeof l!="function"&&(r[o]=l)}),r}function eu(e,t,r){const n=r&&typeof r=="object"?r:{},{total:o=0}=n,l=Qd(n,["total"]),[d,i]=a.useState(()=>({current:"defaultCurrent"in l?l.defaultCurrent:1,pageSize:"defaultPageSize"in l?l.defaultPageSize:Wa})),s=Go(d,l,{total:o>0?o:e}),c=Math.ceil((o||e)/s.pageSize);s.current>c&&(s.current=c||1);const v=(m,f)=>{i({current:m??1,pageSize:f||s.pageSize})},u=(m,f)=>{var p;r&&((p=r.onChange)===null||p===void 0||p.call(r,m,f)),v(m,f),t(m,f||(s==null?void 0:s.pageSize))};return r===!1?[{},()=>{}]:[Object.assign(Object.assign({},s),{onChange:u}),v]}const kn="ascend",Xn="descend",In=e=>typeof e.sorter=="object"&&typeof e.sorter.multiple=="number"?e.sorter.multiple:!1,To=e=>typeof e=="function"?e:e&&typeof e=="object"&&e.compare?e.compare:!1,tu=(e,t)=>t?e[e.indexOf(t)+1]:e[0],ir=(e,t,r)=>{let n=[];const o=(l,d)=>{n.push({column:l,key:Bt(l,d),multiplePriority:In(l),sortOrder:l.sortOrder})};return(e||[]).forEach((l,d)=>{const i=an(d,r);l.children?("sortOrder"in l&&o(l,i),n=[].concat(we(n),we(ir(l.children,t,i)))):l.sorter&&("sortOrder"in l?o(l,i):t&&l.defaultSortOrder&&n.push({column:l,key:Bt(l,i),multiplePriority:In(l),sortOrder:l.defaultSortOrder}))}),n},Va=(e,t,r,n,o,l,d,i)=>(t||[]).map((c,v)=>{const u=an(v,i);let m=c;if(m.sorter){const f=m.sortDirections||o,p=m.showSorterTooltip===void 0?d:m.showSorterTooltip,b=Bt(m,u),g=r.find(({key:y})=>y===b),h=g?g.sortOrder:null,x=tu(f,h);let C;if(c.sortIcon)C=c.sortIcon({sortOrder:h});else{const y=f.includes(kn)&&a.createElement(ii,{className:G(`${e}-column-sorter-up`,{active:h===kn})}),P=f.includes(Xn)&&a.createElement(oi,{className:G(`${e}-column-sorter-down`,{active:h===Xn})});C=a.createElement("span",{className:G(`${e}-column-sorter`,{[`${e}-column-sorter-full`]:!!(y&&P)})},a.createElement("span",{className:`${e}-column-sorter-inner`,"aria-hidden":"true"},y,P))}const{cancelSort:S,triggerAsc:w,triggerDesc:E}=l||{};let k=S;x===Xn?k=E:x===kn&&(k=w);const R=typeof p=="object"?Object.assign({title:k},p):{title:k};m=Object.assign(Object.assign({},m),{className:G(m.className,{[`${e}-column-sort`]:h}),title:y=>{const P=`${e}-column-sorters`,I=a.createElement("span",{className:`${e}-column-title`},Dn(c.title,y)),T=a.createElement("div",{className:P},I,C);return p?typeof p!="boolean"&&(p==null?void 0:p.target)==="sorter-icon"?a.createElement("div",{className:`${P} ${e}-column-sorters-tooltip-target-sorter`},I,a.createElement(Jr,Object.assign({},R),C)):a.createElement(Jr,Object.assign({},R),T):T},onHeaderCell:y=>{var P;const I=((P=c.onHeaderCell)===null||P===void 0?void 0:P.call(c,y))||{},T=I.onClick,O=I.onKeyDown;I.onClick=K=>{n({column:c,key:b,sortOrder:x,multiplePriority:In(c)}),T==null||T(K)},I.onKeyDown=K=>{K.keyCode===qe.ENTER&&(n({column:c,key:b,sortOrder:x,multiplePriority:In(c)}),O==null||O(K))};const N=yd(c.title,{}),$=N==null?void 0:N.toString();return h&&(I["aria-sort"]=h==="ascend"?"ascending":"descending"),I["aria-label"]=$||"",I.className=G(I.className,`${e}-column-has-sorters`),I.tabIndex=0,c.ellipsis&&(I.title=(N??"").toString()),I}})}return"children"in m&&(m=Object.assign(Object.assign({},m),{children:Va(e,m.children,r,n,o,l,d,u)})),m}),Ko=e=>{const{column:t,sortOrder:r}=e;return{column:t,order:r,field:t.dataIndex,columnKey:t.key}},Do=e=>{const t=e.filter(({sortOrder:r})=>r).map(Ko);if(t.length===0&&e.length){const r=e.length-1;return Object.assign(Object.assign({},Ko(e[r])),{column:void 0,order:void 0,field:void 0,columnKey:void 0})}return t.length<=1?t[0]||{}:t},sr=(e,t,r)=>{const n=t.slice().sort((d,i)=>i.multiplePriority-d.multiplePriority),o=e.slice(),l=n.filter(({column:{sorter:d},sortOrder:i})=>To(d)&&i);return l.length?o.sort((d,i)=>{for(let s=0;s<l.length;s+=1){const c=l[s],{column:{sorter:v},sortOrder:u}=c,m=To(v);if(m&&u){const f=m(d,i,u);if(f!==0)return u===kn?f:-f}}return 0}).map(d=>{const i=d[r];return i?Object.assign(Object.assign({},d),{[r]:sr(i,t,r)}):d}):o},nu=e=>{const{prefixCls:t,mergedColumns:r,sortDirections:n,tableLocale:o,showSorterTooltip:l,onSorterChange:d}=e,[i,s]=a.useState(()=>ir(r,!0)),c=(b,g)=>{const h=[];return b.forEach((x,C)=>{const S=an(C,g);if(h.push(Bt(x,S)),Array.isArray(x.children)){const w=c(x.children,S);h.push.apply(h,we(w))}}),h},v=a.useMemo(()=>{let b=!0;const g=ir(r,!1);if(!g.length){const S=c(r);return i.filter(({key:w})=>S.includes(w))}const h=[];function x(S){b?h.push(S):h.push(Object.assign(Object.assign({},S),{sortOrder:null}))}let C=null;return g.forEach(S=>{C===null?(x(S),S.sortOrder&&(S.multiplePriority===!1?b=!1:C=!0)):(C&&S.multiplePriority!==!1||(b=!1),x(S))}),h},[r,i]),u=a.useMemo(()=>{var b,g;const h=v.map(({column:x,sortOrder:C})=>({column:x,order:C}));return{sortColumns:h,sortColumn:(b=h[0])===null||b===void 0?void 0:b.column,sortOrder:(g=h[0])===null||g===void 0?void 0:g.order}},[v]),m=b=>{let g;b.multiplePriority===!1||!v.length||v[0].multiplePriority===!1?g=[b]:g=[].concat(we(v.filter(({key:h})=>h!==b.key)),[b]),s(g),d(Do(g),g)};return[b=>Va(t,b,v,m,n,o,l),v,u,()=>Do(v)]},qa=(e,t)=>e.map(n=>{const o=Object.assign({},n);return o.title=Dn(n.title,t),"children"in o&&(o.children=qa(o.children,t)),o}),ru=e=>[a.useCallback(r=>qa(r,e),[e])],ou=Ka((e,t)=>{const{_renderTimes:r}=e,{_renderTimes:n}=t;return r!==n}),au=Ma((e,t)=>{const{_renderTimes:r}=e,{_renderTimes:n}=t;return r!==n}),lu=e=>{const{componentCls:t,lineWidth:r,lineType:n,tableBorderColor:o,tableHeaderBg:l,tablePaddingVertical:d,tablePaddingHorizontal:i,calc:s}=e,c=`${L(r)} ${n} ${o}`,v=(u,m,f)=>({[`&${t}-${u}`]:{[`> ${t}-container`]:{[`> ${t}-content, > ${t}-body`]:{"\n            > table > tbody > tr > th,\n            > table > tbody > tr > td\n          ":{[`> ${t}-expanded-row-fixed`]:{margin:`${L(s(m).mul(-1).equal())}
              ${L(s(s(f).add(r)).mul(-1).equal())}`}}}}}});return{[`${t}-wrapper`]:{[`${t}${t}-bordered`]:Object.assign(Object.assign(Object.assign({[`> ${t}-title`]:{border:c,borderBottom:0},[`> ${t}-container`]:{borderInlineStart:c,borderTop:c,[`
            > ${t}-content,
            > ${t}-header,
            > ${t}-body,
            > ${t}-summary
          `]:{"> table":{"\n                > thead > tr > th,\n                > thead > tr > td,\n                > tbody > tr > th,\n                > tbody > tr > td,\n                > tfoot > tr > th,\n                > tfoot > tr > td\n              ":{borderInlineEnd:c},"> thead":{"> tr:not(:last-child) > th":{borderBottom:c},"> tr > th::before":{backgroundColor:"transparent !important"}},"\n                > thead > tr,\n                > tbody > tr,\n                > tfoot > tr\n              ":{[`> ${t}-cell-fix-right-first::after`]:{borderInlineEnd:c}},"\n                > tbody > tr > th,\n                > tbody > tr > td\n              ":{[`> ${t}-expanded-row-fixed`]:{margin:`${L(s(d).mul(-1).equal())} ${L(s(s(i).add(r)).mul(-1).equal())}`,"&::after":{position:"absolute",top:0,insetInlineEnd:r,bottom:0,borderInlineEnd:c,content:'""'}}}}}},[`&${t}-scroll-horizontal`]:{[`> ${t}-container > ${t}-body`]:{"> table > tbody":{[`
                > tr${t}-expanded-row,
                > tr${t}-placeholder
              `]:{"> th, > td":{borderInlineEnd:0}}}}}},v("middle",e.tablePaddingVerticalMiddle,e.tablePaddingHorizontalMiddle)),v("small",e.tablePaddingVerticalSmall,e.tablePaddingHorizontalSmall)),{[`> ${t}-footer`]:{border:c,borderTop:0}}),[`${t}-cell`]:{[`${t}-container:first-child`]:{borderTop:0},"&-scrollbar:not([rowspan])":{boxShadow:`0 ${L(r)} 0 ${L(r)} ${l}`}},[`${t}-bordered ${t}-cell-scrollbar`]:{borderInlineEnd:c}}}},iu=e=>{const{componentCls:t}=e;return{[`${t}-wrapper`]:{[`${t}-cell-ellipsis`]:Object.assign(Object.assign({},Dl),{wordBreak:"keep-all",[`
          &${t}-cell-fix-left-last,
          &${t}-cell-fix-right-first
        `]:{overflow:"visible",[`${t}-cell-content`]:{display:"block",overflow:"hidden",textOverflow:"ellipsis"}},[`${t}-column-title`]:{overflow:"hidden",textOverflow:"ellipsis",wordBreak:"keep-all"}})}}},su=e=>{const{componentCls:t}=e;return{[`${t}-wrapper`]:{[`${t}-tbody > tr${t}-placeholder`]:{textAlign:"center",color:e.colorTextDisabled,"\n          &:hover > th,\n          &:hover > td,\n        ":{background:e.colorBgContainer}}}}},cu=e=>{const{componentCls:t,antCls:r,motionDurationSlow:n,lineWidth:o,paddingXS:l,lineType:d,tableBorderColor:i,tableExpandIconBg:s,tableExpandColumnWidth:c,borderRadius:v,tablePaddingVertical:u,tablePaddingHorizontal:m,tableExpandedRowBg:f,paddingXXS:p,expandIconMarginTop:b,expandIconSize:g,expandIconHalfInner:h,expandIconScale:x,calc:C}=e,S=`${L(o)} ${d} ${i}`,w=C(p).sub(o).equal();return{[`${t}-wrapper`]:{[`${t}-expand-icon-col`]:{width:c},[`${t}-row-expand-icon-cell`]:{textAlign:"center",[`${t}-row-expand-icon`]:{display:"inline-flex",float:"none",verticalAlign:"sub"}},[`${t}-row-indent`]:{height:1,float:"left"},[`${t}-row-expand-icon`]:Object.assign(Object.assign({},Ml(e)),{position:"relative",float:"left",width:g,height:g,color:"inherit",lineHeight:L(g),background:s,border:S,borderRadius:v,transform:`scale(${x})`,"&:focus, &:hover, &:active":{borderColor:"currentcolor"},"&::before, &::after":{position:"absolute",background:"currentcolor",transition:`transform ${n} ease-out`,content:'""'},"&::before":{top:h,insetInlineEnd:w,insetInlineStart:w,height:o},"&::after":{top:w,bottom:w,insetInlineStart:h,width:o,transform:"rotate(90deg)"},"&-collapsed::before":{transform:"rotate(-180deg)"},"&-collapsed::after":{transform:"rotate(0deg)"},"&-spaced":{"&::before, &::after":{display:"none",content:"none"},background:"transparent",border:0,visibility:"hidden"}}),[`${t}-row-indent + ${t}-row-expand-icon`]:{marginTop:b,marginInlineEnd:l},[`tr${t}-expanded-row`]:{"&, &:hover":{"> th, > td":{background:f}},[`${r}-descriptions-view`]:{display:"flex",table:{flex:"auto",width:"100%"}}},[`${t}-expanded-row-fixed`]:{position:"relative",margin:`${L(C(u).mul(-1).equal())} ${L(C(m).mul(-1).equal())}`,padding:`${L(u)} ${L(m)}`}}}},du=e=>{const{componentCls:t,antCls:r,iconCls:n,tableFilterDropdownWidth:o,tableFilterDropdownSearchWidth:l,paddingXXS:d,paddingXS:i,colorText:s,lineWidth:c,lineType:v,tableBorderColor:u,headerIconColor:m,fontSizeSM:f,tablePaddingHorizontal:p,borderRadius:b,motionDurationSlow:g,colorIcon:h,colorPrimary:x,tableHeaderFilterActiveBg:C,colorTextDisabled:S,tableFilterDropdownBg:w,tableFilterDropdownHeight:E,controlItemBgHover:k,controlItemBgActive:R,boxShadowSecondary:y,filterDropdownMenuBg:P,calc:I}=e,T=`${r}-dropdown`,O=`${t}-filter-dropdown`,N=`${r}-tree`,$=`${L(c)} ${v} ${u}`;return[{[`${t}-wrapper`]:{[`${t}-filter-column`]:{display:"flex",justifyContent:"space-between"},[`${t}-filter-trigger`]:{position:"relative",display:"flex",alignItems:"center",marginBlock:I(d).mul(-1).equal(),marginInline:`${L(d)} ${L(I(p).div(2).mul(-1).equal())}`,padding:`0 ${L(d)}`,color:m,fontSize:f,borderRadius:b,cursor:"pointer",transition:`all ${g}`,"&:hover":{color:h,background:C},"&.active":{color:x}}}},{[`${r}-dropdown`]:{[O]:Object.assign(Object.assign({},Ct(e)),{minWidth:o,backgroundColor:w,borderRadius:b,boxShadow:y,overflow:"hidden",[`${T}-menu`]:{maxHeight:E,overflowX:"hidden",border:0,boxShadow:"none",borderRadius:"unset",backgroundColor:P,"&:empty::after":{display:"block",padding:`${L(i)} 0`,color:S,fontSize:f,textAlign:"center",content:'"Not Found"'}},[`${O}-tree`]:{paddingBlock:`${L(i)} 0`,paddingInline:i,[N]:{padding:0},[`${N}-treenode ${N}-node-content-wrapper:hover`]:{backgroundColor:k},[`${N}-treenode-checkbox-checked ${N}-node-content-wrapper`]:{"&, &:hover":{backgroundColor:R}}},[`${O}-search`]:{padding:i,borderBottom:$,"&-input":{input:{minWidth:l},[n]:{color:S}}},[`${O}-checkall`]:{width:"100%",marginBottom:d,marginInlineStart:d},[`${O}-btns`]:{display:"flex",justifyContent:"space-between",padding:`${L(I(i).sub(c).equal())} ${L(i)}`,overflow:"hidden",borderTop:$}})}},{[`${r}-dropdown ${O}, ${O}-submenu`]:{[`${r}-checkbox-wrapper + span`]:{paddingInlineStart:i,color:s},"> ul":{maxHeight:"calc(100vh - 130px)",overflowX:"hidden",overflowY:"auto"}}}]},uu=e=>{const{componentCls:t,lineWidth:r,colorSplit:n,motionDurationSlow:o,zIndexTableFixed:l,tableBg:d,zIndexTableSticky:i,calc:s}=e,c=n;return{[`${t}-wrapper`]:{[`
        ${t}-cell-fix-left,
        ${t}-cell-fix-right
      `]:{position:"sticky !important",zIndex:l,background:d},[`
        ${t}-cell-fix-left-first::after,
        ${t}-cell-fix-left-last::after
      `]:{position:"absolute",top:0,right:{_skip_check_:!0,value:0},bottom:s(r).mul(-1).equal(),width:30,transform:"translateX(100%)",transition:`box-shadow ${o}`,content:'""',pointerEvents:"none"},[`${t}-cell-fix-left-all::after`]:{display:"none"},[`
        ${t}-cell-fix-right-first::after,
        ${t}-cell-fix-right-last::after
      `]:{position:"absolute",top:0,bottom:s(r).mul(-1).equal(),left:{_skip_check_:!0,value:0},width:30,transform:"translateX(-100%)",transition:`box-shadow ${o}`,content:'""',pointerEvents:"none"},[`${t}-container`]:{position:"relative","&::before, &::after":{position:"absolute",top:0,bottom:0,zIndex:s(i).add(1).equal({unit:!1}),width:30,transition:`box-shadow ${o}`,content:'""',pointerEvents:"none"},"&::before":{insetInlineStart:0},"&::after":{insetInlineEnd:0}},[`${t}-ping-left`]:{[`&:not(${t}-has-fix-left) ${t}-container::before`]:{boxShadow:`inset 10px 0 8px -8px ${c}`},[`
          ${t}-cell-fix-left-first::after,
          ${t}-cell-fix-left-last::after
        `]:{boxShadow:`inset 10px 0 8px -8px ${c}`},[`${t}-cell-fix-left-last::before`]:{backgroundColor:"transparent !important"}},[`${t}-ping-right`]:{[`&:not(${t}-has-fix-right) ${t}-container::after`]:{boxShadow:`inset -10px 0 8px -8px ${c}`},[`
          ${t}-cell-fix-right-first::after,
          ${t}-cell-fix-right-last::after
        `]:{boxShadow:`inset -10px 0 8px -8px ${c}`}},[`${t}-fixed-column-gapped`]:{[`
        ${t}-cell-fix-left-first::after,
        ${t}-cell-fix-left-last::after,
        ${t}-cell-fix-right-first::after,
        ${t}-cell-fix-right-last::after
      `]:{boxShadow:"none"}}}}},fu=e=>{const{componentCls:t,antCls:r,margin:n}=e;return{[`${t}-wrapper`]:{[`${t}-pagination${r}-pagination`]:{margin:`${L(n)} 0`},[`${t}-pagination`]:{display:"flex",flexWrap:"wrap",rowGap:e.paddingXS,"> *":{flex:"none"},"&-left":{justifyContent:"flex-start"},"&-center":{justifyContent:"center"},"&-right":{justifyContent:"flex-end"}}}}},vu=e=>{const{componentCls:t,tableRadius:r}=e;return{[`${t}-wrapper`]:{[t]:{[`${t}-title, ${t}-header`]:{borderRadius:`${L(r)} ${L(r)} 0 0`},[`${t}-title + ${t}-container`]:{borderStartStartRadius:0,borderStartEndRadius:0,[`${t}-header, table`]:{borderRadius:0},"table > thead > tr:first-child":{"th:first-child, th:last-child, td:first-child, td:last-child":{borderRadius:0}}},"&-container":{borderStartStartRadius:r,borderStartEndRadius:r,"table > thead > tr:first-child":{"> *:first-child":{borderStartStartRadius:r},"> *:last-child":{borderStartEndRadius:r}}},"&-footer":{borderRadius:`0 0 ${L(r)} ${L(r)}`}}}}},mu=e=>{const{componentCls:t}=e;return{[`${t}-wrapper-rtl`]:{direction:"rtl",table:{direction:"rtl"},[`${t}-pagination-left`]:{justifyContent:"flex-end"},[`${t}-pagination-right`]:{justifyContent:"flex-start"},[`${t}-row-expand-icon`]:{float:"right","&::after":{transform:"rotate(-90deg)"},"&-collapsed::before":{transform:"rotate(180deg)"},"&-collapsed::after":{transform:"rotate(0deg)"}},[`${t}-container`]:{"&::before":{insetInlineStart:"unset",insetInlineEnd:0},"&::after":{insetInlineStart:0,insetInlineEnd:"unset"},[`${t}-row-indent`]:{float:"right"}}}}},pu=e=>{const{componentCls:t,antCls:r,iconCls:n,fontSizeIcon:o,padding:l,paddingXS:d,headerIconColor:i,headerIconHoverColor:s,tableSelectionColumnWidth:c,tableSelectedRowBg:v,tableSelectedRowHoverBg:u,tableRowHoverBg:m,tablePaddingHorizontal:f,calc:p}=e;return{[`${t}-wrapper`]:{[`${t}-selection-col`]:{width:c,[`&${t}-selection-col-with-dropdown`]:{width:p(c).add(o).add(p(l).div(4)).equal()}},[`${t}-bordered ${t}-selection-col`]:{width:p(c).add(p(d).mul(2)).equal(),[`&${t}-selection-col-with-dropdown`]:{width:p(c).add(o).add(p(l).div(4)).add(p(d).mul(2)).equal()}},[`
        table tr th${t}-selection-column,
        table tr td${t}-selection-column,
        ${t}-selection-column
      `]:{paddingInlineEnd:e.paddingXS,paddingInlineStart:e.paddingXS,textAlign:"center",[`${r}-radio-wrapper`]:{marginInlineEnd:0}},[`table tr th${t}-selection-column${t}-cell-fix-left`]:{zIndex:p(e.zIndexTableFixed).add(1).equal({unit:!1})},[`table tr th${t}-selection-column::after`]:{backgroundColor:"transparent !important"},[`${t}-selection`]:{position:"relative",display:"inline-flex",flexDirection:"column"},[`${t}-selection-extra`]:{position:"absolute",top:0,zIndex:1,cursor:"pointer",transition:`all ${e.motionDurationSlow}`,marginInlineStart:"100%",paddingInlineStart:L(p(f).div(4).equal()),[n]:{color:i,fontSize:o,verticalAlign:"baseline","&:hover":{color:s}}},[`${t}-tbody`]:{[`${t}-row`]:{[`&${t}-row-selected`]:{[`> ${t}-cell`]:{background:v,"&-row-hover":{background:u}}},[`> ${t}-cell-row-hover`]:{background:m}}}}}},gu=e=>{const{componentCls:t,tableExpandColumnWidth:r,calc:n}=e,o=(l,d,i,s)=>({[`${t}${t}-${l}`]:{fontSize:s,[`
        ${t}-title,
        ${t}-footer,
        ${t}-cell,
        ${t}-thead > tr > th,
        ${t}-tbody > tr > th,
        ${t}-tbody > tr > td,
        tfoot > tr > th,
        tfoot > tr > td
      `]:{padding:`${L(d)} ${L(i)}`},[`${t}-filter-trigger`]:{marginInlineEnd:L(n(i).div(2).mul(-1).equal())},[`${t}-expanded-row-fixed`]:{margin:`${L(n(d).mul(-1).equal())} ${L(n(i).mul(-1).equal())}`},[`${t}-tbody`]:{[`${t}-wrapper:only-child ${t}`]:{marginBlock:L(n(d).mul(-1).equal()),marginInline:`${L(n(r).sub(i).equal())} ${L(n(i).mul(-1).equal())}`}},[`${t}-selection-extra`]:{paddingInlineStart:L(n(i).div(4).equal())}}});return{[`${t}-wrapper`]:Object.assign(Object.assign({},o("middle",e.tablePaddingVerticalMiddle,e.tablePaddingHorizontalMiddle,e.tableFontSizeMiddle)),o("small",e.tablePaddingVerticalSmall,e.tablePaddingHorizontalSmall,e.tableFontSizeSmall))}},hu=e=>{const{componentCls:t,marginXXS:r,fontSizeIcon:n,headerIconColor:o,headerIconHoverColor:l}=e;return{[`${t}-wrapper`]:{[`${t}-thead th${t}-column-has-sorters`]:{outline:"none",cursor:"pointer",transition:`all ${e.motionDurationSlow}, left 0s`,"&:hover":{background:e.tableHeaderSortHoverBg,"&::before":{backgroundColor:"transparent !important"}},"&:focus-visible":{color:e.colorPrimary},[`
          &${t}-cell-fix-left:hover,
          &${t}-cell-fix-right:hover
        `]:{background:e.tableFixedHeaderSortActiveBg}},[`${t}-thead th${t}-column-sort`]:{background:e.tableHeaderSortBg,"&::before":{backgroundColor:"transparent !important"}},[`td${t}-column-sort`]:{background:e.tableBodySortBg},[`${t}-column-title`]:{position:"relative",zIndex:1,flex:1,minWidth:0},[`${t}-column-sorters`]:{display:"flex",flex:"auto",alignItems:"center",justifyContent:"space-between","&::after":{position:"absolute",inset:0,width:"100%",height:"100%",content:'""'}},[`${t}-column-sorters-tooltip-target-sorter`]:{"&::after":{content:"none"}},[`${t}-column-sorter`]:{marginInlineStart:r,color:o,fontSize:0,transition:`color ${e.motionDurationSlow}`,"&-inner":{display:"inline-flex",flexDirection:"column",alignItems:"center"},"&-up, &-down":{fontSize:n,"&.active":{color:e.colorPrimary}},[`${t}-column-sorter-up + ${t}-column-sorter-down`]:{marginTop:"-0.3em"}},[`${t}-column-sorters:hover ${t}-column-sorter`]:{color:l}}}},bu=e=>{const{componentCls:t,opacityLoading:r,tableScrollThumbBg:n,tableScrollThumbBgHover:o,tableScrollThumbSize:l,tableScrollBg:d,zIndexTableSticky:i,stickyScrollBarBorderRadius:s,lineWidth:c,lineType:v,tableBorderColor:u}=e,m=`${L(c)} ${v} ${u}`;return{[`${t}-wrapper`]:{[`${t}-sticky`]:{"&-holder":{position:"sticky",zIndex:i,background:e.colorBgContainer},"&-scroll":{position:"sticky",bottom:0,height:`${L(l)} !important`,zIndex:i,display:"flex",alignItems:"center",background:d,borderTop:m,opacity:r,"&:hover":{transformOrigin:"center bottom"},"&-bar":{height:l,backgroundColor:n,borderRadius:s,transition:`all ${e.motionDurationSlow}, transform 0s`,position:"absolute",bottom:0,"&:hover, &-active":{backgroundColor:o}}}}}}},Mo=e=>{const{componentCls:t,lineWidth:r,tableBorderColor:n,calc:o}=e,l=`${L(r)} ${e.lineType} ${n}`;return{[`${t}-wrapper`]:{[`${t}-summary`]:{position:"relative",zIndex:e.zIndexTableFixed,background:e.tableBg,"> tr":{"> th, > td":{borderBottom:l}}},[`div${t}-summary`]:{boxShadow:`0 ${L(o(r).mul(-1).equal())} 0 ${n}`}}}},yu=e=>{const{componentCls:t,motionDurationMid:r,lineWidth:n,lineType:o,tableBorderColor:l,calc:d}=e,i=`${L(n)} ${o} ${l}`,s=`${t}-expanded-row-cell`;return{[`${t}-wrapper`]:{[`${t}-tbody-virtual`]:{[`${t}-tbody-virtual-holder-inner`]:{[`
            & > ${t}-row, 
            & > div:not(${t}-row) > ${t}-row
          `]:{display:"flex",boxSizing:"border-box",width:"100%"}},[`${t}-cell`]:{borderBottom:i,transition:`background ${r}`},[`${t}-expanded-row`]:{[`${s}${s}-fixed`]:{position:"sticky",insetInlineStart:0,overflow:"hidden",width:`calc(var(--virtual-width) - ${L(n)})`,borderInlineEnd:"none"}}},[`${t}-bordered`]:{[`${t}-tbody-virtual`]:{"&:after":{content:'""',insetInline:0,bottom:0,borderBottom:i,position:"absolute"},[`${t}-cell`]:{borderInlineEnd:i,[`&${t}-cell-fix-right-first:before`]:{content:'""',position:"absolute",insetBlock:0,insetInlineStart:d(n).mul(-1).equal(),borderInlineStart:i}}},[`&${t}-virtual`]:{[`${t}-placeholder ${t}-cell`]:{borderInlineEnd:i,borderBottom:i}}}}}},Cu=e=>{const{componentCls:t,fontWeightStrong:r,tablePaddingVertical:n,tablePaddingHorizontal:o,tableExpandColumnWidth:l,lineWidth:d,lineType:i,tableBorderColor:s,tableFontSize:c,tableBg:v,tableRadius:u,tableHeaderTextColor:m,motionDurationMid:f,tableHeaderBg:p,tableHeaderCellSplitColor:b,tableFooterTextColor:g,tableFooterBg:h,calc:x}=e,C=`${L(d)} ${i} ${s}`;return{[`${t}-wrapper`]:Object.assign(Object.assign({clear:"both",maxWidth:"100%","--rc-virtual-list-scrollbar-bg":e.tableScrollBg},Bl()),{[t]:Object.assign(Object.assign({},Ct(e)),{fontSize:c,background:v,borderRadius:`${L(u)} ${L(u)} 0 0`,scrollbarColor:`${e.tableScrollThumbBg} ${e.tableScrollBg}`}),table:{width:"100%",textAlign:"start",borderRadius:`${L(u)} ${L(u)} 0 0`,borderCollapse:"separate",borderSpacing:0},[`
          ${t}-cell,
          ${t}-thead > tr > th,
          ${t}-tbody > tr > th,
          ${t}-tbody > tr > td,
          tfoot > tr > th,
          tfoot > tr > td
        `]:{position:"relative",padding:`${L(n)} ${L(o)}`,overflowWrap:"break-word"},[`${t}-title`]:{padding:`${L(n)} ${L(o)}`},[`${t}-thead`]:{"\n          > tr > th,\n          > tr > td\n        ":{position:"relative",color:m,fontWeight:r,textAlign:"start",background:p,borderBottom:C,transition:`background ${f} ease`,"&[colspan]:not([colspan='1'])":{textAlign:"center"},[`&:not(:last-child):not(${t}-selection-column):not(${t}-row-expand-icon-cell):not([colspan])::before`]:{position:"absolute",top:"50%",insetInlineEnd:0,width:1,height:"1.6em",backgroundColor:b,transform:"translateY(-50%)",transition:`background-color ${f}`,content:'""'}},"> tr:not(:last-child) > th[colspan]":{borderBottom:0}},[`${t}-tbody`]:{"> tr":{"> th, > td":{transition:`background ${f}, border-color ${f}`,borderBottom:C,[`
              > ${t}-wrapper:only-child,
              > ${t}-expanded-row-fixed > ${t}-wrapper:only-child
            `]:{[t]:{marginBlock:L(x(n).mul(-1).equal()),marginInline:`${L(x(l).sub(o).equal())}
                ${L(x(o).mul(-1).equal())}`,[`${t}-tbody > tr:last-child > td`]:{borderBottomWidth:0,"&:first-child, &:last-child":{borderRadius:0}}}}},"> th":{position:"relative",color:m,fontWeight:r,textAlign:"start",background:p,borderBottom:C,transition:`background ${f} ease`}}},[`${t}-footer`]:{padding:`${L(n)} ${L(o)}`,color:g,background:h}})}},xu=e=>{const{colorFillAlter:t,colorBgContainer:r,colorTextHeading:n,colorFillSecondary:o,colorFillContent:l,controlItemBgActive:d,controlItemBgActiveHover:i,padding:s,paddingSM:c,paddingXS:v,colorBorderSecondary:u,borderRadiusLG:m,controlHeight:f,colorTextPlaceholder:p,fontSize:b,fontSizeSM:g,lineHeight:h,lineWidth:x,colorIcon:C,colorIconHover:S,opacityLoading:w,controlInteractiveSize:E}=e,k=new vn(o).onBackground(r).toHexString(),R=new vn(l).onBackground(r).toHexString(),y=new vn(t).onBackground(r).toHexString(),P=new vn(C),I=new vn(S),T=E/2-x,O=T*2+x*3;return{headerBg:y,headerColor:n,headerSortActiveBg:k,headerSortHoverBg:R,bodySortBg:y,rowHoverBg:y,rowSelectedBg:d,rowSelectedHoverBg:i,rowExpandedBg:t,cellPaddingBlock:s,cellPaddingInline:s,cellPaddingBlockMD:c,cellPaddingInlineMD:v,cellPaddingBlockSM:v,cellPaddingInlineSM:v,borderColor:u,headerBorderRadius:m,footerBg:y,footerColor:n,cellFontSize:b,cellFontSizeMD:b,cellFontSizeSM:b,headerSplitColor:u,fixedHeaderSortActiveBg:k,headerFilterHoverBg:l,filterDropdownMenuBg:r,filterDropdownBg:r,expandIconBg:r,selectionColumnWidth:f,stickyScrollBarBg:p,stickyScrollBarBorderRadius:100,expandIconMarginTop:(b*h-x*3)/2-Math.ceil((g*1.4-x*3)/2),headerIconColor:P.clone().setA(P.a*w).toRgbString(),headerIconHoverColor:I.clone().setA(I.a*w).toRgbString(),expandIconHalfInner:T,expandIconSize:O,expandIconScale:E/O}},Bo=2,Su=en("Table",e=>{const{colorTextHeading:t,colorSplit:r,colorBgContainer:n,controlInteractiveSize:o,headerBg:l,headerColor:d,headerSortActiveBg:i,headerSortHoverBg:s,bodySortBg:c,rowHoverBg:v,rowSelectedBg:u,rowSelectedHoverBg:m,rowExpandedBg:f,cellPaddingBlock:p,cellPaddingInline:b,cellPaddingBlockMD:g,cellPaddingInlineMD:h,cellPaddingBlockSM:x,cellPaddingInlineSM:C,borderColor:S,footerBg:w,footerColor:E,headerBorderRadius:k,cellFontSize:R,cellFontSizeMD:y,cellFontSizeSM:P,headerSplitColor:I,fixedHeaderSortActiveBg:T,headerFilterHoverBg:O,filterDropdownBg:N,expandIconBg:$,selectionColumnWidth:K,stickyScrollBarBg:D,calc:j}=e,M=tn(e,{tableFontSize:R,tableBg:n,tableRadius:k,tablePaddingVertical:p,tablePaddingHorizontal:b,tablePaddingVerticalMiddle:g,tablePaddingHorizontalMiddle:h,tablePaddingVerticalSmall:x,tablePaddingHorizontalSmall:C,tableBorderColor:S,tableHeaderTextColor:d,tableHeaderBg:l,tableFooterTextColor:E,tableFooterBg:w,tableHeaderCellSplitColor:I,tableHeaderSortBg:i,tableHeaderSortHoverBg:s,tableBodySortBg:c,tableFixedHeaderSortActiveBg:T,tableHeaderFilterActiveBg:O,tableFilterDropdownBg:N,tableRowHoverBg:v,tableSelectedRowBg:u,tableSelectedRowHoverBg:m,zIndexTableFixed:Bo,zIndexTableSticky:j(Bo).add(1).equal({unit:!1}),tableFontSizeMiddle:y,tableFontSizeSmall:P,tableSelectionColumnWidth:K,tableExpandIconBg:$,tableExpandColumnWidth:j(o).add(j(e.padding).mul(2)).equal(),tableExpandedRowBg:f,tableFilterDropdownWidth:120,tableFilterDropdownHeight:264,tableFilterDropdownSearchWidth:140,tableScrollThumbSize:8,tableScrollThumbBg:D,tableScrollThumbBgHover:t,tableScrollBg:r});return[Cu(M),fu(M),Mo(M),hu(M),du(M),lu(M),vu(M),cu(M),Mo(M),su(M),pu(M),uu(M),bu(M),iu(M),gu(M),mu(M),yu(M)]},xu,{unitless:{expandIconScale:!0}}),wu=[],$u=(e,t)=>{var r,n;const{prefixCls:o,className:l,rootClassName:d,style:i,size:s,bordered:c,dropdownPrefixCls:v,dataSource:u,pagination:m,rowSelection:f,rowKey:p="key",rowClassName:b,columns:g,children:h,childrenColumnName:x,onChange:C,getPopupContainer:S,loading:w,expandIcon:E,expandable:k,expandedRowRender:R,expandIconColumnIndex:y,indentSize:P,scroll:I,sortDirections:T,locale:O,showSorterTooltip:N={target:"full-header"},virtual:$}=e;hr();const K=a.useMemo(()=>g||wr(h),[g,h]),D=a.useMemo(()=>K.some(me=>me.responsive),[K]),j=Vo(D),M=a.useMemo(()=>{const me=new Set(Object.keys(j).filter(be=>j[be]));return K.filter(be=>!be.responsive||be.responsive.some(Te=>me.has(Te)))},[K,j]),q=dr(e,["className","style","columns"]),{locale:X=zl,direction:U,table:ee,renderEmpty:se,getPrefixCls:ge,getPopupContainer:ce}=a.useContext(St),Z=cr(s),te=Object.assign(Object.assign({},X.Table),O),pe=u||wu,ie=ge("table",o),W=ge("dropdown",v),[,V]=fr(),F=Cn(ie),[Q,oe,Y]=Su(ie,F),ae=Object.assign(Object.assign({childrenColumnName:x,expandIconColumnIndex:y},k),{expandIcon:(r=k==null?void 0:k.expandIcon)!==null&&r!==void 0?r:(n=ee==null?void 0:ee.expandable)===null||n===void 0?void 0:n.expandIcon}),{childrenColumnName:Oe="children"}=ae,Ae=a.useMemo(()=>pe.some(me=>me==null?void 0:me[Oe])?"nest":R||k!=null&&k.expandedRowRender?"row":null,[pe]),Ee={body:a.useRef(null)},B=bd(ie),H=a.useRef(null),de=a.useRef(null);gd(t,()=>Object.assign(Object.assign({},de.current),{nativeElement:H.current}));const ve=a.useMemo(()=>typeof p=="function"?p:me=>me==null?void 0:me[p],[p]),[fe]=Yd(pe,Oe,ve),Pe={},je=(me,be,Te=!1)=>{var ze,Je,it,st;const Xe=Object.assign(Object.assign({},Pe),me);Te&&((ze=Pe.resetPagination)===null||ze===void 0||ze.call(Pe),!((Je=Xe.pagination)===null||Je===void 0)&&Je.current&&(Xe.pagination.current=1),m&&((it=m.onChange)===null||it===void 0||it.call(m,1,(st=Xe.pagination)===null||st===void 0?void 0:st.pageSize))),I&&I.scrollToFirstRowOnChange!==!1&&Ee.body.current&&Gi(0,{getContainer:()=>Ee.body.current}),C==null||C(Xe.pagination,Xe.filters,Xe.sorter,{currentDataSource:lr(sr(pe,Xe.sorterStates,Oe),Xe.filterStates,Oe),action:be})},Fe=(me,be)=>{je({sorter:me,sorterStates:be},"sort",!1)},[xe,ye,re,_]=nu({prefixCls:ie,mergedColumns:M,onSorterChange:Fe,sortDirections:T||["ascend","descend"],tableLocale:te,showSorterTooltip:N}),Ie=a.useMemo(()=>sr(pe,ye,Oe),[pe,ye]);Pe.sorter=_(),Pe.sorterStates=ye;const Ke=(me,be)=>{je({filters:me,filterStates:be},"filter",!0)},[he,Be,Ne]=Jd({prefixCls:ie,locale:te,dropdownPrefixCls:W,mergedColumns:M,onFilterChange:Ke,getPopupContainer:S||ce,rootClassName:G(d,F)}),_e=lr(Ie,Be,Oe);Pe.filters=Ne,Pe.filterStates=Be;const We=a.useMemo(()=>{const me={};return Object.keys(Ne).forEach(be=>{Ne[be]!==null&&(me[be]=Ne[be])}),Object.assign(Object.assign({},re),{filters:me})},[re,Ne]),[Ye]=ru(We),gt=(me,be)=>{je({pagination:Object.assign(Object.assign({},Pe.pagination),{current:me,pageSize:be})},"paginate")},[De,Ot]=eu(_e.length,gt,m);Pe.pagination=m===!1?{}:Zd(De,m),Pe.resetPagination=Ot;const It=a.useMemo(()=>{if(m===!1||!De.pageSize)return _e;const{current:me=1,total:be,pageSize:Te=Wa}=De;return _e.length<be?_e.length>Te?_e.slice((me-1)*Te,me*Te):_e:_e.slice((me-1)*Te,me*Te)},[!!m,_e,De==null?void 0:De.current,De==null?void 0:De.pageSize,De==null?void 0:De.total]),[Qe,Ge]=md({prefixCls:ie,data:_e,pageData:It,getRowKey:ve,getRecordByKey:fe,expandType:Ae,childrenColumnName:Oe,locale:te,getPopupContainer:S||ce},f),at=(me,be,Te)=>{let ze;return typeof b=="function"?ze=G(b(me,be,Te)):ze=G(b),G({[`${ie}-row-selected`]:Ge.has(ve(me,be))},ze)};ae.__PARENT_RENDER_ICON__=ae.expandIcon,ae.expandIcon=ae.expandIcon||E||hd(te),Ae==="nest"&&ae.expandIconColumnIndex===void 0?ae.expandIconColumnIndex=f?1:0:ae.expandIconColumnIndex>0&&f&&(ae.expandIconColumnIndex-=1),typeof ae.indentSize!="number"&&(ae.indentSize=typeof P=="number"?P:15);const Ze=a.useCallback(me=>Ye(Qe(he(xe(me)))),[xe,he,Qe]);let Ue,et;if(m!==!1&&(De!=null&&De.total)){let me;De.size?me=De.size:me=Z==="small"||Z==="middle"?"small":void 0;const be=Je=>a.createElement(Gs,Object.assign({},De,{className:G(`${ie}-pagination ${ie}-pagination-${Je}`,De.className),size:me})),Te=U==="rtl"?"left":"right",{position:ze}=De;if(ze!==null&&Array.isArray(ze)){const Je=ze.find(Xe=>Xe.includes("top")),it=ze.find(Xe=>Xe.includes("bottom")),st=ze.every(Xe=>`${Xe}`=="none");!Je&&!it&&!st&&(et=be(Te)),Je&&(Ue=be(Je.toLowerCase().replace("top",""))),it&&(et=be(it.toLowerCase().replace("bottom","")))}else et=be(Te)}let Me;typeof w=="boolean"?Me={spinning:w}:typeof w=="object"&&(Me=Object.assign({spinning:!0},w));const Le=G(Y,F,`${ie}-wrapper`,ee==null?void 0:ee.className,{[`${ie}-wrapper-rtl`]:U==="rtl"},l,d,oe),lt=Object.assign(Object.assign({},ee==null?void 0:ee.style),i),ln=typeof(O==null?void 0:O.emptyText)<"u"?O.emptyText:(se==null?void 0:se("Table"))||a.createElement(jl,{componentName:"Table"}),sn=$?au:ou,wt={},cn=a.useMemo(()=>{const{fontSize:me,lineHeight:be,lineWidth:Te,padding:ze,paddingXS:Je,paddingSM:it}=V,st=Math.floor(me*be);switch(Z){case"middle":return it*2+st+Te;case"small":return Je*2+st+Te;default:return ze*2+st+Te}},[V,Z]);return $&&(wt.listItemHeight=cn),Q(a.createElement("div",{ref:H,className:Le,style:lt},a.createElement(_l,Object.assign({spinning:!1},Me),Ue,a.createElement(sn,Object.assign({},wt,q,{ref:de,columns:M,direction:U,expandable:ae,prefixCls:ie,className:G({[`${ie}-middle`]:Z==="middle",[`${ie}-small`]:Z==="small",[`${ie}-bordered`]:c,[`${ie}-empty`]:pe.length===0},Y,F,oe),data:It,rowKey:ve,rowClassName:at,emptyText:ln,internalHooks:wn,internalRefs:Ee,transformColumns:Ze,getContainerWidth:B})),et)))},Eu=a.forwardRef($u),Nu=(e,t)=>{const r=a.useRef(0);return r.current+=1,a.createElement(Eu,Object.assign({},e,{ref:t,_renderTimes:r.current}))},Rt=a.forwardRef(Nu);Rt.SELECTION_COLUMN=Kt;Rt.EXPAND_COLUMN=Et;Rt.SELECTION_ALL=tr;Rt.SELECTION_INVERT=nr;Rt.SELECTION_NONE=rr;Rt.Column=rd;Rt.ColumnGroup=od;Rt.Summary=wa;const ku=({isStraightLine:e,showMinimap:t,onToggleLine:r,onSave:n,onLoad:o,onAutoLayout:l,onToggleMinimap:d,onAddNode:i,onUndo:s,onRedo:c})=>ne.jsxs(Ll,{style:{left:"50%",transform:"translateX(-50%)"},children:[ne.jsx(Tt,{onClick:r,title:e?"Switch to curved line":"Switch to straight line",children:e?"—":"~"}),ne.jsx(Tt,{onClick:n,title:"Save flowchart",children:ne.jsx(Ai,{})}),ne.jsx(Tt,{onClick:o,title:"Load flowchart",children:ne.jsx(Qo,{})}),ne.jsx(Tt,{onClick:l,title:"Auto Layout",children:ne.jsx(_i,{})}),ne.jsx(Tt,{onClick:s,title:"Undo",children:ne.jsx(Vi,{})}),ne.jsx(Tt,{onClick:c,title:"Redo",children:ne.jsx(Bi,{})}),ne.jsx(Tt,{onClick:d,title:t?"Hide minimap":"Show minimap",children:ne.jsx($i,{})}),ne.jsx(Tt,{onClick:i,title:"Add Node",children:ne.jsx(Uo,{})})]}),Ru=(e,t)=>{const r=()=>Math.random()*50-25,n=e.find(s=>s.data.nodeType==="start"),o=e.find(s=>s.data.nodeType==="end");if(!n||!o){console.error("Start node or end node not found, unable to add a new node");return}const l=o.position.x-r(),d=n.position.y+150+r(),i={id:Date.now().toString(),type:"customNode",data:{label:`Custom Node ${e.length+1}`,content:`This is custom node #${e.length+1}`,input:[],ouput:[]},position:{x:l,y:d},style:{background:"#FADDDB",border:"2px solid #E6A5AD",color:"#d58690"},sourcePosition:Dt.Right,targetPosition:Dt.Left};t([...e.filter(s=>s.id!==o.id),i,o])},Ou=(e,t,r)=>{t(e.filter(n=>n.id!==r))},{Option:Gn}=vr,Iu=({node:e,onUpdate:t})=>{const[r,n]=J.useState(typeof e.data.content=="string"?e.data.content:""),[o,l]=J.useState(e.data.input||[]);J.useEffect(()=>{n(typeof e.data.content=="string"?e.data.content:""),l(e.data.input||[])},[e.data.content,e.data.input]);const d=a.useCallback(s=>{t({...e,data:{...e.data,...s}})},[e,t]),i=a.useCallback((s,c,v)=>{const u=[...o];u[s][c]=v,l(u),d({input:u})},[o,d]);return ne.jsxs(ne.Fragment,{children:[ne.jsx("div",{children:r}),ne.jsx(En,{defaultActiveKey:["input"],bordered:!1,className:"node-editor-collapse",children:ne.jsx(En.Panel,{header:"输入",extra:ne.jsx(Rn,{className:"node-editor-collapse-btn",icon:ne.jsx(Uo,{}),onClick:s=>{s.stopPropagation();const c=[...o,{id:`input-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,label:"",type:"string",defaultValue:""}];l(c),d({input:c})}}),children:ne.jsx(Rt,{dataSource:o,rowKey:s=>s.id,pagination:!1,columns:a.useMemo(()=>[{title:"变量名",dataIndex:"label",render:(s,c,v)=>ne.jsx(Hn,{value:s,onChange:u=>i(v,"label",u.target.value),placeholder:"Variable name"},v)},{title:"变量值",dataIndex:"type",render:(s,c,v)=>ne.jsxs(vr,{value:s,style:{width:"100%"},onChange:u=>i(v,"type",u),children:[ne.jsx(Gn,{value:"string",children:"String"}),ne.jsx(Gn,{value:"number",children:"Number"}),ne.jsx(Gn,{value:"boolean",children:"Boolean"})]})},{title:"",dataIndex:"defaultValue",render:(s,c,v)=>ne.jsx(Hn,{value:s,onChange:u=>i(v,"defaultValue",u.target.value),placeholder:"Default value"})},{title:"",render:(s,c,v)=>ne.jsx(Rn,{danger:!0,onClick:()=>{const u=o.filter((m,f)=>f!==v);l(u),d({input:u})},children:"Delete"})}],[i,o,d])})},"input")}),ne.jsx(En,{defaultActiveKey:["output"],bordered:!1,className:"node-editor-collapse",children:ne.jsx(En.Panel,{header:"输出",children:ne.jsx(Hn.TextArea,{value:r||"",onChange:s=>{const c=s.target.value;n(c),d({content:c})},placeholder:"Enter node content",autoSize:{minRows:3,maxRows:10}})},"output")})]})},Pu=({id:e,data:t})=>{const{label:r,content:n,input:o,output:l}=t,d=Hl(),i=Jo(),{setNodes:s}=i,[c,v]=a.useState(!1),[u,m]=a.useState({}),[f,p]=a.useState({content:typeof n=="string"?n:"",input:o||[]});J.useEffect(()=>{p({content:typeof n=="string"?n:"",input:o||[]})},[n,o]);const b=x=>{x.stopPropagation(),v(!0)},g=x=>{"stopPropagation"in x&&x.stopPropagation(),Object.keys(u).length>0&&s(C=>C.map(S=>S.id===e?{...S,data:{...S.data,...u}}:S)),v(!1)},h=(x,C)=>ne.jsxs("div",{className:"custom-node-io",children:[ne.jsxs("span",{children:[x,"："]}),C==null?void 0:C.map(S=>ne.jsxs(Vl,{children:[S.type,".",ne.jsx("strong",{children:S.label})]},S.label))]});return ne.jsxs("div",{className:"custom-node",onClick:b,children:[ne.jsx("div",{className:"custom-node-header",children:ne.jsxs("div",{style:{display:"flex",justifyContent:"space-between",width:"100%"},children:[ne.jsx("span",{children:r}),t.nodeType!=="start"&&t.nodeType!=="end"&&ne.jsx(br,{menu:{items:[{key:"delete",label:"删除",icon:ne.jsx(Fl,{}),onClick:x=>{x.domEvent.stopPropagation(),Ou(d,s,e)}},{key:"duplicate",label:"创建副本",icon:ne.jsx(Wl,{}),onClick:x=>{x.domEvent.stopPropagation(),alert("暂不支持")}}]},trigger:["click"],children:ne.jsx(Al,{style:{cursor:"pointer"},onClick:x=>x.stopPropagation()})})]})}),ne.jsx("div",{className:"custom-node-body",children:ne.jsxs("div",{className:"custom-node-content",children:[ne.jsx("div",{children:f.content||"Custom Node Content"}),t.nodeType!=="end"&&h("输入",o),t.nodeType!=="start"&&h("输出",l)]})}),t.nodeType!=="start"&&ne.jsx(Yr,{type:"target",position:Dt.Left}),t.nodeType!=="end"&&ne.jsx(Yr,{type:"source",position:Dt.Right}),ne.jsx(ha,{title:r,placement:"right",closable:!0,maskClosable:!0,onClose:g,open:c,width:500,keyboard:!0,children:ne.jsx(Iu,{node:{id:e,position:{x:0,y:0},data:{...t,...f}},onUpdate:x=>{m(C=>({...C,...x.data}))},onClose:()=>g({stopPropagation:()=>{}})})})]})},Tu=(e,t)=>{const r=JSON.stringify({nodes:e,edges:t});localStorage.setItem("flow-data",r),Jn.success("The flowchart layout has been saved!")},Ku=(e,t)=>{const r=localStorage.getItem("flow-data");if(r){const{nodes:n,edges:o}=JSON.parse(r);e(n),t(o),Jn.success("The flowchart layout has been loaded!")}else Jn.info("No saved flowchart layout!")},Du=[{id:"1",type:"customNode",data:{label:"Start Node",nodeType:"start",content:"开始节点，用于设定工作流启动变量",input:[]},position:{x:0,y:0},style:{background:"#E8F8F5",border:"2px solid #1ABC9C",color:"#16A085"},sourcePosition:Dt.Right,targetPosition:Dt.Left},{id:"2",type:"customNode",data:{label:"End Node",nodeType:"end",content:"结束节点，用于返回工作流运行结果",output:[]},position:{x:400,y:0},style:{background:"#FEF9E7",border:"2px solid #F7DC6F",color:"#D4AC0D"},sourcePosition:Dt.Right,targetPosition:Dt.Left}],Mu=[],Bu=(e,t,r)=>{const n={id:`${t}-${r}-${Date.now()}`,source:t,target:r};return[...e,n]},zu=(e,t)=>e.filter(r=>r.id!==t),ju=(e,t)=>e.map(r=>({...r,type:t?"straight":"default",markerEnd:{type:ql.ArrowClosed},style:{...r.style,strokeWidth:2,...t?{stroke:"#b1b1b7",strokeDasharray:"0"}:{}}})),_u=(e,t,r,n)=>{const o=new Qr.graphlib.Graph;o.setDefaultEdgeLabel(()=>({}));const l=200,d=100;o.setGraph({rankdir:"LR",nodesep:50,ranksep:100}),e.forEach(s=>{o.setNode(s.id,{width:l,height:d})}),t.forEach(s=>{o.setEdge(s.source,s.target)}),Qr.layout(o);const i=e.map(s=>{const c=o.node(s.id);return{...s,position:{x:c.x,y:c.y}}});r(i),console.log("auto：",i),console.log("edges:",t),setTimeout(()=>{n.fitView()},0)};let yt=[],ct=-1,Pn=!1;const zo=(e,t)=>{yt=[{nodes:JSON.parse(JSON.stringify(e)),edges:JSON.parse(JSON.stringify(t))}],ct=0},Lu=(e,t)=>{if(console.log("addHistory添加记录"),Pn){Pn=!1;return}const r={nodes:JSON.parse(JSON.stringify(e)),edges:JSON.parse(JSON.stringify(t))},n=ct>=0?yt[ct]:null;if(n&&n.nodes.length===r.nodes.length&&n.edges.length===r.edges.length&&JSON.stringify(n.nodes)===JSON.stringify(r.nodes)&&JSON.stringify(n.edges)===JSON.stringify(r.edges)){console.log("[History] 状态未变化，跳过保存");return}const o=yt.length-(ct+1);yt.splice(ct+1),yt.push(r),ct=yt.length-1,console.log(`[History] 新增，currentIndex=${ct}, 节点数=${e.length}, 边数=${t.length}, 移除记录=${o}, 调用栈:`)},Hu=()=>{if(ct<=0)return null;Pn=!0;const e=ct-1,t=yt[e];return ct=e,{nodes:[...t.nodes],edges:[...t.edges]}},Au=()=>ct>=yt.length-1?null:(Pn=!0,ct++,yt[ct]),Fu=()=>ct<0?null:yt[ct],Wu={customNode:Pu};function Vu(){const[e,t,r]=Gl(Du),[n,o,l]=Ul(Mu),[d,i]=a.useState(!1),[s,c]=a.useState(!1);a.useEffect(()=>{zo(e,n)},[]);const v=Jo(),u=a.useCallback(()=>{Ru(e,S=>{t(S)})},[e]),m=a.useCallback(S=>{o(Bu(n,S.source,S.target))},[n]),f=a.useCallback(()=>{_u(e,n,t,v)},[e,n,t,v]),p=a.useCallback(()=>{Tu(e,n)},[e,n]),b=a.useCallback(()=>{Ku(t,o),setTimeout(()=>{zo(e,n)},0)},[t,o,e,n]),g=a.useCallback(S=>{o(zu(n,S))},[n,o]),h=a.useRef([]),x=a.useRef([]);a.useEffect(()=>{const S=JSON.stringify(h.current)!==JSON.stringify(e),w=JSON.stringify(x.current)!==JSON.stringify(n);if(S||w){const E=Fu();(e.length>0||n.length>0)&&(!E||JSON.stringify(E.nodes)!==JSON.stringify(e)||JSON.stringify(E.edges)!==JSON.stringify(n))&&Lu(e,n),h.current=e,x.current=n}},[e,n]);const C=ju(n,s).map(S=>({...S,label:S.label&&J.cloneElement(S.label,{onClick:w=>{var E,k,R;(R=(k=(E=S.label)==null?void 0:E.props)==null?void 0:k.onClick)==null||R.call(k,w),g(S.id)}})}));return ne.jsx("div",{style:{width:"100%",height:"100vh"},children:ne.jsxs(Jl,{nodes:e,edges:C,onNodesChange:r,onEdgesChange:l,onConnect:m,fitView:!0,nodesDraggable:!0,edgesFocusable:!0,panOnScroll:!0,nodeTypes:Wu,children:[ne.jsx(Yl,{}),d&&ne.jsx(Ql,{}),ne.jsx(ku,{isStraightLine:s,showMinimap:d,onToggleLine:()=>c(!s),onSave:p,onLoad:b,onAutoLayout:f,onToggleMinimap:()=>i(!d),onAddNode:u,onUndo:()=>{const S=Hu();S&&(t(()=>S.nodes),o(()=>S.edges))},onRedo:()=>{const S=Au();S&&(t(S.nodes),o(S.edges))}})]})})}function Gu(){return ne.jsx(Xl,{children:ne.jsx(Vu,{})})}export{Gu as default};
