# GenAI semconv attribute names
import os

GEN_AI_SYSTEM = "gen_ai.system"
GEN_AI_REQUEST_MODEL = "gen_ai.request.model"
GEN_AI_REQUEST_FREQUENCY_PENALTY = "gen_ai.request.frequency_penalty"
GEN_AI_REQUEST_MAX_TOKENS = "gen_ai.request.max_tokens"
GEN_AI_REQUEST_PRESENCE_PENALTY = "gen_ai.request.presence_penalty"
GEN_AI_REQUEST_STOP_SEQUENCES = "gen_ai.request.stop_sequences"
GEN_AI_REQUEST_TEMPERATURE = "gen_ai.request.temperature"
GEN_AI_REQUEST_TOP_K = "gen_ai.request.top_k"
GEN_AI_REQUEST_TOP_P = "gen_ai.request.top_p"
GEN_AI_REQUEST_STREAMING = "gen_ai.request.streaming"
GEN_AI_REQUEST_USER = "gen_ai.request.user"
GEN_AI_REQUEST_EXTRA_HEADERS = "gen_ai.request.extra_headers"
GEN_AI_PROMPT = "gen_ai.prompt"
GEN_AI_PROMPT_TOOLS = "gen_ai.prompt.tools"
GEN_AI_COMPLETION = "gen_ai.completion"
GEN_AI_COMPLETION_TOOL_CALLS = "gen_ai.completion.tool_calls"
GEN_AI_COMPLETION_CONTENT = "gen_ai.completion.content"
GEN_AI_DURATION = "gen_ai.duration"
GEN_AI_FIRST_TOKEN_DURATION = "gen_ai.first_token_duration"
GEN_AI_RESPONSE_FINISH_REASONS = "gen_ai.response.finish_reasons"
GEN_AI_RESPONSE_ID = "gen_ai.response.id"
GEN_AI_RESPONSE_MODEL = "gen_ai.response.model"
GEN_AI_USAGE_INPUT_TOKENS = "gen_ai.usage.input_tokens"
GEN_AI_USAGE_OUTPUT_TOKENS = "gen_ai.usage.output_tokens"
GEN_AI_USAGE_TOTAL_TOKENS = "gen_ai.usage.total_tokens"
GEN_AI_OPERATION_NAME = "gen_ai.operation.name"
GEN_AI_METHOD_NAME = "gen_ai.method.name"
GEN_AI_SERVER_ADDRESS = "gen_ai.server.address"

ATTRIBUTE_NAME_SPACE = os.getenv("ATTRIBUTE_NAME_SPACE", "aworld.")
AGENT_ID = ATTRIBUTE_NAME_SPACE + "agent.id"
AGENT_NAME = ATTRIBUTE_NAME_SPACE + "agent.name"
AGENT_RUN_SUCCESS = ATTRIBUTE_NAME_SPACE + "agent.run.success"
AGENT_USAGE_TYPE = ATTRIBUTE_NAME_SPACE + "agent.usage_type"
TOOL_NAME = ATTRIBUTE_NAME_SPACE + "tool.name"
TOOL_STEP_SUCCESS = ATTRIBUTE_NAME_SPACE + "tool.step.success"
TASK = ATTRIBUTE_NAME_SPACE + "task"
TASK_ID = ATTRIBUTE_NAME_SPACE + "task.id"
TASK_INPUT = ATTRIBUTE_NAME_SPACE + "task.input"
TASK_IS_SUB_TASK = ATTRIBUTE_NAME_SPACE + "task.is_sub_task"
TASK_GROUP_ID = ATTRIBUTE_NAME_SPACE + "task.group_id"
SESSION_ID = ATTRIBUTE_NAME_SPACE + "session.id"
USER_ID = ATTRIBUTE_NAME_SPACE + "user.id"
