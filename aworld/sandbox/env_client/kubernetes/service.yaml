apiVersion: v1
kind: Service
metadata:
  name: mcp-openapi-service-3 #TODO: to specify your service name
  labels:
    app: mcp-openapi-service-3
spec:
  selector:
    name: mcp-openapi
    #name: mcp-openapi-node-1 #TODO: change label selector to match your backend pod
  ports:
  - protocol: TCP
    name: http
    port: 80 #TODO: choose an unique port on each node to avoid port conflict
    targetPort: 9090
  type: LoadBalancer
  #type: ClusterIP
#  type: LoadBalancer