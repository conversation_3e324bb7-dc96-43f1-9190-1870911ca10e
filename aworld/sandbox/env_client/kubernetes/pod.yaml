apiVersion: v1
kind: Pod
metadata:
  labels:
    name: mcp-openapi-node-5
  name: mcp-openapi-node-5
spec:
#  serviceAccountName: user1  # specify specific sevice account for pod creation
#    automountServiceAccountToken: true # mount token for api access inside pod/container
  imagePullSecrets:          #Comment out to enable specific image pull secret
    - name: mcp-openapi    # repleace it to specific registry key
  containers:
    - image: crpi-5emlza767l7em5xz-vpc.ap-southeast-1.personal.cr.aliyuncs.com/aworld_x/mcp-openapi:0.1.5
      imagePullPolicy: IfNotPresent
      name: mcp-openapi-node-5
      ports:
        - containerPort: 9090
          protocol: TCP
      resources: {}
      securityContext:
        capabilities: {}
        privileged: false
      terminationMessagePath: /dev/termination-log
  dnsPolicy: ClusterFirst
  restartPolicy: Always
#  nodeSelector:
#    env: test-team
